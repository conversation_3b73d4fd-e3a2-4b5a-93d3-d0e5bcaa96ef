{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "file": "utils.js", "sources": ["file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/shared/src/utils.ts"], "sourcesContent": ["import { CamelToPascal } from './utility-types';\n\n/**\n * Converts string to kebab case\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\n/**\n * Converts string to camel case\n *\n * @param {string} string\n * @returns {string} A camelized string\n */\nexport const toCamelCase = <T extends string>(string: T) =>\n  string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) =>\n    p2 ? p2.toUpperCase() : p1.toLowerCase(),\n  );\n\n/**\n * Converts string to pascal case\n *\n * @param {string} string\n * @returns {string} A pascalized string\n */\nexport const toPascalCase = <T extends string>(string: T): CamelToPascal<T> => {\n  const camelCase = toCamelCase(string);\n\n  return (camelCase.charAt(0).toUpperCase() + camelCase.slice(1)) as CamelToPascal<T>;\n};\n\n/**\n * Merges classes into a single string\n *\n * @param {array} classes\n * @returns {string} A string of classes\n */\nexport const mergeClasses = <ClassType = string | undefined | null>(...classes: ClassType[]) =>\n  classes\n    .filter((className, index, array) => {\n      return (\n        Boolean(className) &&\n        (className as string).trim() !== '' &&\n        array.indexOf(className) === index\n      );\n    })\n    .join(' ')\n    .trim();\n\n/**\n * Check if a component has an accessibility prop\n *\n * @param {object} props\n * @returns {boolean} Whether the component has an accessibility prop\n */\nexport const hasA11yProp = (props: Record<string, any>) => {\n  for (const prop in props) {\n    if (prop.startsWith('aria-') || prop === 'role' || prop === 'title') {\n      return true;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAC1B,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,OAAA,CAAQ,CAAsB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAQ/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,CAAA,CAAmB,MAAA,CAC5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAyB,CAAC,OAAO,CAAI,CAAA,CAAA,CAAA,EAAA,CAClD,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAS9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAmB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAA,CAAA,CAAA,CAAA,CAAA;IACvE,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,YAAY,MAAM,CAAA;IAE5B,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,MAAA,CAAO,CAAC,CAAA,CAAE,WAAA,EAAgB,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,KAAA,CAAM,CAAC,CAAA;AAC/D,CAAA;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,CAAA,CAAA,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACrE,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACG,MAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,OAAO,KAAU,CAAA,CAAA,CAAA,CAAA,CAAA;QAEjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,CAChB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAW,CACjC,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAEjC,CAAC,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAG,CAAA,CAAA,CAAA,CACR,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA;AAQG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA;IACzD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,QAAQ,KAAO,CAAA;QACxB,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,OAAS,CAAA,CAAA;YAC5D,OAAA,CAAA,CAAA,CAAA,CAAA;QAAA;IACT;AAEJ,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "file": "defaultAttributes.js", "sources": ["file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/lucide-react/src/defaultAttributes.ts"], "sourcesContent": ["export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACT,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACf,cAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAClB,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "file": "Icon.js", "sources": ["file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/lucide-react/src/Icon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses, hasA11yProp } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) =>\n    createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...(!children && !hasA11yProp(rest) && { 'aria-hidden': 'true' }),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    ),\n);\n\nexport default Icon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAwBA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,qKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACX,CACE,EACE,CAAA,CAAA,CAAA,CAAA,CAAQ,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACR,CAAA,CAAA,CAAA,CAAO,GAAA,CAAA,CAAA,EACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,GAAA,CAAA,EACd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,GAAA,CAAA,CAAA,EACZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,EAEL,CAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uKAAA,EACE,CAAA,CAAA,CAAA,CAAA,CAAA,EACA;QACE,CAAA,CAAA,CAAA;QACA,0KAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACH,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA;QACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACR,WAAA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAM,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAI,CAAA,CAAA,CAAA,CAAI,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAC/E,SAAA,CAAW,iLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,AAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,CAAA;QAC3C,CAAI,CAAA,CAAA,CAAC,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iLAAC,cAAA,EAAY,CAAI,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,MAAO;QAAA,CAAA;QAC/D,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACL,CAAA,EACA;WACK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAI,CAAC,CAAC,CAAK,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAM,qKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAc,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAA;WACvD,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAA;YAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;SAAA;KAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "file": "createLucideIcon.js", "sources": ["file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/lucide-react/src/createLucideIcon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from '@lucide/shared';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(({ className, ...props }, ref) =>\n    createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className,\n      ),\n      ...props,\n    }),\n  );\n\n  Component.displayName = toPascalCase(iconName);\n\n  return Component;\n};\n\nexport default createLucideIcon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAWM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,CAAmB,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,QAAuB,CAAA,CAAA,CAAA,CAAA,CAAA;IACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,mKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAuC,CAAC,CAAA,CAAE,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iKACjF,gBAAA,4JAAc,UAAM,CAAA,CAAA;YAClB,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,kLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACT,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,kLAAY,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAC,CAAA,CAAA,EAC7C,CAAA,OAAA,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,EAClB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAEF,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACJ,CAAA;IAGO,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,mLAAc,eAAA,EAAa,QAAQ,CAAA;IAEtC,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACT,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 362, "column": 0}, "map": {"version": 3, "file": "search.js", "sources": ["file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/lucide-react/src/icons/search.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm21 21-4.34-4.34', key: '14j7rj' }],\n  ['circle', { cx: '11', cy: '11', r: '8', key: '4ej97u' }],\n];\n\n/**\n * @component @name Search\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEgMjEtNC4zNC00LjM0IiAvPgogIDxjaXJjbGUgY3g9IjExIiBjeT0iMTEiIHI9IjgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/search\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Search = createLucideIcon('search', __iconNode);\n\nexport default Search;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAoB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjD;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "file": "loader-circle.js", "sources": ["file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/lucide-react/src/icons/loader-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'M21 12a9 9 0 1 1-6.219-8.56', key: '13zald' }]];\n\n/**\n * @component @name LoaderCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTJhOSA5IDAgMSAxLTYuMjE5LTguNTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/loader-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LoaderCircle = createLucideIcon('loader-circle', __iconNode);\n\nexport default LoaderCircle;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,6BAA+B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;QAAA,CAAC;KAAC;CAAA;AAa5F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 449, "column": 0}, "map": {"version": 3, "file": "external-link.js", "sources": ["file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/lucide-react/src/icons/external-link.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 3h6v6', key: '1q9fwt' }],\n  ['path', { d: 'M10 14 21 3', key: 'gplh6r' }],\n  ['path', { d: 'M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6', key: 'a6xqqp' }],\n];\n\n/**\n * @component @name ExternalLink\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgM2g2djYiIC8+CiAgPHBhdGggZD0iTTEwIDE0IDIxIDMiIC8+CiAgPHBhdGggZD0iTTE4IDEzdjZhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJWOGEyIDIgMCAwIDEgMi0yaDYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/external-link\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ExternalLink = createLucideIcon('external-link', __iconNode);\n\nexport default ExternalLink;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4D,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3F;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 502, "column": 0}, "map": {"version": 3, "file": "moon.js", "sources": ["file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/lucide-react/src/icons/moon.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z', key: 'a7tn18' }],\n];\n\n/**\n * @component @name Moon\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgM2E2IDYgMCAwIDAgOSA5IDkgOSAwIDEgMS05LTlaIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/moon\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Moon = createLucideIcon('moon', __iconNode);\n\nexport default Moon;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACrE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 541, "column": 0}, "map": {"version": 3, "file": "sun.js", "sources": ["file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/lucide-react/src/icons/sun.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '4', key: '4exip2' }],\n  ['path', { d: 'M12 2v2', key: 'tus03m' }],\n  ['path', { d: 'M12 20v2', key: '1lh1kg' }],\n  ['path', { d: 'm4.93 4.93 1.41 1.41', key: '149t6j' }],\n  ['path', { d: 'm17.66 17.66 1.41 1.41', key: 'ptbguv' }],\n  ['path', { d: 'M2 12h2', key: '1t8f8n' }],\n  ['path', { d: 'M20 12h2', key: '1q8mjw' }],\n  ['path', { d: 'm6.34 17.66-1.41 1.41', key: '1m8zz5' }],\n  ['path', { d: 'm19.07 4.93-1.41 1.41', key: '1shlcs' }],\n];\n\n/**\n * @component @name Sun\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSI0IiAvPgogIDxwYXRoIGQ9Ik0xMiAydjIiIC8+CiAgPHBhdGggZD0iTTEyIDIwdjIiIC8+CiAgPHBhdGggZD0ibTQuOTMgNC45MyAxLjQxIDEuNDEiIC8+CiAgPHBhdGggZD0ibTE3LjY2IDE3LjY2IDEuNDEgMS40MSIgLz4KICA8cGF0aCBkPSJNMiAxMmgyIiAvPgogIDxwYXRoIGQ9Ik0yMCAxMmgyIiAvPgogIDxwYXRoIGQ9Im02LjM0IDE3LjY2LTEuNDEgMS40MSIgLz4KICA8cGF0aCBkPSJtMTkuMDcgNC45My0xLjQxIDEuNDEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/sun\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Sun = createLucideIcon('sun', __iconNode);\n\nexport default Sun;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAwB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACrD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA0B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACvD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAyB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACtD;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACxD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAM,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,EAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 638, "column": 0}, "map": {"version": 3, "file": "clock.js", "sources": ["file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/lucide-react/src/icons/clock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['polyline', { points: '12 6 12 12 16 14', key: '68esgv' }],\n];\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxMiA2IDEyIDEyIDE2IDE0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('clock', __iconNode);\n\nexport default Clock;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC5D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 686, "column": 0}, "map": {"version": 3, "file": "x.js", "sources": ["file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/lucide-react/src/icons/x.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M18 6 6 18', key: '1bl5f8' }],\n  ['path', { d: 'm6 6 12 12', key: 'd8bk6v' }],\n];\n\n/**\n * @component @name X\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTggNiA2IDE4IiAvPgogIDxwYXRoIGQ9Im02IDYgMTIgMTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/x\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst X = createLucideIcon('x', __iconNode);\n\nexport default X;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC7C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,EAAK,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 732, "column": 0}, "map": {"version": 3, "file": "index.mjs", "sources": ["file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/node_modules/react/cjs/react-jsx-runtime.production.js", "file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/node_modules/react/cjs/react-jsx-runtime.development.js", "file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/node_modules/react/jsx-runtime.js", "file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/node_modules/classnames/index.js", "file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/admesh-ui-sdk/src/hooks/useAdMeshTracker.ts", "file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshLinkTracker.tsx", "file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/admesh-ui-sdk/src/hooks/useAdMeshStyles.ts", "file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/admesh-ui-sdk/src/utils/disclosureUtils.ts", "file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshProductCard.tsx", "file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshCompareTable.tsx", "file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshBadge.tsx", "file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshExpandableUnit.tsx", "file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshInlineRecommendation.tsx", "file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshConversationSummary.tsx", "file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshCitationReference.tsx", "file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshCitationUnit.tsx", "file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshConversationalUnit.tsx", "file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshChatMessage.tsx", "file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshChatInput.tsx", "file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshChatInterface.tsx", "file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshFloatingChat.tsx", "file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshSidebarHeader.tsx", "file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshSidebarContent.tsx", "file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshSidebar.tsx", "file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshAutoRecommendationWidget.tsx", "file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/admesh-ui-sdk/src/utils/themeUtils.ts", "file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/node_modules/admesh-ui-sdk/src/index.ts"], "sourcesContent": ["/**\n * @license React\n * react-jsx-runtime.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\nvar REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n  REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\");\nfunction jsxProd(type, config, maybeKey) {\n  var key = null;\n  void 0 !== maybeKey && (key = \"\" + maybeKey);\n  void 0 !== config.key && (key = \"\" + config.key);\n  if (\"key\" in config) {\n    maybeKey = {};\n    for (var propName in config)\n      \"key\" !== propName && (maybeKey[propName] = config[propName]);\n  } else maybeKey = config;\n  config = maybeKey.ref;\n  return {\n    $$typeof: REACT_ELEMENT_TYPE,\n    type: type,\n    key: key,\n    ref: void 0 !== config ? config : null,\n    props: maybeKey\n  };\n}\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsx = jsxProd;\nexports.jsxs = jsxProd;\n", "/**\n * @license React\n * react-jsx-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsx = function (type, config, maybeKey, source, self) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        !1,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n    exports.jsxs = function (type, config, maybeKey, source, self) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        !0,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-runtime.development.js');\n}\n", "/*!\n\tCopyright (c) 2018 <PERSON>.\n\tLicensed under the MIT License (MIT), see\n\thttp://jedwatson.github.io/classnames\n*/\n/* global define */\n\n(function () {\n\t'use strict';\n\n\tvar hasOwn = {}.hasOwnProperty;\n\n\tfunction classNames () {\n\t\tvar classes = '';\n\n\t\tfor (var i = 0; i < arguments.length; i++) {\n\t\t\tvar arg = arguments[i];\n\t\t\tif (arg) {\n\t\t\t\tclasses = appendClass(classes, parseValue(arg));\n\t\t\t}\n\t\t}\n\n\t\treturn classes;\n\t}\n\n\tfunction parseValue (arg) {\n\t\tif (typeof arg === 'string' || typeof arg === 'number') {\n\t\t\treturn arg;\n\t\t}\n\n\t\tif (typeof arg !== 'object') {\n\t\t\treturn '';\n\t\t}\n\n\t\tif (Array.isArray(arg)) {\n\t\t\treturn classNames.apply(null, arg);\n\t\t}\n\n\t\tif (arg.toString !== Object.prototype.toString && !arg.toString.toString().includes('[native code]')) {\n\t\t\treturn arg.toString();\n\t\t}\n\n\t\tvar classes = '';\n\n\t\tfor (var key in arg) {\n\t\t\tif (hasOwn.call(arg, key) && arg[key]) {\n\t\t\t\tclasses = appendClass(classes, key);\n\t\t\t}\n\t\t}\n\n\t\treturn classes;\n\t}\n\n\tfunction appendClass (value, newClass) {\n\t\tif (!newClass) {\n\t\t\treturn value;\n\t\t}\n\t\n\t\tif (value) {\n\t\t\treturn value + ' ' + newClass;\n\t\t}\n\t\n\t\treturn value + newClass;\n\t}\n\n\tif (typeof module !== 'undefined' && module.exports) {\n\t\tclassNames.default = classNames;\n\t\tmodule.exports = classNames;\n\t} else if (typeof define === 'function' && typeof define.amd === 'object' && define.amd) {\n\t\t// register as 'classnames', consistent with npm package name\n\t\tdefine('classnames', [], function () {\n\t\t\treturn classNames;\n\t\t});\n\t} else {\n\t\twindow.classNames = classNames;\n\t}\n}());\n", "import { useState, useCallback, useMemo } from 'react';\nimport type { TrackingData, UseAdMeshTrackerReturn } from '../types/index';\n\n// Default tracking endpoint - can be overridden via config\nconst DEFAULT_TRACKING_URL = 'https://api.useadmesh.com/track';\n\ninterface TrackingConfig {\n  apiBaseUrl?: string;\n  enabled?: boolean;\n  debug?: boolean;\n  retryAttempts?: number;\n  retryDelay?: number;\n}\n\n// Global config that can be set by the consuming application\nlet globalConfig: TrackingConfig = {\n  apiBaseUrl: DEFAULT_TRACKING_URL,\n  enabled: true,\n  debug: false,\n  retryAttempts: 3,\n  retryDelay: 1000\n};\n\nexport const setAdMeshTrackerConfig = (config: Partial<TrackingConfig>) => {\n  globalConfig = { ...globalConfig, ...config };\n};\n\nexport const useAdMeshTracker = (config?: Partial<TrackingConfig>): UseAdMeshTrackerReturn => {\n  const [isTracking, setIsTracking] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const mergedConfig = useMemo(() => ({ ...globalConfig, ...config }), [config]);\n\n  const log = useCallback((message: string, data?: unknown) => {\n    if (mergedConfig.debug) {\n      console.log(`[AdMesh Tracker] ${message}`, data);\n    }\n  }, [mergedConfig.debug]);\n\n  const sendTrackingEvent = useCallback(async (\n    eventType: 'click' | 'view' | 'conversion',\n    data: TrackingData\n  ): Promise<void> => {\n    if (!mergedConfig.enabled) {\n      log('Tracking disabled, skipping event', { eventType, data });\n      return;\n    }\n\n    if (!data.adId || !data.admeshLink) {\n      const errorMsg = 'Missing required tracking data: adId and admeshLink are required';\n      log(errorMsg, data);\n      setError(errorMsg);\n      return;\n    }\n\n    setIsTracking(true);\n    setError(null);\n\n    const payload = {\n      event_type: eventType,\n      ad_id: data.adId,\n      admesh_link: data.admeshLink,\n      product_id: data.productId,\n      user_id: data.userId,\n      session_id: data.sessionId,\n      revenue: data.revenue,\n      conversion_type: data.conversionType,\n      metadata: data.metadata,\n      timestamp: new Date().toISOString(),\n      user_agent: navigator.userAgent,\n      referrer: document.referrer,\n      page_url: window.location.href\n    };\n\n    log(`Sending ${eventType} event`, payload);\n\n    let lastError: Error | null = null;\n    \n    for (let attempt = 1; attempt <= (mergedConfig.retryAttempts || 3); attempt++) {\n      try {\n        const response = await fetch(`${mergedConfig.apiBaseUrl}/events`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify(payload),\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n\n        const result = await response.json();\n        log(`${eventType} event tracked successfully`, result);\n        setIsTracking(false);\n        return;\n\n      } catch (err) {\n        lastError = err as Error;\n        log(`Attempt ${attempt} failed for ${eventType} event`, err);\n        \n        if (attempt < (mergedConfig.retryAttempts || 3)) {\n          await new Promise(resolve => \n            setTimeout(resolve, (mergedConfig.retryDelay || 1000) * attempt)\n          );\n        }\n      }\n    }\n\n    // All attempts failed\n    const errorMsg = `Failed to track ${eventType} event after ${mergedConfig.retryAttempts} attempts: ${lastError?.message}`;\n    log(errorMsg, lastError);\n    setError(errorMsg);\n    setIsTracking(false);\n  }, [mergedConfig, log]);\n\n  const trackClick = useCallback(async (data: TrackingData): Promise<void> => {\n    return sendTrackingEvent('click', data);\n  }, [sendTrackingEvent]);\n\n  const trackView = useCallback(async (data: TrackingData): Promise<void> => {\n    return sendTrackingEvent('view', data);\n  }, [sendTrackingEvent]);\n\n  const trackConversion = useCallback(async (data: TrackingData): Promise<void> => {\n    if (!data.revenue && !data.conversionType) {\n      log('Warning: Conversion tracking without revenue or conversion type', data);\n    }\n    return sendTrackingEvent('conversion', data);\n  }, [sendTrackingEvent, log]);\n\n  return {\n    trackClick,\n    trackView,\n    trackConversion,\n    isTracking,\n    error\n  };\n};\n\n// Utility function to build admesh_link with tracking parameters\nexport const buildAdMeshLink = (\n  baseLink: string, \n  adId: string, \n  additionalParams?: Record<string, string>\n): string => {\n  try {\n    const url = new URL(baseLink);\n    url.searchParams.set('ad_id', adId);\n    url.searchParams.set('utm_source', 'admesh');\n    url.searchParams.set('utm_medium', 'recommendation');\n    \n    if (additionalParams) {\n      Object.entries(additionalParams).forEach(([key, value]) => {\n        url.searchParams.set(key, value);\n      });\n    }\n    \n    return url.toString();\n  } catch (err) {\n    console.warn('[AdMesh] Invalid URL provided to buildAdMeshLink:', baseLink, err);\n    return baseLink;\n  }\n};\n\n// Helper function to extract tracking data from recommendation\nexport const extractTrackingData = (\n  recommendation: { ad_id: string; admesh_link: string; product_id: string },\n  additionalData?: Partial<TrackingData>\n): TrackingData => {\n  return {\n    adId: recommendation.ad_id,\n    admeshLink: recommendation.admesh_link,\n    productId: recommendation.product_id,\n    ...additionalData\n  };\n};\n", "import React, { useCallback, useEffect, useRef } from 'react';\nimport type { AdMeshLinkTrackerProps } from '../types/index';\nimport { useAdMeshTracker } from '../hooks/useAdMeshTracker';\n\nexport const AdMeshLinkTracker: React.FC<AdMeshLinkTrackerProps> = ({\n  adId,\n  admeshLink,\n  productId,\n  children,\n  trackingData,\n  className,\n  style\n}) => {\n  const { trackClick, trackView } = useAdMeshTracker();\n  const elementRef = useRef<HTMLDivElement>(null);\n  const hasTrackedView = useRef(false);\n\n  // Track view when component becomes visible\n  useEffect(() => {\n    if (!elementRef.current || hasTrackedView.current) return;\n\n    const observer = new IntersectionObserver(\n      (entries) => {\n        entries.forEach((entry) => {\n          if (entry.isIntersecting && !hasTrackedView.current) {\n            hasTrackedView.current = true;\n            trackView({\n              adId,\n              admeshLink,\n              productId,\n              ...trackingData\n            }).catch(console.error);\n          }\n        });\n      },\n      {\n        threshold: 0.5, // Track when 50% of the element is visible\n        rootMargin: '0px'\n      }\n    );\n\n    observer.observe(elementRef.current);\n\n    return () => {\n      observer.disconnect();\n    };\n  }, [adId, admeshLink, productId, trackingData, trackView]);\n\n  const handleClick = useCallback(async (event: React.MouseEvent) => {\n    // Track the click\n    try {\n      await trackClick({\n        adId,\n        admeshLink,\n        productId,\n        ...trackingData\n      });\n    } catch (error) {\n      console.error('Failed to track click:', error);\n    }\n\n\n\n    // If the children contain a link, let the browser handle navigation\n    // Otherwise, navigate programmatically\n    const target = event.target as HTMLElement;\n    const link = target.closest('a');\n    \n    if (!link) {\n      // No link found, navigate programmatically\n      window.open(admeshLink, '_blank', 'noopener,noreferrer');\n    }\n    // If there's a link, let the browser handle it naturally\n  }, [adId, admeshLink, productId, trackingData, trackClick]);\n\n  return (\n    <div\n      ref={elementRef}\n      className={className}\n      onClick={handleClick}\n      style={{\n        cursor: 'pointer',\n        ...style\n      }}\n    >\n      {children}\n    </div>\n  );\n};\n\nAdMeshLinkTracker.displayName = 'AdMeshLinkTracker';\n", "import { useEffect } from 'react';\n\n// Complete CSS content as a string - this will be injected automatically\nconst ADMESH_STYLES = `\n/* AdMesh UI SDK - Complete Self-Contained Styles */\n\n/* CSS Reset for AdMesh components */\n.admesh-component, .admesh-component * {\n  box-sizing: border-box;\n}\n\n/* CSS Variables */\n.admesh-component {\n  --admesh-primary: #6366f1;\n  --admesh-primary-hover: #4f46e5;\n  --admesh-secondary: #8b5cf6;\n  --admesh-accent: #06b6d4;\n  --admesh-background: #ffffff;\n  --admesh-surface: #ffffff;\n  --admesh-border: #e2e8f0;\n  --admesh-text: #0f172a;\n  --admesh-text-muted: #64748b;\n  --admesh-text-light: #94a3b8;\n  --admesh-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --admesh-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --admesh-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --admesh-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --admesh-radius: 0.75rem;\n  --admesh-radius-sm: 0.375rem;\n  --admesh-radius-lg: 1rem;\n  --admesh-radius-xl: 1.5rem;\n}\n\n.admesh-component[data-admesh-theme=\"dark\"] {\n  --admesh-background: #111827;\n  --admesh-surface: #1f2937;\n  --admesh-border: #374151;\n  --admesh-text: #f9fafb;\n  --admesh-text-muted: #9ca3af;\n  --admesh-text-light: #6b7280;\n  --admesh-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.3), 0 1px 2px -1px rgb(0 0 0 / 0.3);\n  --admesh-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);\n  --admesh-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);\n  --admesh-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3);\n}\n\n/* Layout Styles */\n.admesh-layout {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif;\n  color: var(--admesh-text);\n  background-color: var(--admesh-background);\n  border-radius: var(--admesh-radius);\n  padding: 1.5rem;\n  box-shadow: var(--admesh-shadow);\n  border: 1px solid var(--admesh-border);\n}\n\n.admesh-layout__header {\n  margin-bottom: 1.5rem;\n  text-align: center;\n}\n\n.admesh-layout__title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: var(--admesh-text);\n  margin-bottom: 0.5rem;\n}\n\n.admesh-layout__subtitle {\n  font-size: 0.875rem;\n  color: var(--admesh-text-muted);\n}\n\n.admesh-layout__cards-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n}\n\n.admesh-layout__more-indicator {\n  text-align: center;\n  padding: 1rem;\n  color: var(--admesh-text-muted);\n  font-size: 0.875rem;\n}\n\n.admesh-layout__empty {\n  text-align: center;\n  padding: 3rem 1rem;\n}\n\n.admesh-layout__empty-content h3 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: var(--admesh-text-muted);\n  margin-bottom: 0.5rem;\n}\n\n.admesh-layout__empty-content p {\n  font-size: 0.875rem;\n  color: var(--admesh-text-muted);\n}\n\n/* Product Card Styles */\n.admesh-product-card {\n  background-color: var(--admesh-surface);\n  border: 1px solid var(--admesh-border);\n  border-radius: var(--admesh-radius);\n  padding: 1.5rem;\n  transition: all 0.2s ease-in-out;\n  position: relative;\n  overflow: hidden;\n}\n\n.admesh-product-card:hover {\n  box-shadow: var(--admesh-shadow-lg);\n  transform: translateY(-2px);\n  border-color: var(--admesh-primary);\n}\n\n.admesh-product-card__header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 1rem;\n}\n\n.admesh-product-card__title {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: var(--admesh-text);\n  margin-bottom: 0.5rem;\n  line-height: 1.4;\n}\n\n.admesh-product-card__reason {\n  font-size: 0.875rem;\n  color: var(--admesh-text-muted);\n  line-height: 1.5;\n  margin-bottom: 1rem;\n}\n\n.admesh-product-card__match-score {\n  margin-bottom: 1rem;\n}\n\n.admesh-product-card__match-score-label {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 0.75rem;\n  color: var(--admesh-text-muted);\n  margin-bottom: 0.25rem;\n}\n\n.admesh-product-card__match-score-bar {\n  width: 100%;\n  height: 0.375rem;\n  background-color: var(--admesh-border);\n  border-radius: var(--admesh-radius-sm);\n  overflow: hidden;\n}\n\n.admesh-product-card__match-score-fill {\n  height: 100%;\n  background: linear-gradient(90deg, var(--admesh-primary), #8b5cf6);\n  border-radius: var(--admesh-radius-sm);\n  transition: width 0.3s ease-in-out;\n}\n\n.admesh-product-card__badges {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.5rem;\n  margin-bottom: 1rem;\n}\n\n.admesh-product-card__badge {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.25rem;\n  padding: 0.25rem 0.5rem;\n  background-color: var(--admesh-primary);\n  color: white;\n  font-size: 0.75rem;\n  font-weight: 500;\n  border-radius: var(--admesh-radius-sm);\n}\n\n.admesh-product-card__badge--secondary {\n  background-color: var(--admesh-secondary);\n}\n\n.admesh-product-card__keywords {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.25rem;\n  margin-bottom: 1rem;\n}\n\n.admesh-product-card__keyword {\n  padding: 0.125rem 0.375rem;\n  background-color: var(--admesh-border);\n  color: var(--admesh-text-muted);\n  font-size: 0.75rem;\n  border-radius: var(--admesh-radius-sm);\n}\n\n/* Dark mode specific enhancements */\n.admesh-component[data-admesh-theme=\"dark\"] .admesh-product-card__keyword {\n  background-color: #4b5563;\n  color: #d1d5db;\n}\n\n.admesh-component[data-admesh-theme=\"dark\"] .admesh-product-card:hover {\n  border-color: var(--admesh-primary);\n  background-color: #374151;\n}\n\n.admesh-component[data-admesh-theme=\"dark\"] .admesh-product-card__button:hover {\n  background: linear-gradient(90deg, var(--admesh-primary-hover), var(--admesh-primary));\n}\n\n.admesh-product-card__footer {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 1.5rem;\n}\n\n/* Mobile-specific sidebar improvements */\n@media (max-width: 640px) {\n  .admesh-sidebar {\n    /* Ensure proper mobile viewport handling */\n    height: 100vh !important;\n    height: 100dvh !important; /* Dynamic viewport height for mobile browsers */\n    max-height: 100vh !important;\n    max-height: 100dvh !important;\n    width: 100vw !important;\n    max-width: 90vw !important;\n    overflow: hidden !important;\n  }\n\n  .admesh-sidebar.relative {\n    height: 100% !important;\n    width: 100% !important;\n    max-width: 100% !important;\n  }\n\n  /* Improve touch scrolling */\n  .admesh-sidebar .overflow-y-auto {\n    -webkit-overflow-scrolling: touch !important;\n    overscroll-behavior: contain !important;\n    scroll-behavior: smooth !important;\n  }\n\n  /* Prevent body scroll when sidebar is open */\n  body:has(.admesh-sidebar[data-mobile-open=\"true\"]) {\n    overflow: hidden !important;\n    position: fixed !important;\n    width: 100% !important;\n  }\n}\n\n/* Tablet improvements */\n@media (min-width: 641px) and (max-width: 1024px) {\n  .admesh-sidebar {\n    max-width: 400px !important;\n  }\n}\n\n/* Mobile responsiveness improvements for all components */\n@media (max-width: 640px) {\n  /* Product cards mobile optimization */\n  .admesh-card {\n    padding: 0.75rem !important;\n    margin-bottom: 0.75rem !important;\n  }\n\n  /* Inline recommendations mobile optimization */\n  .admesh-inline-recommendation {\n    padding: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n\n  /* Conversation summary mobile optimization */\n  .admesh-conversation-summary {\n    padding: 1rem !important;\n  }\n\n  /* Percentage text mobile improvements */\n  .admesh-component .text-xs {\n    font-size: 0.75rem !important;\n    line-height: 1rem !important;\n  }\n\n  .admesh-component .text-sm {\n    font-size: 0.875rem !important;\n    line-height: 1.25rem !important;\n  }\n\n  /* Button mobile improvements */\n  .admesh-component button {\n    padding: 0.375rem 0.75rem !important;\n    font-size: 0.75rem !important;\n    min-height: 2rem !important;\n    touch-action: manipulation !important;\n  }\n\n  /* Badge mobile improvements */\n  .admesh-component .rounded-full {\n    padding: 0.25rem 0.5rem !important;\n    font-size: 0.625rem !important;\n    line-height: 1rem !important;\n  }\n\n  /* Progress bar mobile improvements */\n  .admesh-component .bg-gray-200,\n  .admesh-component .bg-slate-600 {\n    height: 0.25rem !important;\n  }\n\n  /* Flex layout mobile improvements */\n  .admesh-component .flex {\n    flex-wrap: wrap !important;\n  }\n\n  .admesh-component .gap-2 {\n    gap: 0.375rem !important;\n  }\n\n  .admesh-component .gap-3 {\n    gap: 0.5rem !important;\n  }\n}\n\n.admesh-product-card__button {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1.5rem;\n  background: linear-gradient(90deg, var(--admesh-primary), var(--admesh-primary-hover));\n  color: white;\n  font-size: 0.875rem;\n  font-weight: 500;\n  border: none;\n  border-radius: var(--admesh-radius);\n  cursor: pointer;\n  transition: all 0.2s ease-in-out;\n  text-decoration: none;\n}\n\n.admesh-product-card__button:hover {\n  transform: translateY(-1px);\n  box-shadow: var(--admesh-shadow-lg);\n}\n\n/* Utility Classes */\n.admesh-text-xs { font-size: 0.75rem; }\n.admesh-text-sm { font-size: 0.875rem; }\n.admesh-text-base { font-size: 1rem; }\n.admesh-text-lg { font-size: 1.125rem; }\n.admesh-text-xl { font-size: 1.25rem; }\n\n.admesh-font-medium { font-weight: 500; }\n.admesh-font-semibold { font-weight: 600; }\n.admesh-font-bold { font-weight: 700; }\n\n.admesh-text-muted { color: var(--admesh-text-muted); }\n\n/* Comparison Table Styles */\n.admesh-compare-table {\n  width: 100%;\n  border-collapse: collapse;\n  background-color: var(--admesh-surface);\n  border: 1px solid var(--admesh-border);\n  border-radius: var(--admesh-radius);\n  overflow: hidden;\n}\n\n.admesh-compare-table th,\n.admesh-compare-table td {\n  padding: 0.75rem;\n  text-align: left;\n  border-bottom: 1px solid var(--admesh-border);\n}\n\n.admesh-compare-table th {\n  background-color: var(--admesh-background);\n  font-weight: 600;\n  color: var(--admesh-text);\n  font-size: 0.875rem;\n}\n\n.admesh-compare-table td {\n  color: var(--admesh-text);\n  font-size: 0.875rem;\n}\n\n.admesh-compare-table tr:hover {\n  background-color: var(--admesh-border);\n}\n\n/* Dark mode table enhancements */\n.admesh-component[data-admesh-theme=\"dark\"] .admesh-compare-table th {\n  background-color: #374151;\n}\n\n.admesh-component[data-admesh-theme=\"dark\"] .admesh-compare-table tr:hover {\n  background-color: #4b5563;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .admesh-layout {\n    padding: 1rem;\n  }\n\n  .admesh-layout__cards-grid {\n    grid-template-columns: 1fr;\n    gap: 0.75rem;\n  }\n\n  .admesh-product-card {\n    padding: 1rem;\n  }\n\n  .admesh-compare-table {\n    font-size: 0.75rem;\n  }\n\n  .admesh-compare-table th,\n  .admesh-compare-table td {\n    padding: 0.5rem;\n  }\n}\n\n/* Essential Utility Classes for Self-Contained SDK - High Specificity */\n.admesh-component .relative { position: relative !important; }\n.admesh-component .absolute { position: absolute !important; }\n.admesh-component .flex { display: flex !important; }\n.admesh-component .inline-flex { display: inline-flex !important; }\n.admesh-component .grid { display: grid !important; }\n.admesh-component .hidden { display: none !important; }\n.admesh-component .block { display: block !important; }\n.admesh-component .inline-block { display: inline-block !important; }\n\n/* Flexbox utilities */\n.admesh-component .flex-col { flex-direction: column !important; }\n.admesh-component .flex-row { flex-direction: row !important; }\n.admesh-component .flex-wrap { flex-wrap: wrap !important; }\n.admesh-component .items-center { align-items: center !important; }\n.admesh-component .items-start { align-items: flex-start !important; }\n.admesh-component .items-end { align-items: flex-end !important; }\n.admesh-component .justify-center { justify-content: center !important; }\n.admesh-component .justify-between { justify-content: space-between !important; }\n.admesh-component .justify-end { justify-content: flex-end !important; }\n.admesh-component .flex-1 { flex: 1 1 0% !important; }\n.admesh-component .flex-shrink-0 { flex-shrink: 0 !important; }\n\n/* Grid utilities */\n.admesh-component .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }\n.admesh-component .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }\n.admesh-component .grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }\n\n/* Spacing utilities */\n.admesh-component .gap-1 { gap: 0.25rem; }\n.admesh-component .gap-2 { gap: 0.5rem; }\n.admesh-component .gap-3 { gap: 0.75rem; }\n.admesh-component .gap-4 { gap: 1rem; }\n.admesh-component .gap-6 { gap: 1.5rem; }\n.admesh-component .gap-8 { gap: 2rem; }\n\n/* Padding utilities */\n.admesh-component .p-1 { padding: 0.25rem; }\n.admesh-component .p-2 { padding: 0.5rem; }\n.admesh-component .p-3 { padding: 0.75rem; }\n.admesh-component .p-4 { padding: 1rem; }\n.admesh-component .p-5 { padding: 1.25rem; }\n.admesh-component .p-6 { padding: 1.5rem; }\n.admesh-component .px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }\n.admesh-component .px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }\n.admesh-component .px-4 { padding-left: 1rem; padding-right: 1rem; }\n.admesh-component .py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }\n.admesh-component .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }\n.admesh-component .py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }\n.admesh-component .pt-2 { padding-top: 0.5rem; }\n.admesh-component .pt-3 { padding-top: 0.75rem; }\n.admesh-component .pb-2 { padding-bottom: 0.5rem; }\n.admesh-component .pb-3 { padding-bottom: 0.75rem; }\n\n/* Margin utilities */\n.admesh-component .m-0 { margin: 0; }\n.admesh-component .mb-1 { margin-bottom: 0.25rem; }\n.admesh-component .mb-2 { margin-bottom: 0.5rem; }\n.admesh-component .mb-3 { margin-bottom: 0.75rem; }\n.admesh-component .mb-4 { margin-bottom: 1rem; }\n.admesh-component .mb-6 { margin-bottom: 1.5rem; }\n.admesh-component .mt-1 { margin-top: 0.25rem; }\n.admesh-component .mt-2 { margin-top: 0.5rem; }\n.admesh-component .mt-4 { margin-top: 1rem; }\n.admesh-component .mt-6 { margin-top: 1.5rem; }\n.admesh-component .mt-auto { margin-top: auto; }\n.admesh-component .ml-1 { margin-left: 0.25rem; }\n.admesh-component .mr-1 { margin-right: 0.25rem; }\n.admesh-component .mr-2 { margin-right: 0.5rem; }\n\n/* Width and height utilities */\n.admesh-component .w-2 { width: 0.5rem; }\n.admesh-component .w-3 { width: 0.75rem; }\n.admesh-component .w-4 { width: 1rem; }\n.admesh-component .w-5 { width: 1.25rem; }\n.admesh-component .w-6 { width: 1.5rem; }\n.admesh-component .w-full { width: 100%; }\n.admesh-component .w-fit { width: fit-content; }\n.admesh-component .h-2 { height: 0.5rem; }\n.admesh-component .h-3 { height: 0.75rem; }\n.admesh-component .h-4 { height: 1rem; }\n.admesh-component .h-5 { height: 1.25rem; }\n.admesh-component .h-6 { height: 1.5rem; }\n.admesh-component .h-full { height: 100%; }\n.admesh-component .min-w-0 { min-width: 0px; }\n\n/* Border utilities */\n.admesh-component .border { border-width: 1px; }\n.admesh-component .border-t { border-top-width: 1px; }\n.admesh-component .border-gray-100 { border-color: #f3f4f6; }\n.admesh-component .border-gray-200 { border-color: #e5e7eb; }\n.admesh-component .border-gray-300 { border-color: #d1d5db; }\n.admesh-component .border-blue-200 { border-color: #bfdbfe; }\n.admesh-component .border-green-200 { border-color: #bbf7d0; }\n\n/* Border radius utilities */\n.admesh-component .rounded { border-radius: 0.25rem !important; }\n.admesh-component .rounded-md { border-radius: 0.375rem !important; }\n.admesh-component .rounded-lg { border-radius: 0.5rem !important; }\n.admesh-component .rounded-xl { border-radius: 0.75rem !important; }\n.admesh-component .rounded-full { border-radius: 9999px !important; }\n\n/* Background utilities */\n.admesh-component .bg-white { background-color: #ffffff; }\n.admesh-component .bg-gray-50 { background-color: #f9fafb; }\n.admesh-component .bg-gray-100 { background-color: #f3f4f6; }\n.admesh-component .bg-blue-50 { background-color: #eff6ff; }\n.admesh-component .bg-blue-100 { background-color: #dbeafe; }\n.admesh-component .bg-green-100 { background-color: #dcfce7; }\n.admesh-component .bg-green-500 { background-color: #22c55e; }\n.admesh-component .bg-blue-500 { background-color: #3b82f6; }\n\n/* Gradients */\n.admesh-component .bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)); }\n.admesh-component .bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }\n.admesh-component .from-white { --tw-gradient-from: #ffffff; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(255, 255, 255, 0)); }\n.admesh-component .to-gray-50 { --tw-gradient-to: #f9fafb; }\n.admesh-component .from-purple-500 { --tw-gradient-from: #a855f7; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(168, 85, 247, 0)); }\n.admesh-component .to-pink-500 { --tw-gradient-to: #ec4899; }\n.admesh-component .from-green-400 { --tw-gradient-from: #4ade80; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(74, 222, 128, 0)); }\n.admesh-component .to-blue-500 { --tw-gradient-to: #3b82f6; }\n\n/* Text utilities */\n.admesh-component .text-xs { font-size: 0.75rem; line-height: 1rem; }\n.admesh-component .text-sm { font-size: 0.875rem; line-height: 1.25rem; }\n.admesh-component .text-base { font-size: 1rem; line-height: 1.5rem; }\n.admesh-component .text-lg { font-size: 1.125rem; line-height: 1.75rem; }\n.admesh-component .text-xl { font-size: 1.25rem; line-height: 1.75rem; }\n.admesh-component .font-medium { font-weight: 500; }\n.admesh-component .font-semibold { font-weight: 600; }\n.admesh-component .font-bold { font-weight: 700; }\n.admesh-component .leading-relaxed { line-height: 1.625; }\n\n/* Text colors */\n.admesh-component .text-white { color: #ffffff; }\n.admesh-component .text-gray-400 { color: #9ca3af; }\n.admesh-component .text-gray-500 { color: #6b7280; }\n.admesh-component .text-gray-600 { color: #4b5563; }\n.admesh-component .text-gray-700 { color: #374151; }\n.admesh-component .text-gray-800 { color: #1f2937; }\n.admesh-component .text-blue-600 { color: #2563eb; }\n.admesh-component .text-blue-700 { color: #1d4ed8; }\n.admesh-component .text-green-700 { color: #15803d; }\n\n/* Shadow utilities */\n.admesh-component .shadow-sm { box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05); }\n.admesh-component .shadow { box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1); }\n.admesh-component .shadow-md { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); }\n.admesh-component .shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); }\n.admesh-component .shadow-xl { box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1); }\n\n/* Transition utilities */\n.admesh-component .transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }\n.admesh-component .transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }\n.admesh-component .duration-200 { transition-duration: 200ms; }\n.admesh-component .duration-300 { transition-duration: 300ms; }\n\n/* Transform utilities */\n.admesh-component .hover\\\\:-translate-y-1:hover { transform: translateY(-0.25rem); }\n.admesh-component .hover\\\\:scale-105:hover { transform: scale(1.05); }\n\n/* Hover utilities */\n.admesh-component .hover\\\\:shadow-xl:hover { box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1); }\n.admesh-component .hover\\\\:bg-gray-100:hover { background-color: #f3f4f6; }\n.admesh-component .hover\\\\:text-blue-800:hover { color: #1e40af; }\n\n/* Cursor utilities */\n.admesh-component .cursor-pointer { cursor: pointer; }\n\n/* Overflow utilities */\n.admesh-component .overflow-hidden { overflow: hidden; }\n.admesh-component .truncate { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }\n\n/* Text decoration */\n.admesh-component .underline { text-decoration-line: underline; }\n\n/* Whitespace */\n.admesh-component .whitespace-nowrap { white-space: nowrap; }\n\n/* Dark mode utilities */\n@media (prefers-color-scheme: dark) {\n  .admesh-component .dark\\\\:bg-slate-800 { background-color: #1e293b; }\n  .admesh-component .dark\\\\:bg-slate-900 { background-color: #0f172a; }\n  .admesh-component .dark\\\\:border-slate-700 { border-color: #334155; }\n  .admesh-component .dark\\\\:text-white { color: #ffffff; }\n  .admesh-component .dark\\\\:text-gray-200 { color: #e5e7eb; }\n  .admesh-component .dark\\\\:text-gray-300 { color: #d1d5db; }\n  .admesh-component .dark\\\\:text-gray-400 { color: #9ca3af; }\n  .admesh-component .dark\\\\:text-blue-400 { color: #60a5fa; }\n}\n\n/* Responsive utilities */\n@media (min-width: 640px) {\n  .admesh-component .sm\\\\:p-5 { padding: 1.25rem; }\n  .admesh-component .sm\\\\:text-base { font-size: 1rem; line-height: 1.5rem; }\n  .admesh-component .sm\\\\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }\n  .admesh-component .sm\\\\:flex-row { flex-direction: row; }\n  .admesh-component .sm\\\\:items-center { align-items: center; }\n  .admesh-component .sm\\\\:justify-between { justify-content: space-between; }\n}\n\n@media (min-width: 768px) {\n  .admesh-component .md\\\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }\n}\n\n@media (min-width: 1024px) {\n  .admesh-component .lg\\\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }\n  .admesh-component .lg\\\\:col-span-1 { grid-column: span 1 / span 1; }\n}\n`;\n\nlet stylesInjected = false;\n\nexport const useAdMeshStyles = () => {\n  useEffect(() => {\n    if (stylesInjected) return;\n\n    // Create and inject styles\n    const styleElement = document.createElement('style');\n    styleElement.id = 'admesh-ui-sdk-styles';\n    styleElement.textContent = ADMESH_STYLES;\n    \n    // Check if styles are already injected\n    if (!document.getElementById('admesh-ui-sdk-styles')) {\n      document.head.appendChild(styleElement);\n      stylesInjected = true;\n    }\n\n    // Cleanup function\n    return () => {\n      const existingStyle = document.getElementById('admesh-ui-sdk-styles');\n      if (existingStyle && document.head.contains(existingStyle)) {\n        document.head.removeChild(existingStyle);\n        stylesInjected = false;\n      }\n    };\n  }, []);\n};\n", "import type { AdMeshRecommendation } from '../types/index';\n\n/**\n * Utility functions for generating compliant disclosure labels and tooltips\n */\n\nexport interface DisclosureConfig {\n  showTooltips?: boolean;\n  compactMode?: boolean;\n  customLabels?: {\n    smartPick?: string;\n    partnerMatch?: string;\n    promotedOption?: string;\n    relatedOption?: string;\n  };\n}\n\n/**\n * Generate appropriate label based on match score and recommendation quality\n */\nexport const getRecommendationLabel = (\n  recommendation: AdMeshRecommendation,\n  config: DisclosureConfig = {}\n): string => {\n  const matchScore = recommendation.intent_match_score || 0;\n  const customLabels = config.customLabels || {};\n\n  // High match score (>0.8)\n  if (matchScore >= 0.8) {\n    return customLabels.smartPick || 'Smart Pick';\n  }\n  \n  // Medium match score (0.6-0.8)\n  if (matchScore >= 0.6) {\n    return customLabels.partnerMatch || 'Partner Match';\n  }\n  \n  // Lower match score (<0.6)\n  if (matchScore >= 0.3) {\n    return customLabels.promotedOption || 'Promoted Option';\n  }\n  \n  // Very low match - related option\n  return customLabels.relatedOption || 'Related Option';\n};\n\n/**\n * Generate tooltip text for recommendation labels\n */\nexport const getLabelTooltip = (\n  recommendation: AdMeshRecommendation,\n  _label: string\n): string => {\n  const matchScore = recommendation.intent_match_score || 0;\n\n  if (matchScore >= 0.8) {\n    return \"This recommendation is from a partner who compensates us when you engage. We've matched it to your needs based on your query.\";\n  }\n  \n  if (matchScore >= 0.6) {\n    return \"Top-rated partner solution matched to your specific requirements. Partner compensates us for qualified referrals.\";\n  }\n  \n  if (matchScore >= 0.3) {\n    return \"This partner solution may be relevant to your needs. The partner compensates us when you take qualifying actions.\";\n  }\n  \n  return \"This solution is somewhat related to your query. While not a perfect match, it might still be helpful. This partner compensates us for qualified referrals.\";\n};\n\n/**\n * Generate section-level disclosure text\n */\nexport const getSectionDisclosure = (\n  hasHighMatches: boolean = true,\n  isExpanded: boolean = false\n): string => {\n  if (!hasHighMatches) {\n    return \"Expanded Results: While these don't perfectly match your query, they're related solutions from our partner network. All partners compensate us for referrals.\";\n  }\n  \n  if (isExpanded) {\n    return \"These curated recommendations are from partners who compensate us for referrals.\";\n  }\n  \n  return \"Personalized Partner Recommendations: All results are from vetted partners who compensate us for qualified matches. We've ranked them based on relevance to your specific needs.\";\n};\n\n/**\n * Generate inline disclosure text for product cards\n */\nexport const getInlineDisclosure = (\n  recommendation: AdMeshRecommendation,\n  compact: boolean = false\n): string => {\n  const matchScore = recommendation.intent_match_score || 0;\n\n  if (compact) {\n    return \"Promoted Match\";\n  }\n\n  if (matchScore >= 0.8) {\n    return \"Smart Pick\";\n  }\n\n  if (matchScore >= 0.6) {\n    return \"Partner Match\";\n  }\n\n  return \"Promoted Option\";\n};\n\n/**\n * Generate detailed tooltip for inline disclosures\n */\nexport const getInlineTooltip = (): string => {\n  return \"We've partnered with trusted providers to bring you relevant solutions. These partners compensate us for qualified referrals, which helps us keep our service free.\";\n};\n\n/**\n * Generate badge text without emojis\n */\nexport const getBadgeText = (badgeType: string): string => {\n  const badgeMap: Record<string, string> = {\n    'Top Match': 'Top Match',\n    'Smart Pick': 'Smart Pick',\n    'Perfect Fit': 'Perfect Fit',\n    'Great Match': 'Great Match',\n    'Recommended': 'Recommended',\n    'Good Fit': 'Good Fit',\n    'Featured': 'Featured',\n    'Popular Choice': 'Popular Choice',\n    'Premium Pick': 'Premium Pick',\n    'Free Tier': 'Free Tier',\n    'AI Powered': 'AI Powered',\n    'Popular': 'Popular',\n    'New': 'New',\n    'Trial Available': 'Trial Available',\n    'Related Option': 'Related Option',\n    'Alternative Solution': 'Alternative Solution',\n    'Expanded Match': 'Expanded Match'\n  };\n  \n  return badgeMap[badgeType] || badgeType;\n};\n\n/**\n * Generate appropriate CTA text\n */\nexport const getCtaText = (\n  recommendation: AdMeshRecommendation,\n  context: 'button' | 'link' = 'button'\n): string => {\n  const productName = recommendation.recommendation_title || recommendation.title;\n  \n  if (context === 'link') {\n    return productName;\n  }\n  \n  // For buttons, use action-oriented text\n  if (recommendation.trial_days && recommendation.trial_days > 0) {\n    return `Try ${productName}`;\n  }\n\n  return `Learn More`;\n};\n\n/**\n * Check if recommendations have high match scores\n */\nexport const hasHighQualityMatches = (recommendations: AdMeshRecommendation[]): boolean => {\n  return recommendations.some(rec => (rec.intent_match_score || 0) >= 0.8);\n};\n\n/**\n * Generate compliant powered-by text\n */\nexport const getPoweredByText = (compact: boolean = false): string => {\n  if (compact) {\n    return \"Powered by AdMesh\";\n  }\n  \n  return \"Recommendations powered by AdMesh\";\n};\n", "import React, { useMemo, useState } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshProductCardProps, BadgeType } from '../types/index';\nimport { AdMeshLinkTracker } from './AdMeshLinkTracker';\nimport { useAdMeshStyles } from '../hooks/useAdMeshStyles';\nimport {\n  getRecommendationLabel,\n  getLabelTooltip,\n  getInlineDisclosure,\n  getInlineTooltip,\n  getBadgeText\n} from '../utils/disclosureUtils';\n\nexport const AdMeshProductCard: React.FC<AdMeshProductCardProps> = ({\n  recommendation,\n  theme,\n  showMatchScore = false,\n  showBadges = true,\n  variation = 'default',\n  className,\n  style\n}) => {\n  // Inject styles automatically\n  useAdMeshStyles();\n\n  // State for expandable variations\n  const [isExpanded, setIsExpanded] = useState(false);\n  // Generate badges based on recommendation data using compliant labels\n  const badges = useMemo((): BadgeType[] => {\n    const generatedBadges: BadgeType[] = [];\n\n    // Add primary recommendation label based on match score\n    const primaryLabel = getRecommendationLabel(recommendation);\n    if (primaryLabel === 'Smart Pick') {\n      generatedBadges.push('Top Match'); // Map to existing badge type\n    }\n\n    // Add Trial Available badge\n    if (recommendation.trial_days && recommendation.trial_days > 0) {\n      generatedBadges.push('Trial Available');\n    }\n\n    // Add AI Powered badge (check if AI-related keywords exist)\n    const aiKeywords = ['ai', 'artificial intelligence', 'machine learning', 'ml', 'automation'];\n    const hasAIKeywords = recommendation.keywords?.some(keyword =>\n      aiKeywords.some(ai => keyword.toLowerCase().includes(ai))\n    ) || recommendation.title.toLowerCase().includes('ai');\n\n    if (hasAIKeywords) {\n      generatedBadges.push('AI Powered');\n    }\n\n    // Add badges from the response if available\n    if (recommendation.badges && recommendation.badges.length > 0) {\n      recommendation.badges.forEach(badge => {\n        // Only add if it's a valid BadgeType and not already included\n        if (['Top Match', 'Free Tier', 'AI Powered', 'Popular', 'New', 'Trial Available'].includes(badge) &&\n            !generatedBadges.includes(badge as BadgeType)) {\n          generatedBadges.push(badge as BadgeType);\n        }\n      });\n    }\n\n    // Note: is_open_source field has been removed\n\n    return generatedBadges;\n  }, [recommendation]);\n\n  // Get compliant disclosure text\n  const inlineDisclosure = getInlineDisclosure(recommendation, false);\n  const inlineTooltip = getInlineTooltip();\n\n  // Format match score as percentage\n  const matchScorePercentage = Math.round(recommendation.intent_match_score * 100);\n\n  // Get content based on variation\n  const getVariationContent = () => {\n    const variations = recommendation.content_variations;\n\n    if (variation === 'simple') {\n      return {\n        title: recommendation.recommendation_title || recommendation.title,\n        description: recommendation.recommendation_description || recommendation.description || recommendation.reason,\n        ctaText: recommendation.recommendation_title || recommendation.title,\n        isSimple: true\n      };\n    } else if (variation === 'question' && variations?.question) {\n      return {\n        title: variations.question.cta || recommendation.recommendation_title || recommendation.title,\n        description: variations.question.text,\n        ctaText: variations.question.cta || recommendation.recommendation_title || recommendation.title\n      };\n    } else if (variation === 'statement' && variations?.statement) {\n      return {\n        title: recommendation.recommendation_title || recommendation.title,\n        description: variations.statement.text,\n        ctaText: variations.statement.cta || recommendation.recommendation_title || recommendation.title\n      };\n    } else {\n      // Default variation\n      return {\n        title: recommendation.recommendation_title || recommendation.title,\n        description: recommendation.recommendation_description || recommendation.description || recommendation.reason,\n        ctaText: recommendation.recommendation_title || recommendation.title\n      };\n    }\n  };\n\n  const content = getVariationContent();\n\n  const cardClasses = classNames(\n    'admesh-component',\n    'admesh-card',\n    'relative p-4 sm:p-5 rounded-xl bg-gradient-to-br from-white to-gray-50 dark:from-slate-800 dark:to-slate-900 border border-gray-200/50 dark:border-slate-700/50 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1',\n    className\n  );\n\n  const cardStyle = theme ? {\n    '--admesh-primary': theme.primaryColor || theme.accentColor || '#3b82f6',\n    '--admesh-secondary': theme.secondaryColor || '#10b981',\n    '--admesh-accent': theme.accentColor || '#3b82f6',\n    '--admesh-background': theme.backgroundColor,\n    '--admesh-surface': theme.surfaceColor,\n    '--admesh-border': theme.borderColor,\n    '--admesh-text': theme.textColor,\n    '--admesh-text-secondary': theme.textSecondaryColor,\n    '--admesh-radius': theme.borderRadius || '12px',\n    '--admesh-shadow-sm': theme.shadows?.small,\n    '--admesh-shadow-md': theme.shadows?.medium,\n    '--admesh-shadow-lg': theme.shadows?.large,\n    '--admesh-spacing-sm': theme.spacing?.small,\n    '--admesh-spacing-md': theme.spacing?.medium,\n    '--admesh-spacing-lg': theme.spacing?.large,\n    '--admesh-font-size-sm': theme.fontSize?.small,\n    '--admesh-font-size-base': theme.fontSize?.base,\n    '--admesh-font-size-lg': theme.fontSize?.large,\n    '--admesh-font-size-title': theme.fontSize?.title,\n    fontFamily: theme.fontFamily\n  } as React.CSSProperties : undefined;\n\n  // Render different layouts based on variation\n  if (variation === 'simple') {\n    // Simple inline ad format (replaces AdMeshSimpleAd)\n    return (\n      <div\n        className={classNames(\n          \"admesh-component admesh-simple-ad\",\n          \"inline-block text-sm leading-relaxed\",\n          className\n        )}\n        style={{\n          fontFamily: theme?.fontFamily || '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n          ...theme?.components?.productCard,\n          ...style\n        }}\n        data-admesh-theme={theme?.mode}\n      >\n        {/* Recommendation label */}\n        <span\n          style={{\n            fontSize: '11px',\n            fontWeight: '600',\n            color: theme?.accentColor || '#2563eb',\n            backgroundColor: theme?.mode === 'dark' ? '#374151' : '#f3f4f6',\n            padding: '2px 6px',\n            borderRadius: '4px',\n            marginRight: '8px'\n          }}\n          title={getLabelTooltip(recommendation, getRecommendationLabel(recommendation))}\n        >\n          {getRecommendationLabel(recommendation)}\n        </span>\n\n        {/* Main content */}\n        <span\n          style={{\n            color: theme?.mode === 'dark' ? '#f3f4f6' : '#374151',\n            marginRight: '4px'\n          }}\n        >\n          {content.description}{' '}\n        </span>\n\n        {/* CTA Link */}\n        <AdMeshLinkTracker\n          adId={recommendation.ad_id}\n          admeshLink={recommendation.admesh_link}\n          productId={recommendation.product_id}\n          trackingData={{\n            title: recommendation.title,\n            matchScore: recommendation.intent_match_score,\n            component: 'simple_ad_cta'\n          }}\n        >\n          <span\n            style={{\n              color: theme?.accentColor || '#2563eb',\n              textDecoration: 'underline',\n              cursor: 'pointer',\n              fontSize: 'inherit',\n              fontFamily: 'inherit'\n            }}\n          >\n            {content.ctaText}\n          </span>\n        </AdMeshLinkTracker>\n\n        {/* Disclosure */}\n        <span\n          style={{\n            fontSize: '10px',\n            color: theme?.mode === 'dark' ? '#9ca3af' : '#6b7280',\n            marginLeft: '8px'\n          }}\n          title={inlineTooltip}\n        >\n          ({inlineDisclosure})\n        </span>\n      </div>\n    );\n  }\n\n  if (variation === 'question' || variation === 'statement') {\n    // Expandable layout - starts simple, can expand to full card\n    return (\n      <div\n        className={classNames(\n          \"admesh-component admesh-expandable-variation transition-all duration-300\",\n          isExpanded\n            ? \"p-4 sm:p-5 rounded-xl bg-gradient-to-br from-white to-gray-50 dark:from-slate-800 dark:to-slate-900 border border-gray-200/50 dark:border-slate-700/50 shadow-lg\"\n            : \"p-4 rounded-lg bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 shadow-sm hover:shadow-md\",\n          className\n        )}\n        style={{\n          fontFamily: theme?.fontFamily || '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n          ...theme?.components?.productCard,\n          ...style\n        }}\n        data-admesh-theme={theme?.mode}\n      >\n        {!isExpanded ? (\n          // Simple inline layout with top label\n          <>\n            {/* Recommendation label at top */}\n            <div className=\"mb-2\">\n              <span\n                style={{\n                  fontSize: '11px',\n                  fontWeight: '600',\n                  color: theme?.accentColor || '#2563eb',\n                  backgroundColor: theme?.mode === 'dark' ? '#374151' : '#f3f4f6',\n                  padding: '2px 6px',\n                  borderRadius: '4px'\n                }}\n                title={getLabelTooltip(recommendation, getRecommendationLabel(recommendation))}\n              >\n                {getRecommendationLabel(recommendation)}\n              </span>\n            </div>\n\n            <div className=\"flex items-center justify-between gap-3\">\n              {/* Content */}\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm text-gray-700 dark:text-gray-300 leading-relaxed\">\n                  {content.description}{' '}\n                  <AdMeshLinkTracker\n                    adId={recommendation.ad_id}\n                    admeshLink={recommendation.admesh_link}\n                    productId={recommendation.product_id}\n                    trackingData={{\n                      title: recommendation.title,\n                      matchScore: recommendation.intent_match_score,\n                      component: 'simple_variation_cta'\n                    }}\n                  >\n                    <span\n                      className=\"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline cursor-pointer font-medium transition-colors\"\n                    >\n                      {content.ctaText}\n                    </span>\n                  </AdMeshLinkTracker>\n                </p>\n              </div>\n\n              {/* Expand Button */}\n              <div className=\"flex items-center gap-3 flex-shrink-0\">\n                <button\n                  onClick={() => setIsExpanded(true)}\n                  className=\"flex items-center gap-2 px-3 py-2 text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-all duration-200 border border-blue-200 dark:border-blue-700 hover:border-blue-300 dark:hover:border-blue-600\"\n                  title=\"View more details\"\n                >\n                  <span>More Details</span>\n                  <svg className=\"h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                </button>\n              </div>\n            </div>\n\n\n          </>\n        ) : (\n          // Expanded full card layout (same as default variation)\n          <div\n            className=\"h-full flex flex-col\"\n            style={cardStyle}\n            data-admesh-theme={theme?.mode}\n          >\n            {/* Header with badges, title, and collapse button */}\n            <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3 mb-4\">\n              <div className=\"flex flex-col sm:flex-row sm:items-center gap-2 flex-1 min-w-0\">\n                {showBadges && badges.includes('Top Match') && (\n                  <span\n                    className=\"text-xs font-semibold text-white px-3 py-1 rounded-full w-fit shadow-md\"\n                    style={{\n                      backgroundColor: theme?.primaryColor || theme?.accentColor || '#f59e0b',\n                      borderRadius: theme?.borderRadius || '9999px'\n                    }}\n                    title={getLabelTooltip(recommendation, 'Smart Pick')}\n                  >\n                    {getBadgeText('Top Match')}\n                  </span>\n                )}\n                <div className=\"flex items-center gap-2 min-w-0\">\n                  {recommendation.product_logo && (\n                    <img\n                      src={recommendation.product_logo.url}\n                      alt={`${recommendation.title} logo`}\n                      className=\"w-6 h-6 rounded flex-shrink-0\"\n                      onError={(e) => {\n                        // Hide image if it fails to load\n                        (e.target as HTMLImageElement).style.display = 'none';\n                      }}\n                    />\n                  )}\n                  <h4 className=\"font-semibold text-gray-800 dark:text-gray-200 text-sm sm:text-base truncate\">\n                    {content.title}\n                  </h4>\n                </div>\n              </div>\n\n              <div className=\"flex gap-3 flex-shrink-0\">\n                <button\n                  onClick={() => setIsExpanded(false)}\n                  className=\"flex items-center gap-2 px-3 py-2 text-xs font-medium text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-800/50 rounded-lg transition-all duration-200 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600\"\n                  title=\"Show less details\"\n                >\n                  <span>Less Details</span>\n                  <svg className=\"h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 12H4\" />\n                  </svg>\n                </button>\n                <AdMeshLinkTracker\n                  adId={recommendation.ad_id}\n                  admeshLink={recommendation.admesh_link}\n                  productId={recommendation.product_id}\n                  trackingData={{\n                    title: recommendation.title,\n                    matchScore: recommendation.intent_match_score,\n                    component: 'product_card_cta'\n                  }}\n                >\n                  <button className=\"text-xs sm:text-sm px-3 py-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600 flex items-center transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg\">\n                    {variation === 'question' ? 'Try' : 'Visit'} {content.ctaText}\n                    <svg className=\"ml-1 h-3 w-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\" />\n                    </svg>\n                  </button>\n                </AdMeshLinkTracker>\n              </div>\n            </div>\n\n            {/* Product Description/Reason */}\n            <div className=\"mb-6\">\n              <p className=\"text-sm text-gray-600 dark:text-gray-300 leading-relaxed\">\n                {content.description}\n              </p>\n            </div>\n\n            {/* Disclosure */}\n            <div className=\"mb-6\">\n              <p\n                className=\"text-xs text-gray-500 dark:text-gray-400 leading-relaxed\"\n                title={inlineTooltip}\n              >\n                {inlineDisclosure}\n              </p>\n            </div>\n\n            {/* Match Score */}\n            {showMatchScore && typeof recommendation.intent_match_score === \"number\" && (\n              <div className=\"mb-4\">\n                <div className=\"flex items-center justify-between text-xs sm:text-sm text-gray-600 dark:text-gray-300 mb-2\">\n                  <span className=\"font-medium\">Match Score</span>\n                  <span className=\"font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-blue-500 whitespace-nowrap\">{matchScorePercentage}% match</span>\n                </div>\n                <div className=\"w-full bg-gray-200 dark:bg-slate-600 rounded-full h-2 overflow-hidden\">\n                  <div\n                    className=\"bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full transition-all duration-500 ease-out\"\n                    style={{ width: `${matchScorePercentage}%` }}\n                  />\n                </div>\n              </div>\n            )}\n\n            <div className=\"flex flex-wrap gap-2 text-xs mb-3\">\n              {recommendation.pricing && (\n                <span className=\"flex items-center px-2 py-1 rounded-full bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-700\">\n                  <svg className=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n                  </svg>\n                  {recommendation.pricing}\n                </span>\n              )}\n\n              {recommendation.trial_days && recommendation.trial_days > 0 && (\n                <span className=\"flex items-center px-2 py-1 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900/30 dark:to-cyan-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700\">\n                  <svg className=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6 0h6\" />\n                  </svg>\n                  {recommendation.trial_days}-day trial\n                </span>\n              )}\n            </div>\n\n            {/* Features */}\n            {recommendation.features && recommendation.features.length > 0 && (\n              <div className=\"mb-3\">\n                <div className=\"text-xs text-gray-600 dark:text-gray-300 mb-2 font-medium\">\n                  ✨ Key Features\n                </div>\n                <div className=\"flex flex-wrap gap-1.5\">\n                  {recommendation.features.slice(0, 4).map((feature, j) => (\n                    <span\n                      key={j}\n                      className=\"text-xs px-2 py-1 rounded-full bg-gradient-to-r from-indigo-100 to-purple-100 dark:from-indigo-900/30 dark:to-purple-900/30 text-indigo-700 dark:text-indigo-300 border border-indigo-200 dark:border-indigo-700\"\n                    >\n                      <svg className=\"h-3 w-3 mr-0.5 inline text-indigo-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                      {feature}\n                    </span>\n                  ))}\n                  {recommendation.features.length > 4 && (\n                    <span className=\"text-xs text-gray-500 dark:text-gray-400 px-2 py-1\">\n                      +{recommendation.features.length - 4} more\n                    </span>\n                  )}\n                </div>\n              </div>\n            )}\n\n            {/* Integrations */}\n            {recommendation.integrations && recommendation.integrations.length > 0 && (\n              <div className=\"mb-3\">\n                <div className=\"text-xs text-gray-600 dark:text-gray-300 mb-2 font-medium\">\n                  🔗 Integrations\n                </div>\n                <div className=\"flex flex-wrap gap-1.5\">\n                  {recommendation.integrations.slice(0, 3).map((integration, j) => (\n                    <span\n                      key={j}\n                      className=\"text-xs px-2 py-1 rounded-full bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30 text-orange-700 dark:text-orange-300 border border-orange-200 dark:border-orange-700\"\n                    >\n                      <svg className=\"h-3 w-3 mr-0.5 inline text-orange-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n                      </svg>\n                      {integration}\n                    </span>\n                  ))}\n                  {recommendation.integrations.length > 3 && (\n                    <span className=\"text-xs text-gray-500 dark:text-gray-400 px-2 py-1\">\n                      +{recommendation.integrations.length - 3} more\n                    </span>\n                  )}\n                </div>\n              </div>\n            )}\n\n            {/* Powered by AdMesh branding */}\n            <div className=\"flex justify-end mt-auto pt-2\">\n              <span className=\"text-xs text-gray-400 dark:text-gray-500\">\n                Powered by AdMesh\n              </span>\n            </div>\n          </div>\n        )}\n      </div>\n    );\n  }\n\n  // Default full product card layout\n  return (\n    <div\n      className={cardClasses}\n      style={{\n        fontFamily: theme?.fontFamily || '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n        ...theme?.components?.productCard,\n        ...style\n      }}\n      data-admesh-theme={theme?.mode}\n    >\n      <div\n        className=\"h-full flex flex-col\"\n        style={cardStyle}\n      >\n        {/* Recommendation label at top */}\n        <div className=\"mb-3\">\n          <span\n            style={{\n              fontSize: '11px',\n              fontWeight: '600',\n              color: theme?.accentColor || '#2563eb',\n              backgroundColor: theme?.mode === 'dark' ? '#374151' : '#f3f4f6',\n              padding: '2px 6px',\n              borderRadius: '4px'\n            }}\n            title={getLabelTooltip(recommendation, getRecommendationLabel(recommendation))}\n          >\n            {getRecommendationLabel(recommendation)}\n          </span>\n        </div>\n\n        {/* Header with title */}\n        <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3 mb-4\">\n          <div className=\"flex items-center gap-2 flex-1 min-w-0\">\n            {recommendation.product_logo && (\n              <img\n                src={recommendation.product_logo.url}\n                alt={`${recommendation.title} logo`}\n                className=\"w-6 h-6 rounded flex-shrink-0\"\n                onError={(e) => {\n                  // Hide image if it fails to load\n                  (e.target as HTMLImageElement).style.display = 'none';\n                }}\n              />\n            )}\n            <h4 className=\"font-semibold text-gray-800 dark:text-gray-200 text-sm sm:text-base truncate\">\n              {content.title}\n            </h4>\n          </div>\n\n          <div className=\"flex gap-2 flex-shrink-0\">\n            <AdMeshLinkTracker\n              adId={recommendation.ad_id}\n              admeshLink={recommendation.admesh_link}\n              productId={recommendation.product_id}\n              trackingData={{\n                title: recommendation.title,\n                matchScore: recommendation.intent_match_score,\n                component: 'product_card_cta'\n              }}\n            >\n              <button className=\"text-xs sm:text-sm px-3 py-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600 flex items-center transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg\">\n                Visit {content.ctaText}\n                <svg className=\"ml-1 h-3 w-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\" />\n                </svg>\n              </button>\n            </AdMeshLinkTracker>\n          </div>\n        </div>\n\n        {/* Product Description/Reason */}\n        <div className=\"mb-6\">\n          <p className=\"text-sm text-gray-600 dark:text-gray-300 leading-relaxed\">\n            {content.description}\n          </p>\n        </div>\n\n        {/* Match Score */}\n        {showMatchScore && typeof recommendation.intent_match_score === \"number\" && (\n          <div className=\"mb-6\">\n            <div className=\"flex items-center justify-between text-xs sm:text-sm text-gray-600 dark:text-gray-300 mb-2\">\n              <span className=\"font-medium\">Match Score</span>\n              <span className=\"font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-blue-500 whitespace-nowrap\">{matchScorePercentage}% match</span>\n            </div>\n            <div className=\"w-full bg-gray-200 dark:bg-slate-600 rounded-full h-2 overflow-hidden\">\n              <div\n                className=\"bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full transition-all duration-500 ease-out\"\n                style={{ width: `${matchScorePercentage}%` }}\n              />\n            </div>\n          </div>\n        )}\n\n        <div className=\"flex flex-wrap gap-2 text-xs mb-3\">\n          {recommendation.pricing && (\n            <span className=\"flex items-center px-2 py-1 rounded-full bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-700\">\n              <svg className=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n              </svg>\n              {recommendation.pricing}\n            </span>\n          )}\n\n          {recommendation.trial_days && recommendation.trial_days > 0 && (\n            <span className=\"flex items-center px-2 py-1 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900/30 dark:to-cyan-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700\">\n              <svg className=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6 0h6\" />\n              </svg>\n              {recommendation.trial_days}-day trial\n            </span>\n          )}\n        </div>\n\n        {/* Features */}\n        {recommendation.features && recommendation.features.length > 0 && (\n          <div className=\"mb-3\">\n            <div className=\"text-xs text-gray-600 dark:text-gray-300 mb-2 font-medium\">\n              ✨ Key Features\n            </div>\n            <div className=\"flex flex-wrap gap-1.5\">\n              {recommendation.features.slice(0, 4).map((feature, j) => (\n                <span\n                  key={j}\n                  className=\"text-xs px-2 py-1 rounded-full bg-gradient-to-r from-indigo-100 to-purple-100 dark:from-indigo-900/30 dark:to-purple-900/30 text-indigo-700 dark:text-indigo-300 border border-indigo-200 dark:border-indigo-700\"\n                >\n                  <svg className=\"h-3 w-3 mr-0.5 inline text-indigo-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                  {feature}\n                </span>\n              ))}\n              {recommendation.features.length > 4 && (\n                <span className=\"text-xs text-gray-500 dark:text-gray-400 px-2 py-1\">\n                  +{recommendation.features.length - 4} more\n                </span>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Integrations */}\n        {recommendation.integrations && recommendation.integrations.length > 0 && (\n          <div className=\"mb-3\">\n            <div className=\"text-xs text-gray-600 dark:text-gray-300 mb-2 font-medium\">\n              🔗 Integrations\n            </div>\n            <div className=\"flex flex-wrap gap-1.5\">\n              {recommendation.integrations.slice(0, 3).map((integration, j) => (\n                <span\n                  key={j}\n                  className=\"text-xs px-2 py-1 rounded-full bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30 text-orange-700 dark:text-orange-300 border border-orange-200 dark:border-orange-700\"\n                >\n                  <svg className=\"h-3 w-3 mr-0.5 inline text-orange-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n                  </svg>\n                  {integration}\n                </span>\n              ))}\n              {recommendation.integrations.length > 3 && (\n                <span className=\"text-xs text-gray-500 dark:text-gray-400 px-2 py-1\">\n                  +{recommendation.integrations.length - 3} more\n                </span>\n              )}\n            </div>\n          </div>\n        )}\n\n\n\n\n\n\n\n\n\n        {/* Footer section */}\n        <div className=\"mt-auto pt-3 border-t border-gray-100 dark:border-slate-700\">\n          <div className=\"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400\">\n            <span title={inlineTooltip}>\n              {inlineDisclosure}\n            </span>\n            <span className=\"text-gray-400 dark:text-gray-500\">\n              Powered by AdMesh\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nAdMeshProductCard.displayName = 'AdMeshProductCard';\n", "import React, { useMemo } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshCompareTableProps } from '../types/index';\nimport { AdMeshLinkTracker } from './AdMeshLinkTracker';\nimport { useAdMeshStyles } from '../hooks/useAdMeshStyles';\n\nexport const AdMeshCompareTable: React.FC<AdMeshCompareTableProps> = ({\n  recommendations,\n  theme,\n  maxProducts = 3,\n  showMatchScores = true,\n  showFeatures = true,\n  className,\n  style\n}) => {\n  // Inject styles automatically\n  useAdMeshStyles();\n\n  // Limit the number of products to compare\n  const productsToCompare = useMemo(() => {\n    return recommendations.slice(0, maxProducts);\n  }, [recommendations, maxProducts]);\n\n\n\n  const containerClasses = classNames(\n    'admesh-component',\n    'admesh-compare-layout',\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  if (productsToCompare.length === 0) {\n    return (\n      <div\n        className={containerClasses}\n        style={{\n          ...containerStyle,\n          fontFamily: theme?.fontFamily || '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n          ...theme?.components?.compareTable,\n          ...style\n        }}\n        data-admesh-theme={theme?.mode}\n      >\n        <div className=\"p-8 text-center text-gray-500 dark:text-gray-400\">\n          <p>No products to compare</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div\n      className={containerClasses}\n      style={{\n        ...containerStyle,\n        fontFamily: theme?.fontFamily || '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n        ...theme?.components?.compareTable,\n        ...style\n      }}\n      data-admesh-theme={theme?.mode}\n    >\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"text-center\">\n          <div className=\"flex items-center justify-center gap-2 mb-2\">\n            <svg className=\"w-5 h-5 text-gray-600 dark:text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n            </svg>\n            <h3 className=\"text-lg font-semibold text-gray-800 dark:text-gray-200\">\n              Smart Comparison\n            </h3>\n          </div>\n          <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n            {productsToCompare.length} intelligent matches found\n          </p>\n        </div>\n\n        {/* Product Cards Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {productsToCompare.map((product, index) => (\n            <div\n              key={product.product_id || index}\n              className=\"relative p-4 rounded-lg bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 shadow-sm hover:shadow transition-shadow\"\n            >\n              {/* Product Header */}\n              <div className=\"flex justify-between items-start mb-3\">\n                <div className=\"flex items-center gap-2\">\n                  {index === 0 && (\n                    <span className=\"text-xs font-semibold text-white bg-black px-2 py-0.5 rounded-full\">\n                      Top Match\n                    </span>\n                  )}\n                  <span className=\"text-xs text-gray-400 dark:text-gray-500\">\n                    #{index + 1}\n                  </span>\n                </div>\n                {showMatchScores && (\n                  <div className=\"text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap\">\n                    {Math.round(product.intent_match_score * 100)}% match\n                  </div>\n                )}\n              </div>\n\n              {/* Product Title */}\n              <h4 className=\"font-semibold text-gray-800 dark:text-gray-200 mb-2\">\n                {product.title}\n              </h4>\n\n              {/* Match Score */}\n              {showMatchScores && (\n                <div className=\"mb-3\">\n                  <div className=\"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1\">\n                    <span>Match Score</span>\n                    <span className=\"whitespace-nowrap\">{Math.round(product.intent_match_score * 100)}% match</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 dark:bg-slate-600 rounded h-1.5 overflow-hidden\">\n                    <div\n                      className=\"bg-black h-1.5\"\n                      style={{ width: `${Math.round(product.intent_match_score * 100)}%` }}\n                    />\n                  </div>\n                </div>\n              )}\n\n              {/* Pricing and Trial Info */}\n              <div className=\"flex flex-wrap gap-2 text-xs mb-3\">\n                {product.pricing && (\n                  <span className=\"flex items-center text-gray-600 dark:text-gray-400\">\n                    <svg className=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n                    </svg>\n                    {product.pricing}\n                  </span>\n                )}\n\n\n\n                {product.trial_days && product.trial_days > 0 && (\n                  <span className=\"flex items-center text-gray-600 dark:text-gray-400\">\n                    <svg className=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6 0h6\" />\n                    </svg>\n                    {product.trial_days}-day trial\n                  </span>\n                )}\n              </div>\n\n              {/* Features */}\n              {showFeatures && product.features && product.features.length > 0 && (\n                <div className=\"mb-3\">\n                  <div className=\"text-xs text-gray-500 dark:text-gray-400 mb-1\">\n                    Key Features:\n                  </div>\n                  <div className=\"flex flex-wrap gap-1.5\">\n                    {product.features.slice(0, 4).map((feature, j) => (\n                      <span\n                        key={j}\n                        className=\"text-xs px-2 py-0.5 rounded-full bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-gray-300\"\n                      >\n                        <svg className=\"h-3 w-3 mr-0.5 inline text-gray-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                        </svg>\n                        {feature}\n                      </span>\n                    ))}\n                    {(product.features.length || 0) > 4 && (\n                      <span className=\"text-xs text-gray-500 dark:text-gray-400 italic\">\n                        +{product.features.length - 4} more\n                      </span>\n                    )}\n                  </div>\n                </div>\n              )}\n\n              {/* Visit Button */}\n              <AdMeshLinkTracker\n                adId={product.ad_id}\n                admeshLink={product.admesh_link}\n                productId={product.product_id}\n                trackingData={{\n                  title: product.title,\n                  matchScore: product.intent_match_score,\n                  component: 'compare_table_cta'\n                }}\n              >\n                <button className=\"w-full text-xs px-3 py-2 rounded-lg bg-black text-white hover:bg-gray-800 flex items-center justify-center gap-1 mt-auto transition-colors\">\n                  Visit Offer\n                  <svg className=\"h-3 w-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\" />\n                  </svg>\n                </button>\n              </AdMeshLinkTracker>\n            </div>\n          ))}\n        </div>\n\n        {/* Powered by AdMesh branding */}\n        <div className=\"flex items-center justify-center mt-6 pt-4 border-t border-gray-200/50 dark:border-gray-700/50\">\n          <span className=\"flex items-center gap-1.5 text-xs text-gray-400 dark:text-gray-500\">\n            <svg className=\"w-3 h-3 text-indigo-500\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z\" clipRule=\"evenodd\" />\n            </svg>\n            <span className=\"font-medium\">Powered by</span>\n            <span className=\"font-semibold text-indigo-600 dark:text-indigo-400\">AdMesh</span>\n          </span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nAdMeshCompareTable.displayName = 'AdMeshCompareTable';\n", "import React from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshBadgeProps, BadgeType } from '../types/index';\n\n// Badge type to variant mapping\nconst badgeTypeVariants: Record<BadgeType, string> = {\n  'Top Match': 'primary',\n  'Free Tier': 'success',\n  'AI Powered': 'secondary',\n  'Popular': 'warning',\n  'New': 'primary',\n  'Trial Available': 'success'\n};\n\n// Badge type to icon mapping (using clean Unicode symbols)\nconst badgeTypeIcons: Partial<Record<BadgeType, string>> = {\n  'Top Match': '★',\n  'Free Tier': '◆',\n  'AI Powered': '◉',\n  'Popular': '▲',\n  'New': '●',\n  'Trial Available': '◈'\n};\n\nexport const AdMeshBadge: React.FC<AdMeshBadgeProps> = ({\n  type,\n  variant,\n  size = 'md',\n  className,\n  style\n}) => {\n  const effectiveVariant = variant || badgeTypeVariants[type] || 'secondary';\n  const icon = badgeTypeIcons[type];\n\n  const badgeClasses = classNames(\n    'admesh-component',\n    'admesh-badge',\n    `admesh-badge--${effectiveVariant}`,\n    `admesh-badge--${size}`,\n    className\n  );\n\n  return (\n    <span\n      className={badgeClasses}\n      style={style}\n    >\n      {icon && <span className=\"admesh-badge__icon\">{icon}</span>}\n      <span className=\"admesh-badge__text\">{type}</span>\n    </span>\n  );\n};\n\nAdMeshBadge.displayName = 'AdMeshBadge';\n", "import React, { useState } from 'react';\nimport type { AdMeshRecommendation, AdMeshTheme } from '../types';\nimport { AdMeshLinkTracker } from './AdMeshLinkTracker';\nimport { useAdMeshStyles } from '../hooks/useAdMeshStyles';\nimport {\n  getInlineDisclosure,\n  getInlineTooltip\n} from '../utils/disclosureUtils';\n\nexport interface AdMeshExpandableUnitProps {\n  /** Product recommendation data */\n  recommendation: AdMeshRecommendation;\n  /** Theme configuration */\n  theme?: AdMeshTheme;\n  /** Custom CSS class name */\n  className?: string;\n  /** Custom inline styles */\n  style?: React.CSSProperties;\n  /** Show \"powered by AdMesh\" branding */\n  showPoweredBy?: boolean;\n  /** Initial expanded state */\n  initialExpanded?: boolean;\n  /** Custom sections to display */\n  sections?: {\n    title: string;\n    description: string;\n    icon?: string;\n  }[];\n  /** Custom call-to-action text */\n  ctaText?: string;\n  /** Show collapse/expand functionality */\n  collapsible?: boolean;\n}\n\n/**\n * AdMeshExpandableUnit - A comprehensive ad unit with expandable sections\n * \n * Similar to the Temporal ad format, this component displays:\n * - Header with product name and sponsor info\n * - Multiple expandable sections with descriptions\n * - Primary call-to-action button\n * - Optional powered by branding\n */\nexport const AdMeshExpandableUnit: React.FC<AdMeshExpandableUnitProps> = ({\n  recommendation,\n  theme = { mode: 'light' },\n  className = '',\n  style,\n  showPoweredBy = true,\n  initialExpanded = false,\n  sections,\n  ctaText,\n  collapsible = true\n}) => {\n  // Inject styles automatically\n  useAdMeshStyles();\n\n  const [isExpanded, setIsExpanded] = useState(initialExpanded);\n\n\n\n  const handleToggleExpand = () => {\n    if (collapsible) {\n      setIsExpanded(!isExpanded);\n    }\n  };\n\n  // Use feature sections from API if available, otherwise use default sections\n  const apiFeatureSections = recommendation.feature_sections || [];\n\n  const defaultSections = [\n    {\n      title: 'Documentation',\n      description: `Learn more about ${recommendation.recommendation_title || recommendation.title}. Start exploring the features and capabilities.`,\n      icon: '◆'\n    },\n    {\n      title: 'Talk To An Expert',\n      description: `Ready to learn more about ${recommendation.recommendation_title || recommendation.title}? Reach out to a platform specialist for personalized guidance.`,\n      icon: '◉'\n    },\n    {\n      title: `${recommendation.recommendation_title || recommendation.title} Features`,\n      description: recommendation.recommendation_description || recommendation.description || `${recommendation.recommendation_title || recommendation.title} offers comprehensive solutions for your needs. Discover the full potential.`,\n      icon: '▲'\n    },\n    {\n      title: 'How it Works',\n      description: `Learn how to get started with ${recommendation.recommendation_title || recommendation.title}. Begin your journey today.`,\n      icon: '●'\n    }\n  ];\n\n  // Get compliant labels and disclosures\n  const inlineDisclosure = getInlineDisclosure(recommendation, false);\n  const inlineTooltip = getInlineTooltip();\n\n  // Prioritize: custom sections > API feature sections > default sections\n  const displaySections = sections || (apiFeatureSections.length > 0 ? apiFeatureSections : defaultSections);\n  const displayCtaText = ctaText || `Try ${recommendation.recommendation_title || recommendation.title}`;\n\n  // Clean, professional color scheme with customization support\n  const colors = {\n    background: theme.backgroundColor || (theme.mode === 'dark' ? '#1f2937' : '#ffffff'),\n    surface: theme.surfaceColor || (theme.mode === 'dark' ? '#374151' : '#f9fafb'),\n    border: theme.borderColor || (theme.mode === 'dark' ? '#4b5563' : '#e5e7eb'),\n    text: theme.textColor || (theme.mode === 'dark' ? '#f9fafb' : '#111827'),\n    textSecondary: theme.textSecondaryColor || (theme.mode === 'dark' ? '#9ca3af' : '#6b7280'),\n    accent: theme.accentColor || theme.primaryColor || '#3b82f6',\n    secondary: theme.secondaryColor || '#10b981',\n    // Remove excessive gradients, use clean solid colors or subtle gradients\n    headerBg: theme.gradients?.primary || (theme.mode === 'dark' ? '#374151' : '#f8fafc'),\n    sectionBg: theme.gradients?.secondary || (theme.mode === 'dark' ? '#4b5563' : '#ffffff')\n  };\n\n  // Get custom styles if provided\n  const customStyles = theme.disableDefaultStyles ? {} : {\n    fontFamily: theme.fontFamily || '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n    borderRadius: theme.borderRadius || '12px',\n    border: `1px solid ${colors.border}`,\n    background: colors.background,\n    overflow: 'hidden',\n    maxWidth: '420px',\n    boxShadow: theme.shadows?.medium || (theme.mode === 'dark'\n      ? '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)'\n      : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'),\n    position: 'relative' as const,\n    transition: 'all 0.2s ease'\n  };\n\n  return (\n    <div\n      className={`admesh-component admesh-expandable-unit ${className}`}\n      style={{\n        ...customStyles,\n        ...theme.components?.expandableUnit,\n        ...style\n      }}\n      data-admesh-theme={theme.mode}\n    >\n      {/* Header */}\n      <div\n        style={{\n          background: colors.headerBg,\n          padding: '20px',\n          borderBottom: isExpanded || !collapsible ? `1px solid ${colors.border}` : 'none',\n          position: 'relative',\n          transition: 'all 0.2s ease'\n        }}\n      >\n        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', gap: '16px' }}>\n          <div style={{ display: 'flex', alignItems: 'center', gap: '16px', flex: 1, minWidth: 0 }}>\n            <div\n              style={{\n                width: '40px',\n                height: '40px',\n                borderRadius: theme.borderRadius || '8px',\n                background: colors.accent,\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                color: 'white',\n                fontSize: theme.fontSize?.base || '16px',\n                fontWeight: '600',\n                boxShadow: theme.shadows?.small || '0 2px 4px rgba(0, 0, 0, 0.1)',\n                border: `1px solid ${colors.border}`\n              }}\n            >\n              {(recommendation.recommendation_title || recommendation.title).charAt(0).toUpperCase()}\n            </div>\n            <div style={{ flex: 1, minWidth: 0 }}>\n              <h3\n                style={{\n                  margin: 0,\n                  fontSize: '18px',\n                  fontWeight: '600',\n                  color: colors.text,\n                  lineHeight: '1.4',\n                  overflow: 'hidden',\n                  textOverflow: 'ellipsis',\n                  whiteSpace: 'nowrap'\n                }}\n              >\n                {recommendation.recommendation_title || recommendation.title}\n              </h3>\n              <p\n                style={{\n                  margin: '8px 0 0 0',\n                  fontSize: '13px',\n                  color: colors.textSecondary,\n                  fontWeight: '400',\n                  overflow: 'hidden',\n                  textOverflow: 'ellipsis',\n                  whiteSpace: 'nowrap'\n                }}\n                title={inlineTooltip}\n              >\n                {inlineDisclosure} • {new URL(recommendation.url || recommendation.admesh_link).hostname}\n              </p>\n            </div>\n          </div>\n\n          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n            {/* CTA Button when collapsed */}\n            {!isExpanded && collapsible && (\n              <AdMeshLinkTracker\n                adId={recommendation.ad_id}\n                admeshLink={recommendation.admesh_link}\n                productId={recommendation.product_id}\n                trackingData={{\n                  title: recommendation.recommendation_title || recommendation.title,\n                  component: 'expandable_unit',\n                  expanded: false,\n                  location: 'header'\n                }}\n              >\n                <button\n                  style={{\n                    padding: theme.spacing?.small ? `${theme.spacing.small} ${theme.spacing.medium || '12px'}` : '6px 12px',\n                    backgroundColor: colors.accent,\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: theme.borderRadius || '6px',\n                    fontSize: theme.fontSize?.small || '12px',\n                    fontWeight: '500',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease',\n                    boxShadow: theme.shadows?.small || '0 1px 3px rgba(0, 0, 0, 0.1)',\n                    whiteSpace: 'nowrap',\n                    ...theme.components?.button\n                  }}\n                  onMouseOver={(e) => {\n                    if (!theme.disableDefaultStyles) {\n                      e.currentTarget.style.transform = 'translateY(-1px)';\n                      e.currentTarget.style.boxShadow = theme.shadows?.medium || '0 2px 6px rgba(0, 0, 0, 0.15)';\n                    }\n                  }}\n                  onMouseOut={(e) => {\n                    if (!theme.disableDefaultStyles) {\n                      e.currentTarget.style.transform = 'translateY(0)';\n                      e.currentTarget.style.boxShadow = theme.shadows?.small || '0 1px 3px rgba(0, 0, 0, 0.1)';\n                    }\n                  }}\n                >\n                  {displayCtaText}\n                </button>\n              </AdMeshLinkTracker>\n            )}\n\n            {/* Modern Expand/Collapse button */}\n            {collapsible && (\n              <button\n                onClick={handleToggleExpand}\n                style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '8px',\n                  padding: '8px 12px',\n                  background: theme.mode === 'dark' ? '#374151' : '#f3f4f6',\n                  border: `1px solid ${theme.mode === 'dark' ? '#4b5563' : '#d1d5db'}`,\n                  borderRadius: '8px',\n                  cursor: 'pointer',\n                  color: theme.accentColor || '#2563eb',\n                  fontSize: '12px',\n                  fontWeight: '600',\n                  transition: 'all 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.background = theme.mode === 'dark' ? '#4b5563' : '#e5e7eb';\n                  e.currentTarget.style.borderColor = theme.accentColor || '#2563eb';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.background = theme.mode === 'dark' ? '#374151' : '#f3f4f6';\n                  e.currentTarget.style.borderColor = theme.mode === 'dark' ? '#4b5563' : '#d1d5db';\n                }}\n                aria-label={isExpanded ? 'Show less details' : 'Show more details'}\n              >\n                <span>{isExpanded ? 'Less Details' : 'More Details'}</span>\n                <svg\n                  width=\"16\"\n                  height=\"16\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  strokeWidth=\"2\"\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                >\n                  {isExpanded ? (\n                    // Minus icon for collapse\n                    <path d=\"M5 12h14\" />\n                  ) : (\n                    // Info icon for expand\n                    <>\n                      <circle cx=\"12\" cy=\"12\" r=\"10\" />\n                      <path d=\"M12 16v-4\" />\n                      <path d=\"M12 8h.01\" />\n                    </>\n                  )}\n                </svg>\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Expandable Content */}\n      {(isExpanded || !collapsible) && (\n        <div style={{ padding: '0' }}>\n          {/* Sections */}\n          {displaySections.map((section, index) => (\n            <div\n              key={index}\n              style={{\n                padding: '24px',\n                backgroundColor: index % 2 === 0 ? colors.background : colors.sectionBg,\n                borderBottom: index < displaySections.length - 1 ? `1px solid ${colors.border}` : 'none'\n              }}\n            >\n              <h4\n                style={{\n                  margin: '0 0 12px 0',\n                  fontSize: '15px',\n                  fontWeight: '600',\n                  color: colors.text,\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '12px'\n                }}\n              >\n                {section.icon && <span>{section.icon}</span>}\n                {section.title}\n              </h4>\n              <p\n                style={{\n                  margin: 0,\n                  fontSize: '14px',\n                  color: colors.textSecondary,\n                  lineHeight: '1.6'\n                }}\n              >\n                {section.description}\n              </p>\n            </div>\n          ))}\n\n          {/* CTA Button - only show when expanded or when not collapsible */}\n          {(isExpanded || !collapsible) && (\n            <div style={{ padding: '24px', borderTop: `1px solid ${colors.border}`, backgroundColor: colors.background }}>\n              <AdMeshLinkTracker\n                adId={recommendation.ad_id}\n                admeshLink={recommendation.admesh_link}\n                productId={recommendation.product_id}\n                trackingData={{\n                  title: recommendation.title,\n                  component: 'expandable_unit',\n                  expanded: isExpanded,\n                  location: 'footer'\n                }}\n              >\n                <button\n                  style={{\n                    width: '100%',\n                    padding: '14px 28px',\n                    background: colors.accent,\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: '12px',\n                    fontSize: '15px',\n                    fontWeight: '600',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    boxShadow: '0 4px 12px rgba(99, 102, 241, 0.3)',\n                    position: 'relative',\n                    overflow: 'hidden'\n                  }}\n                  onMouseOver={(e) => {\n                    e.currentTarget.style.transform = 'translateY(-2px) scale(1.02)';\n                    e.currentTarget.style.boxShadow = '0 8px 20px rgba(99, 102, 241, 0.4)';\n                  }}\n                  onMouseOut={(e) => {\n                    e.currentTarget.style.transform = 'translateY(0) scale(1)';\n                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(99, 102, 241, 0.3)';\n                  }}\n                >\n                  {displayCtaText}\n                </button>\n              </AdMeshLinkTracker>\n            </div>\n          )}\n\n          {/* Powered by AdMesh */}\n          {showPoweredBy && (\n            <div\n              style={{\n                padding: '8px 16px',\n                borderTop: `1px solid ${colors.border}`,\n                backgroundColor: colors.headerBg\n              }}\n            >\n              <div\n                style={{\n                  fontSize: '11px',\n                  color: colors.textSecondary,\n                  textAlign: 'center' as const\n                }}\n              >\n                powered by <strong style={{ color: colors.text }}>AdMesh</strong>\n              </div>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AdMeshExpandableUnit;\n", "import React from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshInlineRecommendationProps } from '../types/index';\nimport { AdMeshLinkTracker } from './AdMeshLinkTracker';\nimport {\n  getInlineDisclosure,\n  getInlineTooltip\n} from '../utils/disclosureUtils';\n\nexport const AdMeshInlineRecommendation: React.FC<AdMeshInlineRecommendationProps> = ({\n  recommendation,\n  theme,\n  compact = false,\n  showReason = true,\n  className,\n  style\n}) => {\n  const matchScorePercentage = Math.round(recommendation.intent_match_score * 100);\n\n  // Get compliant labels and disclosures\n  const inlineDisclosure = getInlineDisclosure(recommendation, compact);\n  const inlineTooltip = getInlineTooltip();\n\n  const containerClasses = classNames(\n    'admesh-inline-recommendation',\n    'group cursor-pointer transition-all duration-200',\n    {\n      'p-2 sm:p-3 rounded-md bg-gray-50 dark:bg-slate-800/50 hover:bg-gray-100 dark:hover:bg-slate-800 border border-gray-200 dark:border-slate-700': !compact,\n      'p-1.5 sm:p-2 rounded hover:bg-gray-50 dark:hover:bg-slate-800/30': compact,\n    },\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <AdMeshLinkTracker\n      adId={recommendation.ad_id}\n      admeshLink={recommendation.admesh_link}\n      productId={recommendation.product_id}\n      trackingData={{\n        title: recommendation.title,\n        matchScore: recommendation.intent_match_score\n      }}\n      className={containerClasses}\n      style={{\n        fontFamily: theme?.fontFamily || '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n        ...theme?.components?.inlineRecommendation,\n        ...style\n      }}\n    >\n      <div\n        className=\"flex items-start gap-3\"\n        style={containerStyle}\n        data-admesh-theme={theme?.mode}\n      >\n        {/* Icon/Badge */}\n        <div className=\"flex-shrink-0 mt-0.5\">\n          {recommendation.offer_images && recommendation.offer_images.length > 0 ? (\n            <div className=\"w-6 h-6 rounded-full overflow-hidden border border-gray-200 dark:border-gray-600\">\n              <img\n                src={recommendation.offer_images[0].url}\n                alt={recommendation.recommendation_title || recommendation.title}\n                className=\"w-full h-full object-cover\"\n              />\n            </div>\n          ) : recommendation.product_logo ? (\n            <div className=\"w-6 h-6 rounded-full overflow-hidden border border-gray-200 dark:border-gray-600\">\n              <img\n                src={recommendation.product_logo.url}\n                alt={recommendation.recommendation_title || recommendation.title}\n                className=\"w-full h-full object-cover\"\n              />\n            </div>\n          ) : recommendation.intent_match_score >= 0.8 ? (\n            <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n          ) : (\n            <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n          )}\n        </div>\n\n        {/* Content */}\n        <div className=\"flex-1 min-w-0\">\n          <div className=\"flex items-start sm:items-center gap-2 mb-1 flex-col sm:flex-row\">\n            <h4 className={classNames(\n              'font-medium transition-colors duration-200 flex-shrink-0',\n              'text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300',\n              'cursor-pointer hover:underline',\n              compact ? 'text-sm sm:text-base' : 'text-base sm:text-lg'\n            )}>\n              {recommendation.recommendation_title || recommendation.title}\n            </h4>\n            \n            {/* Match score badge */}\n            {recommendation.intent_match_score >= 0.7 && (\n              <span className={classNames(\n                'inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium flex-shrink-0 whitespace-nowrap',\n                recommendation.intent_match_score >= 0.8\n                  ? 'bg-green-100 text-green-800 dark:bg-green-800/80 dark:text-green-100'\n                  : 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'\n              )}>\n                {matchScorePercentage}% match\n              </span>\n            )}\n          </div>\n\n          {/* Reason/Description */}\n          {showReason && (recommendation.recommendation_description || recommendation.reason) && (\n            <p className={classNames(\n              'text-gray-600 dark:text-gray-400 line-clamp-2',\n              compact ? 'text-xs' : 'text-sm'\n            )}>\n              {recommendation.recommendation_description || recommendation.reason}\n            </p>\n          )}\n\n          {/* Disclosure */}\n          <p\n            className={classNames(\n              'text-gray-500 dark:text-gray-400 mt-1',\n              compact ? 'text-xs' : 'text-xs'\n            )}\n            title={inlineTooltip}\n          >\n            {inlineDisclosure}\n          </p>\n\n          {/* Features/Keywords */}\n          {!compact && recommendation.keywords && recommendation.keywords.length > 0 && (\n            <div className=\"flex flex-wrap gap-1 mt-2\">\n              {recommendation.keywords.slice(0, 3).map((keyword, index) => (\n                <span\n                  key={index}\n                  className=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-slate-700 dark:text-gray-300\"\n                >\n                  {keyword}\n                </span>\n              ))}\n              {recommendation.keywords.length > 3 && (\n                <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                  +{recommendation.keywords.length - 3} more\n                </span>\n              )}\n            </div>\n          )}\n\n          {/* Pricing/Trial info */}\n          {!compact && recommendation.trial_days && recommendation.trial_days > 0 && (\n            <div className=\"flex items-center gap-2 mt-2\">\n              <span className=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400\">\n                {recommendation.trial_days}-day trial\n              </span>\n            </div>\n          )}\n        </div>\n\n        {/* Arrow indicator */}\n        <div className=\"flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity\">\n          <svg \n            className=\"w-4 h-4 text-gray-400 dark:text-gray-500\" \n            fill=\"none\" \n            stroke=\"currentColor\" \n            viewBox=\"0 0 24 24\"\n          >\n            <path \n              strokeLinecap=\"round\" \n              strokeLinejoin=\"round\" \n              strokeWidth={2} \n              d=\"M9 5l7 7-7 7\" \n            />\n          </svg>\n        </div>\n      </div>\n    </AdMeshLinkTracker>\n  );\n};\n", "import React from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshConversationSummaryProps } from '../types/index';\nimport { AdMeshInlineRecommendation } from './AdMeshInlineRecommendation';\n\nexport const AdMeshConversationSummary: React.FC<AdMeshConversationSummaryProps> = ({\n  recommendations,\n  conversationSummary,\n  theme,\n  showTopRecommendations = 3,\n  onRecommendationClick,\n  onStartNewConversation,\n  className\n}) => {\n  const topRecommendations = recommendations\n    .sort((a, b) => b.intent_match_score - a.intent_match_score)\n    .slice(0, showTopRecommendations);\n\n  const containerClasses = classNames(\n    'admesh-conversation-summary',\n    'bg-white dark:bg-black',\n    'rounded-lg border border-gray-200 dark:border-gray-800 p-4 sm:p-6',\n    'font-sans', // Standardize font family\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Header */}\n      <div className=\"flex items-center gap-3 mb-4\">\n        <div className=\"flex-shrink-0\">\n          <div className=\"w-8 h-8 sm:w-10 sm:h-10 bg-black dark:bg-white rounded-full flex items-center justify-center\">\n            <svg className=\"w-4 h-4 sm:w-5 sm:h-5 text-white dark:text-black\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n          </div>\n        </div>\n        <div className=\"min-w-0 flex-1\">\n          <h3 className=\"text-base sm:text-lg font-semibold text-black dark:text-white\">\n            Conversation Summary\n          </h3>\n          <p className=\"text-xs sm:text-sm text-gray-600 dark:text-gray-300\">\n            Here's what we discussed and found for you\n          </p>\n        </div>\n      </div>\n\n      {/* Summary Text */}\n      <div className=\"mb-6\">\n        <div className=\"bg-gray-50 dark:bg-gray-900 rounded-lg p-4 border border-gray-200 dark:border-gray-700\">\n          <p className=\"text-gray-800 dark:text-gray-200 leading-relaxed\">\n            {conversationSummary}\n          </p>\n        </div>\n      </div>\n\n      {/* Top Recommendations */}\n      {topRecommendations.length > 0 && (\n        <div className=\"mb-6\">\n          <div className=\"flex items-center gap-2 mb-3\">\n            <svg className=\"w-5 h-5 text-black dark:text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n            </svg>\n            <h4 className=\"font-medium text-black dark:text-white\">\n              Top Recommendations\n            </h4>\n          </div>\n          \n          <div className=\"space-y-2\">\n            {topRecommendations.map((recommendation, index) => (\n              <div key={recommendation.ad_id || index} className=\"relative\">\n                {/* Ranking badge */}\n                <div className=\"absolute -left-2 top-2 z-10\">\n                  <div className={classNames(\n                    'w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold',\n                    index === 0 ? 'bg-black dark:bg-white text-white dark:text-black' :\n                    index === 1 ? 'bg-gray-600 dark:bg-gray-400 text-white dark:text-black' :\n                    'bg-gray-800 dark:bg-gray-200 text-white dark:text-black'\n                  )}>\n                    {index + 1}\n                  </div>\n                </div>\n                \n                <div className=\"ml-4\">\n                  <AdMeshInlineRecommendation\n                    recommendation={recommendation}\n                    theme={theme}\n                    compact={true}\n                    showReason={true}\n                    onClick={onRecommendationClick}\n                  />\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Additional Insights */}\n      {recommendations.length > showTopRecommendations && (\n        <div className=\"mb-6\">\n          <div className=\"bg-gray-100 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700\">\n            <div className=\"flex items-center gap-2\">\n              <svg className=\"w-4 h-4 text-black dark:text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n              <span className=\"text-sm font-medium text-gray-800 dark:text-gray-200\">\n                {recommendations.length - showTopRecommendations} additional recommendation{recommendations.length - showTopRecommendations > 1 ? 's' : ''} available\n              </span>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Action Buttons */}\n      <div className=\"flex flex-col sm:flex-row gap-3\">\n        {onStartNewConversation && (\n          <button\n            onClick={onStartNewConversation}\n            className=\"flex-1 bg-black dark:bg-white hover:bg-gray-800 dark:hover:bg-gray-100 text-white dark:text-black font-medium py-2.5 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n            </svg>\n            Start New Conversation\n          </button>\n        )}\n        \n        <button\n          onClick={() => {\n            if (topRecommendations.length > 0) {\n              onRecommendationClick?.(topRecommendations[0].ad_id, topRecommendations[0].admesh_link);\n            }\n          }}\n          className=\"flex-1 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 text-black dark:text-white font-medium py-2.5 px-4 rounded-lg border border-gray-300 dark:border-gray-600 transition-all duration-200 flex items-center justify-center gap-2\"\n        >\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\" />\n          </svg>\n          View Top Pick\n        </button>\n      </div>\n\n      {/* Powered by AdMesh */}\n      <div className=\"flex justify-center mt-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n          Powered by AdMesh\n        </span>\n      </div>\n    </div>\n  );\n};\n", "import React, { useState } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshCitationReferenceProps } from '../types/index';\nimport { AdMeshLinkTracker } from './AdMeshLinkTracker';\n\nexport const AdMeshCitationReference: React.FC<AdMeshCitationReferenceProps> = ({\n  recommendation,\n  citationNumber,\n  citationStyle = 'numbered',\n  theme,\n  showTooltip = true,\n  onHover,\n  className,\n  style\n}) => {\n  const [isHovered, setIsHovered] = useState(false);\n\n  const handleMouseEnter = () => {\n    setIsHovered(true);\n    onHover?.(recommendation);\n  };\n\n  const handleMouseLeave = () => {\n    setIsHovered(false);\n  };\n\n\n\n  // Generate citation display based on style\n  const getCitationDisplay = () => {\n    switch (citationStyle) {\n      case 'bracketed':\n        return `[${citationNumber}]`;\n      case 'superscript':\n        return citationNumber.toString();\n      case 'numbered':\n      default:\n        return citationNumber.toString();\n    }\n  };\n\n  const citationClasses = classNames(\n    'admesh-citation-reference',\n    'inline-flex items-center justify-center',\n    'cursor-pointer transition-all duration-200',\n    'text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300',\n    'font-medium',\n    {\n      // Numbered style (default)\n      'w-5 h-5 bg-blue-100 dark:bg-blue-900/30 rounded-full text-xs border border-blue-300 dark:border-blue-700 hover:bg-blue-200 dark:hover:bg-blue-900/50': citationStyle === 'numbered',\n      \n      // Bracketed style\n      'px-1 text-sm hover:underline': citationStyle === 'bracketed',\n      \n      // Superscript style\n      'text-xs align-super hover:underline': citationStyle === 'superscript',\n    },\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <span className=\"relative inline-block\">\n      <AdMeshLinkTracker\n        adId={recommendation.ad_id}\n        admeshLink={recommendation.admesh_link}\n        productId={recommendation.product_id}\n        trackingData={{\n          title: recommendation.title,\n          matchScore: recommendation.intent_match_score,\n          citationNumber,\n          citationStyle\n        }}\n        className={citationClasses}\n        style={style}\n      >\n        <span\n          style={containerStyle}\n          data-admesh-theme={theme?.mode}\n          onMouseEnter={handleMouseEnter}\n          onMouseLeave={handleMouseLeave}\n        >\n          {getCitationDisplay()}\n        </span>\n      </AdMeshLinkTracker>\n\n      {/* Tooltip */}\n      {showTooltip && isHovered && (\n        <div className=\"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-50\">\n          <div className=\"bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs rounded-lg px-3 py-2 shadow-lg max-w-xs\">\n            <div className=\"font-semibold mb-1\">{recommendation.title}</div>\n            {recommendation.reason && (\n              <div className=\"text-gray-300 dark:text-gray-600 text-xs\">\n                {recommendation.reason.length > 100 \n                  ? `${recommendation.reason.substring(0, 100)}...` \n                  : recommendation.reason\n                }\n              </div>\n            )}\n            {recommendation.intent_match_score >= 0.7 && (\n              <div className=\"text-green-400 dark:text-green-600 text-xs mt-1\">\n                {Math.round(recommendation.intent_match_score * 100)}% match\n              </div>\n            )}\n            <div className=\"text-gray-400 dark:text-gray-500 text-xs mt-1 italic\">\n              Click to visit product page\n            </div>\n            {/* Tooltip arrow */}\n            <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-100\"></div>\n          </div>\n        </div>\n      )}\n    </span>\n  );\n};\n", "import React, { useState, useMemo } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshCitationUnitProps, AdMeshRecommendation } from '../types/index';\nimport { AdMeshCitationReference } from './AdMeshCitationReference';\nimport { AdMeshInlineRecommendation } from './AdMeshInlineRecommendation';\n\nexport const AdMeshCitationUnit: React.FC<AdMeshCitationUnitProps> = ({\n  recommendations,\n  conversationText,\n  theme,\n  showCitationList = true,\n  citationStyle = 'numbered',\n  onCitationHover,\n  className,\n  style\n}) => {\n  const [hoveredRecommendation, setHoveredRecommendation] = useState<AdMeshRecommendation | null>(null);\n\n  // Process conversation text to insert citations\n  const processedContent = useMemo(() => {\n    if (!conversationText || recommendations.length === 0) {\n      return { text: conversationText, citationMap: new Map() };\n    }\n\n    let processedText = conversationText;\n    const citationMap = new Map();\n    \n    // Sort recommendations by intent match score (highest first)\n    const sortedRecommendations = [...recommendations]\n      .sort((a, b) => b.intent_match_score - a.intent_match_score);\n\n    // Find mentions of product titles in the text and replace with citations\n    sortedRecommendations.forEach((recommendation, index) => {\n      const citationNumber = index + 1;\n      const title = recommendation.title;\n      \n      // Create citation reference\n      citationMap.set(citationNumber, recommendation);\n      \n      // Look for exact title matches (case insensitive)\n      const titleRegex = new RegExp(`\\\\b${title.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&')}\\\\b`, 'gi');\n      \n      // Replace first occurrence with citation\n      if (titleRegex.test(processedText)) {\n        processedText = processedText.replace(titleRegex, (match) => {\n          return `${match}{{CITATION_${citationNumber}}}`;\n        });\n      } else {\n        // If no exact match, try to find a good insertion point\n        // Look for related keywords or add at the end of relevant sentences\n        const keywords = recommendation.keywords || [];\n        let inserted = false;\n        \n        for (const keyword of keywords) {\n          const keywordRegex = new RegExp(`\\\\b${keyword.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&')}\\\\b`, 'gi');\n          if (keywordRegex.test(processedText) && !inserted) {\n            processedText = processedText.replace(keywordRegex, (match) => {\n              inserted = true;\n              return `${match}{{CITATION_${citationNumber}}}`;\n            });\n            break;\n          }\n        }\n        \n        // If still no insertion point found, add citation at the end\n        if (!inserted) {\n          processedText += `{{CITATION_${citationNumber}}}`;\n        }\n      }\n    });\n\n    return { text: processedText, citationMap };\n  }, [conversationText, recommendations]);\n\n  // Render text with embedded citations\n  const renderTextWithCitations = () => {\n    const { text, citationMap } = processedContent;\n    const parts = text.split(/(\\{\\{CITATION_\\d+\\}\\})/);\n\n    return parts.map((part, index) => {\n      const citationMatch = part.match(/\\{\\{CITATION_(\\d+)\\}\\}/);\n\n      if (citationMatch) {\n        const citationNumber = parseInt(citationMatch[1]);\n        const recommendation = citationMap.get(citationNumber);\n\n        if (recommendation) {\n          return (\n            <AdMeshCitationReference\n              key={`citation-${citationNumber}-${index}`}\n              recommendation={recommendation}\n              citationNumber={citationNumber}\n              citationStyle={citationStyle}\n              theme={theme}\n              showTooltip={true}\n              onHover={(rec) => {\n                setHoveredRecommendation(rec);\n                onCitationHover?.(rec);\n              }}\n            />\n          );\n        }\n      }\n\n      return <span key={index}>{part}</span>;\n    });\n  };\n\n  const containerClasses = classNames(\n    'admesh-citation-unit',\n    'space-y-4',\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={containerClasses}\n      style={{\n        ...containerStyle,\n        fontFamily: theme?.fontFamily || '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n        ...theme?.components?.citationUnit,\n        ...style\n      }}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Main conversation text with embedded citations */}\n      <div className=\"admesh-citation-text text-gray-800 dark:text-gray-200 leading-relaxed\">\n        {renderTextWithCitations()}\n      </div>\n\n      {/* Citation list/references */}\n      {showCitationList && recommendations.length > 0 && (\n        <div className=\"admesh-citation-list\">\n          <div className=\"border-t border-gray-200 dark:border-slate-700 pt-4\">\n            <h4 className=\"text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2\">\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1\" />\n              </svg>\n              References\n            </h4>\n            \n            <div className=\"space-y-2\">\n              {recommendations\n                .sort((a, b) => b.intent_match_score - a.intent_match_score)\n                .map((recommendation, index) => (\n                  <div \n                    key={recommendation.ad_id || index}\n                    className={classNames(\n                      'flex items-start gap-3 p-2 rounded-lg transition-colors duration-200',\n                      {\n                        'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800': \n                          hoveredRecommendation?.ad_id === recommendation.ad_id,\n                        'hover:bg-gray-50 dark:hover:bg-slate-800/50': \n                          hoveredRecommendation?.ad_id !== recommendation.ad_id\n                      }\n                    )}\n                  >\n                    {/* Citation number */}\n                    <div className=\"flex-shrink-0 mt-1\">\n                      <AdMeshCitationReference\n                        recommendation={recommendation}\n                        citationNumber={index + 1}\n                        citationStyle={citationStyle}\n                        theme={theme}\n                        showTooltip={false}\n                      />\n                    </div>\n                    \n                    {/* Recommendation details */}\n                    <div className=\"flex-1 min-w-0\">\n                      <AdMeshInlineRecommendation\n                        recommendation={recommendation}\n                        theme={theme}\n                        compact={true}\n                        showReason={false}\n                      />\n                    </div>\n                  </div>\n                ))}\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n", "import React, { useState, useEffect } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshConversationalUnitProps } from '../types/index';\nimport { AdMeshInlineRecommendation } from './AdMeshInlineRecommendation';\nimport { AdMeshConversationSummary } from './AdMeshConversationSummary';\nimport { AdMeshProductCard } from './AdMeshProductCard';\nimport { AdMeshCitationUnit } from './AdMeshCitationUnit';\n\nexport const AdMeshConversationalUnit: React.FC<AdMeshConversationalUnitProps> = ({\n  recommendations,\n  config,\n  theme,\n  conversationSummary,\n  sessionId,\n  onRecommendationClick,\n  onDismiss,\n  className\n}) => {\n  const [isVisible, setIsVisible] = useState(config.autoShow !== false);\n  const [hasAnimated, setHasAnimated] = useState(false);\n\n  useEffect(() => {\n    if (config.delayMs && config.delayMs > 0) {\n      const timer = setTimeout(() => {\n        setIsVisible(true);\n        setHasAnimated(true);\n      }, config.delayMs);\n      return () => clearTimeout(timer);\n    } else {\n      setHasAnimated(true);\n    }\n  }, [config.delayMs]);\n\n  if (!isVisible || recommendations.length === 0) {\n    return null;\n  }\n\n  const maxRecommendations = config.maxRecommendations || 3;\n  const displayRecommendations = recommendations.slice(0, maxRecommendations);\n\n  const handleRecommendationClick = (adId: string, admeshLink: string) => {\n    onRecommendationClick?.(adId, admeshLink);\n  };\n\n  const handleDismiss = () => {\n    setIsVisible(false);\n    onDismiss?.();\n  };\n\n  // Render based on display mode\n  const renderContent = () => {\n    switch (config.displayMode) {\n      case 'summary':\n        return conversationSummary ? (\n          <AdMeshConversationSummary\n            recommendations={displayRecommendations}\n            conversationSummary={conversationSummary}\n            theme={theme}\n            showTopRecommendations={maxRecommendations}\n            onRecommendationClick={handleRecommendationClick}\n            onStartNewConversation={onDismiss}\n          />\n        ) : null;\n\n      case 'inline':\n        return (\n          <div className=\"space-y-2\">\n            {displayRecommendations.map((recommendation, index) => (\n              <AdMeshInlineRecommendation\n                key={recommendation.ad_id || index}\n                recommendation={recommendation}\n                theme={theme}\n                compact={true}\n                showReason={true}\n                onClick={handleRecommendationClick}\n              />\n            ))}\n          </div>\n        );\n\n      case 'minimal':\n        return displayRecommendations.length > 0 ? (\n          <div className=\"admesh-minimal-unit\">\n            <div className=\"flex items-center gap-2 mb-2\">\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n              <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                {displayRecommendations.length} intelligent match{displayRecommendations.length > 1 ? 'es' : ''} found\n              </span>\n            </div>\n            <AdMeshInlineRecommendation\n              recommendation={displayRecommendations[0]}\n              theme={theme}\n              compact={true}\n              showReason={false}\n              onClick={handleRecommendationClick}\n            />\n            {displayRecommendations.length > 1 && (\n              <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                +{displayRecommendations.length - 1} more recommendation{displayRecommendations.length > 2 ? 's' : ''}\n              </div>\n            )}\n          </div>\n        ) : null;\n\n      case 'citation':\n        return conversationSummary ? (\n          <AdMeshCitationUnit\n            recommendations={displayRecommendations}\n            conversationText={conversationSummary}\n            theme={theme}\n            showCitationList={true}\n            citationStyle=\"numbered\"\n            onRecommendationClick={handleRecommendationClick}\n          />\n        ) : null;\n\n      case 'floating':\n        return (\n          <div className=\"admesh-floating-unit bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700 p-4\">\n            <div className=\"flex justify-between items-start mb-3\">\n              <div className=\"flex items-center gap-2\">\n                <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                <span className=\"text-sm font-semibold text-gray-800 dark:text-gray-200\">\n                  Recommended for you\n                </span>\n              </div>\n              {onDismiss && (\n                <button\n                  onClick={handleDismiss}\n                  className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors\"\n                  aria-label=\"Dismiss recommendations\"\n                >\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              )}\n            </div>\n            <div className=\"space-y-2\">\n              {displayRecommendations.map((recommendation, index) => (\n                <AdMeshInlineRecommendation\n                  key={recommendation.ad_id || index}\n                  recommendation={recommendation}\n                  theme={theme}\n                  compact={true}\n                  showReason={false}\n                  onClick={handleRecommendationClick}\n                />\n              ))}\n            </div>\n          </div>\n        );\n\n      default:\n        return (\n          <div className=\"space-y-3\">\n            {displayRecommendations.map((recommendation, index) => (\n              <AdMeshProductCard\n                key={recommendation.ad_id || index}\n                recommendation={recommendation}\n                theme={theme}\n                showMatchScore={false}\n                showBadges={true}\n                onClick={handleRecommendationClick}\n              />\n            ))}\n          </div>\n        );\n    }\n  };\n\n  const containerClasses = classNames(\n    'admesh-conversational-unit',\n    'transition-all duration-300 ease-in-out',\n    {\n      'opacity-0 translate-y-2': !hasAnimated,\n      'opacity-100 translate-y-0': hasAnimated,\n      'fixed bottom-4 right-4 max-w-sm z-50': config.displayMode === 'floating',\n      'my-3': config.displayMode === 'inline',\n      'mt-4 pt-4 border-t border-gray-200 dark:border-slate-700': config.displayMode === 'summary',\n    },\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n      data-admesh-context={config.context}\n      data-session-id={sessionId}\n    >\n      {renderContent()}\n      \n      {/* Powered by AdMesh branding */}\n      {config.showPoweredBy !== false && (\n        <div className=\"flex justify-end mt-2\">\n          <span className=\"text-xs text-gray-400 dark:text-gray-500\">\n            Powered by AdMesh\n          </span>\n        </div>\n      )}\n    </div>\n  );\n};\n", "import React from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshChatMessageProps } from '../types/index';\nimport { AdMeshConversationalUnit } from './AdMeshConversationalUnit';\n\nexport const AdMeshChatMessage: React.FC<AdMeshChatMessageProps> = ({\n  message,\n  theme,\n  onRecommendationClick,\n  className\n}) => {\n  const isUser = message.role === 'user';\n  const isAssistant = message.role === 'assistant';\n\n  const messageClasses = classNames(\n    'admesh-chat-message',\n    'flex items-start gap-3',\n    {\n      'flex-row-reverse': isUser,\n    },\n    className\n  );\n\n  const bubbleClasses = classNames(\n    'max-w-xs lg:max-w-sm px-4 py-3 rounded-lg text-sm',\n    {\n      'bg-gradient-to-r from-blue-600 to-indigo-600 text-white': isUser,\n      'bg-gray-100 dark:bg-slate-800 text-gray-900 dark:text-gray-100': isAssistant,\n      'bg-yellow-100 dark:bg-yellow-900 text-yellow-900 dark:text-yellow-100': message.role === 'system',\n    }\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  const formatTime = (timestamp: Date) => {\n    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  };\n\n  return (\n    <div\n      className={messageClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Avatar */}\n      {!isUser && (\n        <div className=\"w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center flex-shrink-0\">\n          <svg className=\"w-4 h-4 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n          </svg>\n        </div>\n      )}\n\n      {isUser && (\n        <div className=\"w-8 h-8 bg-gray-300 dark:bg-slate-600 rounded-full flex items-center justify-center flex-shrink-0\">\n          <svg className=\"w-4 h-4 text-gray-600 dark:text-gray-300\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n          </svg>\n        </div>\n      )}\n\n      {/* Message Content */}\n      <div className={`flex flex-col ${isUser ? 'items-end' : 'items-start'} flex-1`}>\n        {/* Message Bubble */}\n        <div className={bubbleClasses}>\n          <div className=\"whitespace-pre-wrap break-words\">\n            {message.content}\n          </div>\n        </div>\n\n        {/* Timestamp */}\n        <div className={classNames(\n          'text-xs text-gray-500 dark:text-gray-400 mt-1',\n          { 'text-right': isUser }\n        )}>\n          {formatTime(message.timestamp)}\n        </div>\n\n        {/* Recommendations */}\n        {message.recommendations && message.recommendations.length > 0 && (\n          <div className=\"mt-3 w-full max-w-lg\">\n            {/* Recommendations Header */}\n            <div className=\"flex items-center gap-2 mb-3\">\n              <svg className=\"w-4 h-4 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n              </svg>\n              <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                {message.recommendations.length} recommendation{message.recommendations.length > 1 ? 's' : ''} found\n              </span>\n            </div>\n\n            {/* Recommendations Display */}\n            <AdMeshConversationalUnit\n              recommendations={message.recommendations}\n              config={{\n                displayMode: 'inline',\n                context: 'chat',\n                maxRecommendations: 3,\n                showPoweredBy: false,\n                autoShow: true,\n                delayMs: 300\n              }}\n              theme={theme}\n              onRecommendationClick={onRecommendationClick}\n              className=\"bg-gray-50 dark:bg-slate-800/50 rounded-lg p-3 border border-gray-200 dark:border-slate-700\"\n            />\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n", "import React, { useState, useRef, KeyboardEvent } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshChatInputProps } from '../types/index';\n\nexport const AdMeshChatInput: React.FC<AdMeshChatInputProps> = ({\n  placeholder = \"Type your message...\",\n  disabled = false,\n  suggestions = [],\n  theme,\n  onSendMessage,\n  className\n}) => {\n  const [message, setMessage] = useState('');\n  const [showSuggestions, setShowSuggestions] = useState(false);\n  const [filteredSuggestions, setFilteredSuggestions] = useState<string[]>([]);\n  const inputRef = useRef<HTMLTextAreaElement>(null);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\n    const value = e.target.value;\n    setMessage(value);\n\n    // Filter suggestions based on input\n    if (value.trim() && suggestions.length > 0) {\n      const filtered = suggestions.filter(suggestion =>\n        suggestion.toLowerCase().includes(value.toLowerCase())\n      );\n      setFilteredSuggestions(filtered);\n      setShowSuggestions(filtered.length > 0);\n    } else {\n      setShowSuggestions(false);\n    }\n\n    // Auto-resize textarea\n    if (inputRef.current) {\n      inputRef.current.style.height = 'auto';\n      inputRef.current.style.height = `${Math.min(inputRef.current.scrollHeight, 120)}px`;\n    }\n  };\n\n  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSend();\n    }\n  };\n\n  const handleSend = () => {\n    const trimmedMessage = message.trim();\n    if (trimmedMessage && !disabled && onSendMessage) {\n      onSendMessage(trimmedMessage);\n      setMessage('');\n      setShowSuggestions(false);\n      \n      // Reset textarea height\n      if (inputRef.current) {\n        inputRef.current.style.height = 'auto';\n      }\n    }\n  };\n\n  const handleSuggestionClick = (suggestion: string) => {\n    setMessage(suggestion);\n    setShowSuggestions(false);\n    if (inputRef.current) {\n      inputRef.current.focus();\n    }\n  };\n\n  const containerClasses = classNames(\n    'admesh-chat-input',\n    'relative',\n    className\n  );\n\n  const inputClasses = classNames(\n    'w-full resize-none rounded-lg border border-gray-300 dark:border-slate-600',\n    'bg-white dark:bg-slate-800 text-gray-900 dark:text-gray-100',\n    'placeholder-gray-500 dark:placeholder-gray-400',\n    'focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent',\n    'transition-all duration-200 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600',\n    'pr-12 pl-4 py-3 text-sm leading-5',\n    {\n      'opacity-50 cursor-not-allowed': disabled,\n    }\n  );\n\n  const sendButtonClasses = classNames(\n    'absolute right-2 bottom-2 p-2 rounded-lg transition-all duration-200',\n    'flex items-center justify-center',\n    {\n      'bg-blue-600 hover:bg-blue-700 text-white': message.trim() && !disabled,\n      'bg-gray-200 dark:bg-slate-700 text-gray-400 dark:text-gray-500 cursor-not-allowed': !message.trim() || disabled,\n    }\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Suggestions Dropdown */}\n      {showSuggestions && filteredSuggestions.length > 0 && (\n        <div className=\"absolute bottom-full left-0 right-0 mb-2 bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-lg shadow-lg max-h-40 overflow-y-auto z-10\">\n          {filteredSuggestions.slice(0, 5).map((suggestion, index) => (\n            <button\n              key={index}\n              onClick={() => handleSuggestionClick(suggestion)}\n              className=\"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors first:rounded-t-lg last:rounded-b-lg\"\n            >\n              {suggestion}\n            </button>\n          ))}\n        </div>\n      )}\n\n      {/* Input Container */}\n      <div className=\"relative\">\n        <textarea\n          ref={inputRef}\n          value={message}\n          onChange={handleInputChange}\n          onKeyDown={handleKeyDown}\n          placeholder={placeholder}\n          disabled={disabled}\n          rows={1}\n          className={inputClasses}\n          style={{ minHeight: '44px', maxHeight: '120px' }}\n        />\n\n        {/* Send Button */}\n        <button\n          onClick={handleSend}\n          disabled={!message.trim() || disabled}\n          className={sendButtonClasses}\n          aria-label=\"Send message\"\n        >\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n          </svg>\n        </button>\n      </div>\n\n      {/* Helper Text */}\n      <div className=\"flex items-center justify-between mt-2 text-xs text-gray-500 dark:text-gray-400\">\n        <span>Press Enter to send, Shift+Enter for new line</span>\n        <span className={classNames(\n          'transition-opacity duration-200',\n          { 'opacity-0': message.length < 100 }\n        )}>\n          {message.length}/500\n        </span>\n      </div>\n    </div>\n  );\n};\n", "import React, { useEffect, useRef } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshChatInterfaceProps } from '../types/index';\nimport { AdMeshChatMessage } from './AdMeshChatMessage';\nimport { AdMeshChatInput } from './AdMeshChatInput';\n\nexport const AdMeshChatInterface: React.FC<AdMeshChatInterfaceProps> = ({\n  messages,\n  config,\n  theme,\n  isLoading = false,\n  onSendMessage,\n  onRecommendationClick,\n  className\n}) => {\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n  const messagesContainerRef = useRef<HTMLDivElement>(null);\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\n    }\n  }, [messages]);\n\n  const containerClasses = classNames(\n    'admesh-chat-interface',\n    'flex flex-col h-full bg-white dark:bg-slate-900',\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  // Limit messages if maxMessages is set\n  const displayMessages = config.maxMessages \n    ? messages.slice(-config.maxMessages)\n    : messages;\n\n  return (\n    <div\n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Messages Area */}\n      <div \n        ref={messagesContainerRef}\n        className=\"flex-1 overflow-y-auto p-4 space-y-4 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600\"\n      >\n        {displayMessages.length === 0 ? (\n          <div className=\"flex flex-col items-center justify-center h-full text-center\">\n            <div className=\"w-16 h-16 bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900 dark:to-indigo-900 rounded-full flex items-center justify-center mb-4\">\n              <svg className=\"w-8 h-8 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n              </svg>\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2\">\n              Welcome to AdMesh AI\n            </h3>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 max-w-xs\">\n              Ask me anything about products, tools, or services. I'll provide personalized recommendations just for you!\n            </p>\n          </div>\n        ) : (\n          <>\n            {displayMessages.map((message) => (\n              <AdMeshChatMessage\n                key={message.id}\n                message={message}\n                theme={theme}\n                onRecommendationClick={onRecommendationClick}\n              />\n            ))}\n\n            {/* Typing Indicator */}\n            {isLoading && config.enableTypingIndicator !== false && (\n              <div className=\"flex items-start gap-3\">\n                <div className=\"w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center flex-shrink-0\">\n                  <svg className=\"w-4 h-4 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                  </svg>\n                </div>\n                <div className=\"bg-gray-100 dark:bg-slate-800 rounded-lg px-4 py-3 max-w-xs\">\n                  <div className=\"flex space-x-1\">\n                    <div className=\"w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce\"></div>\n                    <div className=\"w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                    <div className=\"w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            <div ref={messagesEndRef} />\n          </>\n        )}\n      </div>\n\n      {/* Quick Suggestions */}\n      {config.enableSuggestions && config.suggestions && config.suggestions.length > 0 && messages.length === 0 && (\n        <div className=\"px-4 pb-2\">\n          <div className=\"text-xs text-gray-500 dark:text-gray-400 mb-2\">Quick suggestions:</div>\n          <div className=\"flex flex-wrap gap-2\">\n            {config.suggestions.slice(0, 3).map((suggestion, index) => (\n              <button\n                key={index}\n                onClick={() => onSendMessage?.(suggestion)}\n                className=\"px-3 py-1.5 text-xs bg-gray-100 dark:bg-slate-800 hover:bg-gray-200 dark:hover:bg-slate-700 text-gray-700 dark:text-gray-300 rounded-full transition-colors\"\n              >\n                {suggestion}\n              </button>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Input Area */}\n      {config.showInputField !== false && onSendMessage && (\n        <div className=\"border-t border-gray-200 dark:border-slate-700 p-4\">\n          <AdMeshChatInput\n            placeholder={config.placeholder || \"Ask me about products, tools, or services...\"}\n            disabled={isLoading}\n            suggestions={config.suggestions}\n            theme={theme}\n            onSendMessage={onSendMessage}\n          />\n        </div>\n      )}\n    </div>\n  );\n};\n", "import React, { useState, useEffect } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshFloatingChatProps, ChatMessage } from '../types/index';\nimport { AdMeshChatInterface } from './AdMeshChatInterface';\n\nexport const AdMeshFloatingChat: React.FC<AdMeshFloatingChatProps> = ({\n  config,\n  theme,\n  title = 'AI Assistant',\n  subtitle = 'Get personalized recommendations',\n  isOpen: controlledIsOpen,\n  onToggle,\n  onSendMessage,\n  onRecommendationClick,\n  autoRecommendations,\n  autoRecommendationTrigger,\n  showInputField = true,\n  autoShowRecommendations = false,\n  onAutoRecommendationDismiss,\n  className\n}) => {\n  const [internalIsOpen, setInternalIsOpen] = useState(config.autoOpen || false);\n  const [messages, setMessages] = useState<ChatMessage[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [hasInteracted, setHasInteracted] = useState(false);\n\n  const isOpen = controlledIsOpen !== undefined ? controlledIsOpen : internalIsOpen;\n\n  // Initialize with welcome message\n  useEffect(() => {\n    if (config.showWelcomeMessage && config.welcomeMessage && messages.length === 0) {\n      const welcomeMessage: ChatMessage = {\n        id: 'welcome',\n        role: 'assistant',\n        content: config.welcomeMessage,\n        timestamp: new Date(),\n      };\n      setMessages([welcomeMessage]);\n    }\n  }, [config.showWelcomeMessage, config.welcomeMessage, messages.length]);\n\n  // Handle auto-recommendations\n  useEffect(() => {\n    if (autoRecommendations && autoRecommendations.length > 0 && autoShowRecommendations) {\n      const autoMessage: ChatMessage = {\n        id: `auto-${Date.now()}`,\n        role: 'assistant',\n        content: autoRecommendationTrigger\n          ? `Based on \"${autoRecommendationTrigger}\", here are some relevant recommendations:`\n          : 'I found some relevant recommendations for you:',\n        timestamp: new Date(),\n        recommendations: autoRecommendations,\n      };\n\n      // Auto-open the chat and show recommendations\n      if (controlledIsOpen === undefined) {\n        setInternalIsOpen(true);\n      }\n\n      // Add the auto-recommendation message\n      setMessages(prev => {\n        // Avoid duplicating auto-recommendations\n        const hasAutoMessage = prev.some(msg => msg.id.startsWith('auto-'));\n        if (hasAutoMessage) {\n          return prev.map(msg =>\n            msg.id.startsWith('auto-') ? autoMessage : msg\n          );\n        }\n        return [...prev, autoMessage];\n      });\n    }\n  }, [autoRecommendations, autoShowRecommendations, autoRecommendationTrigger, controlledIsOpen]);\n\n  const handleToggle = () => {\n    if (onToggle) {\n      onToggle();\n    } else {\n      setInternalIsOpen(!internalIsOpen);\n    }\n    setHasInteracted(true);\n  };\n\n  const handleSendMessage = async (messageContent: string) => {\n    if (!onSendMessage) return;\n\n    // Add user message\n    const userMessage: ChatMessage = {\n      id: `user-${Date.now()}`,\n      role: 'user',\n      content: messageContent,\n      timestamp: new Date(),\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setIsLoading(true);\n\n    try {\n      // Send message\n      await onSendMessage(messageContent);\n      // Note: The response should be handled by the parent component\n    } catch (error) {\n      console.error('Error sending message:', error);\n      const errorMessage: ChatMessage = {\n        id: `error-${Date.now()}`,\n        role: 'assistant',\n        content: 'Sorry, I encountered an error. Please try again.',\n        timestamp: new Date(),\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Get chat dimensions based on size\n  const getChatDimensions = () => {\n    switch (config.size) {\n      case 'sm': return 'w-80 h-96';\n      case 'md': return 'w-96 h-[32rem]';\n      case 'lg': return 'w-[28rem] h-[36rem]';\n      case 'xl': return 'w-[32rem] h-[40rem]';\n      default: return 'w-96 h-[32rem]';\n    }\n  };\n\n  // Get position classes\n  const getPositionClasses = () => {\n    switch (config.position) {\n      case 'bottom-right': return 'bottom-4 right-4';\n      case 'bottom-left': return 'bottom-4 left-4';\n      case 'top-right': return 'top-4 right-4';\n      case 'top-left': return 'top-4 left-4';\n      default: return 'bottom-4 right-4';\n    }\n  };\n\n  const containerClasses = classNames(\n    'admesh-floating-chat',\n    'fixed z-50 transition-all duration-300 ease-in-out',\n    getPositionClasses(),\n    className\n  );\n\n  const chatClasses = classNames(\n    'bg-white dark:bg-slate-900 rounded-lg shadow-2xl border border-gray-200 dark:border-slate-700 overflow-hidden',\n    getChatDimensions(),\n    {\n      'opacity-0 scale-95 pointer-events-none': !isOpen,\n      'opacity-100 scale-100': isOpen,\n    }\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Chat Interface */}\n      <div className={chatClasses}>\n        {isOpen && (\n          <>\n            {/* Header */}\n            <div className=\"flex items-center justify-between p-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center\">\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                  </svg>\n                </div>\n                <div>\n                  <h3 className=\"font-semibold text-sm\">{title}</h3>\n                  <p className=\"text-xs text-blue-100\">{subtitle}</p>\n                </div>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                {/* Dismiss auto-recommendations button */}\n                {autoRecommendations && autoRecommendations.length > 0 && onAutoRecommendationDismiss && (\n                  <button\n                    onClick={() => {\n                      onAutoRecommendationDismiss();\n                      setMessages(prev => prev.filter(msg => !msg.id.startsWith('auto-')));\n                    }}\n                    className=\"p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors\"\n                    aria-label=\"Dismiss recommendations\"\n                    title=\"Dismiss recommendations\"\n                  >\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\" />\n                    </svg>\n                  </button>\n                )}\n\n                <button\n                  onClick={handleToggle}\n                  className=\"p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors\"\n                  aria-label=\"Close chat\"\n                >\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n            </div>\n\n            {/* Chat Interface */}\n            <AdMeshChatInterface\n              messages={messages}\n              config={{\n                ...config,\n                showInputField: showInputField\n              }}\n              theme={theme}\n              isLoading={isLoading}\n              onSendMessage={showInputField ? handleSendMessage : () => {}}\n              onRecommendationClick={onRecommendationClick}\n              className=\"h-full\"\n            />\n          </>\n        )}\n      </div>\n\n      {/* Chat Toggle Button */}\n      {!isOpen && (\n        <button\n          onClick={handleToggle}\n          className={classNames(\n            'w-14 h-14 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700',\n            'text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200',\n            'flex items-center justify-center relative'\n          )}\n          aria-label=\"Open chat\"\n        >\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n          </svg>\n          \n          {/* Notification dot for new users */}\n          {!hasInteracted && (\n            <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse\"></div>\n          )}\n        </button>\n      )}\n\n      {/* Powered by AdMesh */}\n      {isOpen && (\n        <div className=\"absolute bottom-2 right-2 text-xs text-gray-400 dark:text-gray-500 bg-white dark:bg-slate-900 px-2 py-1 rounded shadow-sm\">\n          Powered by AdMesh\n        </div>\n      )}\n    </div>\n  );\n};\n", "import React, { useState, useEffect } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshSidebarHeaderProps } from '../types/index';\n\nexport const AdMeshSidebarHeader: React.FC<AdMeshSidebarHeaderProps> = ({\n  title,\n  theme,\n  collapsible = false,\n  isCollapsed = false,\n  onToggle,\n  onSearch,\n  showSearch = false,\n  className\n}) => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [isSearchFocused, setIsSearchFocused] = useState(false);\n  const [isMobile, setIsMobile] = useState(false);\n\n  // Mobile detection\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth < 640);\n    };\n\n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    return () => window.removeEventListener('resize', checkMobile);\n  }, []);\n\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const value = e.target.value;\n    setSearchQuery(value);\n    onSearch?.(value);\n  };\n\n  const handleSearchClear = () => {\n    setSearchQuery('');\n    onSearch?.('');\n  };\n\n  const headerClasses = classNames(\n    'admesh-sidebar-header',\n    'flex flex-col p-4 border-b border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-800',\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={headerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Title and Toggle */}\n      <div className=\"flex items-center justify-between mb-3\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 truncate\">\n          {title}\n        </h3>\n\n        <div className=\"flex items-center gap-2\">\n          {/* Mobile close button */}\n          {isMobile && onToggle && (\n            <button\n              onClick={onToggle}\n              className=\"p-1.5 rounded-lg hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors flex-shrink-0 sm:hidden\"\n              title=\"Close sidebar\"\n            >\n              <svg\n                className=\"w-4 h-4 text-gray-600 dark:text-gray-400\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          )}\n\n          {/* Desktop collapse button */}\n          {collapsible && (\n            <button\n              onClick={onToggle}\n              className=\"p-1.5 rounded-lg hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors flex-shrink-0 hidden sm:block\"\n              title={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}\n            >\n              <svg\n                className={classNames(\n                  'w-4 h-4 text-gray-600 dark:text-gray-400 transition-transform duration-200',\n                  { 'rotate-180': isCollapsed }\n                )}\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n              </svg>\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* Search Bar */}\n      {showSearch && !isCollapsed && (\n        <div className=\"relative\">\n          <div className={classNames(\n            'relative flex items-center transition-all duration-200',\n            {\n              'ring-2 ring-blue-500 dark:ring-blue-400': isSearchFocused,\n            }\n          )}>\n            {/* Search Icon */}\n            <div className=\"absolute left-3 pointer-events-none\">\n              <svg className=\"w-4 h-4 text-gray-400 dark:text-gray-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n              </svg>\n            </div>\n\n            {/* Search Input */}\n            <input\n              type=\"text\"\n              value={searchQuery}\n              onChange={handleSearchChange}\n              onFocus={() => setIsSearchFocused(true)}\n              onBlur={() => setIsSearchFocused(false)}\n              placeholder=\"Search recommendations...\"\n              className={classNames(\n                'w-full pl-10 pr-10 py-2 text-sm bg-white dark:bg-slate-900 border border-gray-300 dark:border-slate-600 rounded-lg',\n                'placeholder-gray-400 dark:placeholder-gray-500 text-gray-900 dark:text-gray-100',\n                'focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent',\n                'transition-all duration-200'\n              )}\n            />\n\n            {/* Clear Button */}\n            {searchQuery && (\n              <button\n                onClick={handleSearchClear}\n                className=\"absolute right-3 p-0.5 rounded-full hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors\"\n                title=\"Clear search\"\n              >\n                <svg className=\"w-3 h-3 text-gray-400 dark:text-gray-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                </svg>\n              </button>\n            )}\n          </div>\n\n          {/* Search Results Count */}\n          {searchQuery && (\n            <div className=\"mt-2 text-xs text-gray-500 dark:text-gray-400\">\n              Search results will be filtered in real-time\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Quick Stats */}\n      {!isCollapsed && (\n        <div className=\"mt-3 flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400\">\n          <div className=\"flex items-center gap-1\">\n            <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n            <span>Live recommendations</span>\n          </div>\n          <div className=\"flex items-center gap-1\">\n            <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n            </svg>\n            <span>AI-powered</span>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n", "import React, { useState } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshSidebarContentProps } from '../types/index';\nimport { AdMeshInlineRecommendation } from './AdMeshInlineRecommendation';\nimport { AdMeshProductCard } from './AdMeshProductCard';\n\nexport const AdMeshSidebarContent: React.FC<AdMeshSidebarContentProps> = ({\n  recommendations,\n  displayMode,\n  theme,\n  maxRecommendations,\n  onRecommendationClick,\n  className\n}) => {\n  const [showFilters, setShowFilters] = useState(false);\n  const [activeTab, setActiveTab] = useState<'all' | 'top' | 'recent'>('all');\n\n  const displayRecommendations = maxRecommendations \n    ? recommendations.slice(0, maxRecommendations)\n    : recommendations;\n\n  const getTabRecommendations = () => {\n    switch (activeTab) {\n      case 'top':\n        return displayRecommendations\n          .filter(rec => rec.intent_match_score >= 0.8)\n          .slice(0, 5);\n      case 'recent':\n        return displayRecommendations.slice(0, 3);\n      default:\n        return displayRecommendations;\n    }\n  };\n\n  const tabRecommendations = getTabRecommendations();\n\n  const contentClasses = classNames(\n    'admesh-sidebar-content',\n    'flex flex-col h-full',\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  const renderRecommendations = () => {\n    if (tabRecommendations.length === 0) {\n      return (\n        <div className=\"flex-1 flex flex-col items-center justify-center p-6 text-center\">\n          <div className=\"w-16 h-16 bg-gray-100 dark:bg-slate-800 rounded-full flex items-center justify-center mb-4\">\n            <svg className=\"w-8 h-8 text-gray-400 dark:text-gray-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n            </svg>\n          </div>\n          <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n            No recommendations found\n          </h4>\n          <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n            Try adjusting your search or filters\n          </p>\n        </div>\n      );\n    }\n\n    switch (displayMode) {\n      case 'recommendations':\n        return (\n          <div className=\"space-y-3\">\n            {tabRecommendations.map((recommendation, index) => (\n              <AdMeshInlineRecommendation\n                key={recommendation.ad_id || index}\n                recommendation={recommendation}\n                theme={theme}\n                compact={true}\n                showReason={true}\n                onClick={onRecommendationClick}\n              />\n            ))}\n          </div>\n        );\n\n      case 'history':\n        return (\n          <div className=\"space-y-2\">\n            {tabRecommendations.map((recommendation, index) => (\n              <div key={recommendation.ad_id || index} className=\"flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-800 transition-colors\">\n                <div className=\"w-2 h-2 bg-gray-400 rounded-full flex-shrink-0\"></div>\n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\">\n                    {recommendation.title}\n                  </div>\n                  <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    {Math.round(recommendation.intent_match_score * 100)}% match\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        );\n\n      case 'favorites':\n        return (\n          <div className=\"space-y-3\">\n            {tabRecommendations.slice(0, 3).map((recommendation, index) => (\n              <div key={recommendation.ad_id || index} className=\"relative\">\n                <AdMeshInlineRecommendation\n                  recommendation={recommendation}\n                  theme={theme}\n                  compact={true}\n                  showReason={false}\n                  onClick={onRecommendationClick}\n                />\n                <button className=\"absolute top-2 right-2 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors\">\n                  <svg className=\"w-3 h-3 text-yellow-500\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\" />\n                  </svg>\n                </button>\n              </div>\n            ))}\n          </div>\n        );\n\n      case 'mixed':\n        return (\n          <div className=\"space-y-4\">\n            {/* Top recommendation as card */}\n            {tabRecommendations[0] && (\n              <div>\n                <h4 className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2\">\n                  Top Pick\n                </h4>\n                <AdMeshProductCard\n                  recommendation={tabRecommendations[0]}\n                  theme={theme}\n                  showMatchScore={true}\n                  showBadges={true}\n                  onClick={onRecommendationClick}\n                  className=\"text-xs\"\n                />\n              </div>\n            )}\n\n            {/* Other recommendations as inline */}\n            {tabRecommendations.slice(1).length > 0 && (\n              <div>\n                <h4 className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2\">\n                  More Options\n                </h4>\n                <div className=\"space-y-2\">\n                  {tabRecommendations.slice(1, 4).map((recommendation, index) => (\n                    <AdMeshInlineRecommendation\n                      key={recommendation.ad_id || index}\n                      recommendation={recommendation}\n                      theme={theme}\n                      compact={true}\n                      showReason={false}\n                      onClick={onRecommendationClick}\n                    />\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n        );\n\n      default:\n        return (\n          <div className=\"space-y-3\">\n            {tabRecommendations.map((recommendation, index) => (\n              <AdMeshInlineRecommendation\n                key={recommendation.ad_id || index}\n                recommendation={recommendation}\n                theme={theme}\n                compact={true}\n                showReason={true}\n                onClick={onRecommendationClick}\n              />\n            ))}\n          </div>\n        );\n    }\n  };\n\n  return (\n    <div\n      className={contentClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Tabs */}\n      <div className=\"flex border-b border-gray-200 dark:border-slate-700 bg-white dark:bg-slate-900\">\n        <button\n          onClick={() => setActiveTab('all')}\n          className={classNames(\n            'flex-1 px-3 py-2 text-xs font-medium transition-colors',\n            activeTab === 'all'\n              ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'\n              : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\n          )}\n        >\n          All ({recommendations.length})\n        </button>\n        <button\n          onClick={() => setActiveTab('top')}\n          className={classNames(\n            'flex-1 px-3 py-2 text-xs font-medium transition-colors',\n            activeTab === 'top'\n              ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'\n              : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\n          )}\n        >\n          Top\n        </button>\n        <button\n          onClick={() => setActiveTab('recent')}\n          className={classNames(\n            'flex-1 px-3 py-2 text-xs font-medium transition-colors',\n            activeTab === 'recent'\n              ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'\n              : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\n          )}\n        >\n          Recent\n        </button>\n      </div>\n\n      {/* Content */}\n      <div className=\"flex-1 overflow-y-auto p-4 min-h-0\" style={{\n        WebkitOverflowScrolling: 'touch', // Smooth scrolling on iOS\n        overscrollBehavior: 'contain' // Prevent scroll chaining on mobile\n      }}>\n        {renderRecommendations()}\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"p-3 border-t border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-800\">\n        <div className=\"flex items-center justify-between text-xs\">\n          <span className=\"text-gray-500 dark:text-gray-400\">\n            {tabRecommendations.length} recommendation{tabRecommendations.length !== 1 ? 's' : ''}\n          </span>\n          <button\n            onClick={() => setShowFilters(!showFilters)}\n            className=\"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors\"\n          >\n            Filters\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n", "import React, { useState, useEffect, useMemo } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshSidebarProps, SidebarFilters } from '../types/index';\nimport { AdMeshSidebarHeader } from './AdMeshSidebarHeader';\nimport { AdMeshSidebarContent } from './AdMeshSidebarContent';\n\nexport const AdMeshSidebar: React.FC<AdMeshSidebarProps> = ({\n  recommendations,\n  config,\n  theme,\n  title = 'Recommendations',\n  isOpen = true,\n  onToggle,\n  onRecommendationClick,\n  onSearch,\n  // onFilter,\n  className,\n  containerMode = false // New prop for demo/container integration\n}) => {\n  const [isCollapsed, setIsCollapsed] = useState(config.defaultCollapsed || false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [filters] = useState<SidebarFilters>({});\n  const [isMobile, setIsMobile] = useState(false);\n\n  // Mobile detection\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth < 640);\n    };\n\n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    return () => window.removeEventListener('resize', checkMobile);\n  }, []);\n\n  // Prevent body scroll on mobile when sidebar is open\n  useEffect(() => {\n    if (isMobile && isOpen && !isCollapsed && !containerMode) {\n      const originalStyle = window.getComputedStyle(document.body).overflow;\n      document.body.style.overflow = 'hidden';\n      document.body.style.position = 'fixed';\n      document.body.style.width = '100%';\n\n      return () => {\n        document.body.style.overflow = originalStyle;\n        document.body.style.position = '';\n        document.body.style.width = '';\n      };\n    }\n  }, [isMobile, isOpen, isCollapsed, containerMode]);\n\n  // Handle auto-refresh if enabled\n  useEffect(() => {\n    if (config.autoRefresh && config.refreshInterval) {\n      const interval = setInterval(() => {\n        // Trigger a refresh - in a real app this would refetch recommendations\n        console.log('Auto-refreshing recommendations...');\n      }, config.refreshInterval);\n\n      return () => clearInterval(interval);\n    }\n  }, [config.autoRefresh, config.refreshInterval]);\n\n  // Filter recommendations based on search and filters\n  const filteredRecommendations = useMemo(() => {\n    let filtered = [...recommendations];\n\n    // Apply search filter\n    if (searchQuery.trim()) {\n      const query = searchQuery.toLowerCase();\n      filtered = filtered.filter(rec => \n        rec.title.toLowerCase().includes(query) ||\n        rec.reason.toLowerCase().includes(query) ||\n        rec.keywords?.some(keyword => keyword.toLowerCase().includes(query))\n      );\n    }\n\n    // Apply category filter\n    if (filters.categories && filters.categories.length > 0) {\n      filtered = filtered.filter(rec => \n        rec.categories?.some(cat => filters.categories?.includes(cat))\n      );\n    }\n\n    // Apply free tier filter\n    if (filters.hasFreeTier) {\n      // Note: has_free_tier property not available in current type definition\n      // This filter is disabled until the property is added to AdMeshRecommendation\n      // filtered = filtered.filter(rec => rec.has_free_tier);\n    }\n\n    // Apply trial filter\n    if (filters.hasTrial) {\n      filtered = filtered.filter(rec => rec.trial_days && rec.trial_days > 0);\n    }\n\n    // Apply minimum match score filter\n    if (filters.minMatchScore !== undefined) {\n      filtered = filtered.filter(rec => rec.intent_match_score >= filters.minMatchScore!);\n    }\n\n    // Sort by match score (highest first)\n    filtered.sort((a, b) => b.intent_match_score - a.intent_match_score);\n\n    // Limit results\n    if (config.maxRecommendations) {\n      filtered = filtered.slice(0, config.maxRecommendations);\n    }\n\n    return filtered;\n  }, [recommendations, searchQuery, filters, config.maxRecommendations]);\n\n  const handleToggle = () => {\n    if (config.collapsible) {\n      setIsCollapsed(!isCollapsed);\n      onToggle?.();\n    }\n  };\n\n  const handleSearch = (query: string) => {\n    setSearchQuery(query);\n    onSearch?.(query);\n  };\n\n  // const handleFilter = (newFilters: SidebarFilters) => {\n  //   setFilters(newFilters);\n  //   onFilter?.(newFilters);\n  // };\n\n  // Get sidebar width based on size with mobile responsiveness\n  const getSidebarWidth = () => {\n    if (isCollapsed) return 'w-12';\n\n    // On mobile, always use full width with proper constraints\n    switch (config.size) {\n      case 'sm': return 'w-full sm:w-64 max-w-[90vw] sm:max-w-sm';\n      case 'md': return 'w-full sm:w-80 max-w-[90vw] sm:max-w-md';\n      case 'lg': return 'w-full sm:w-96 max-w-[90vw] sm:max-w-lg';\n      case 'xl': return 'w-full sm:w-[28rem] max-w-[90vw] sm:max-w-xl';\n      default: return 'w-full sm:w-80 max-w-[90vw] sm:max-w-md';\n    }\n  };\n\n  const sidebarClasses = classNames(\n    'admesh-sidebar',\n    'flex flex-col bg-white dark:bg-slate-900 border-gray-200 dark:border-slate-700 shadow-lg transition-all duration-300 ease-in-out',\n    getSidebarWidth(),\n    {\n      'border-r': config.position === 'left',\n      'border-l': config.position === 'right',\n      // Use fixed positioning for full-screen mode, relative for container mode\n      // Improved mobile positioning with proper viewport handling\n      'fixed top-0 bottom-0 z-[9999]': !containerMode,\n      'relative h-full': containerMode,\n      'left-0': config.position === 'left' && !containerMode,\n      'right-0': config.position === 'right' && !containerMode,\n      // Better mobile transform handling\n      'transform -translate-x-full': config.position === 'left' && !isOpen && !containerMode,\n      'transform translate-x-full': config.position === 'right' && !isOpen && !containerMode,\n      // Mobile-specific improvements\n      'min-h-0': true, // Prevent height issues on mobile\n      'overflow-hidden': !containerMode, // Prevent scroll issues\n    },\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  if (!isOpen && !config.collapsible) {\n    return null;\n  }\n\n  return (\n    <>\n      {/* Overlay for mobile - show in both modes on small screens */}\n      {isOpen && !isCollapsed && (\n        <div\n          className={classNames(\n            \"bg-black bg-opacity-50 z-[9998] sm:hidden transition-opacity duration-300\",\n            containerMode ? \"absolute inset-0\" : \"fixed inset-0\"\n          )}\n          onClick={() => onToggle?.()}\n          style={{\n            // Ensure overlay covers the entire viewport on mobile\n            position: containerMode ? 'absolute' : 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            touchAction: 'none', // Prevent scrolling behind overlay\n          }}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div\n        className={sidebarClasses}\n        style={containerStyle}\n        data-admesh-theme={theme?.mode}\n        data-sidebar-position={config.position}\n        data-sidebar-size={config.size}\n        data-mobile-open={isMobile && isOpen && !isCollapsed ? 'true' : 'false'}\n        data-container-mode={containerMode ? 'true' : 'false'}\n      >\n        {/* Header */}\n        {config.showHeader !== false && (\n          <AdMeshSidebarHeader\n            title={title}\n            theme={theme}\n            collapsible={config.collapsible}\n            isCollapsed={isCollapsed}\n            onToggle={handleToggle}\n            onSearch={config.showSearch ? handleSearch : undefined}\n            showSearch={config.showSearch && !isCollapsed}\n          />\n        )}\n\n        {/* Content */}\n        {!isCollapsed && (\n          <AdMeshSidebarContent\n            recommendations={filteredRecommendations}\n            displayMode={config.displayMode}\n            theme={theme}\n            maxRecommendations={config.maxRecommendations}\n            onRecommendationClick={onRecommendationClick}\n            className=\"flex-1 overflow-hidden min-h-0\"\n          />\n        )}\n\n        {/* Collapsed state indicator */}\n        {isCollapsed && config.collapsible && (\n          <div className=\"flex-1 flex flex-col items-center justify-center p-2\">\n            <button\n              onClick={handleToggle}\n              className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-800 transition-colors\"\n              title=\"Expand sidebar\"\n            >\n              <svg className=\"w-5 h-5 text-gray-600 dark:text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n              </svg>\n            </button>\n            <div className=\"mt-4 text-xs text-gray-500 dark:text-gray-400 transform -rotate-90 whitespace-nowrap\">\n              {filteredRecommendations.length}\n            </div>\n          </div>\n        )}\n\n        {/* Powered by AdMesh */}\n        {!isCollapsed && (\n          <div className=\"p-3 border-t border-gray-200 dark:border-slate-700\">\n            <div className=\"text-xs text-gray-400 dark:text-gray-500 text-center\">\n              Powered by AdMesh\n            </div>\n          </div>\n        )}\n      </div>\n    </>\n  );\n};\n", "import React, { useState, useEffect } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshRecommendation, AdMeshTheme } from '../types/index';\nimport { AdMeshConversationalUnit } from './AdMeshConversationalUnit';\n\nexport interface AdMeshAutoRecommendationWidgetProps {\n  recommendations: AdMeshRecommendation[];\n  trigger?: string; // The query/context that triggered recommendations\n  theme?: AdMeshTheme;\n  title?: string;\n  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';\n  size?: 'sm' | 'md' | 'lg';\n  autoShow?: boolean;\n  showDelay?: number; // Delay before showing (ms)\n  onRecommendationClick?: (adId: string, admeshLink: string) => void;\n  onDismiss?: () => void;\n  className?: string;\n}\n\nexport const AdMeshAutoRecommendationWidget: React.FC<AdMeshAutoRecommendationWidgetProps> = ({\n  recommendations,\n  trigger,\n  theme,\n  title = 'AI Recommendations',\n  position = 'bottom-right',\n  size = 'md',\n  autoShow = true,\n  showDelay = 1000,\n  onRecommendationClick,\n  onDismiss,\n  className\n}) => {\n  const [isVisible, setIsVisible] = useState(false);\n  const [hasAnimated, setHasAnimated] = useState(false);\n\n  // Auto-show with delay\n  useEffect(() => {\n    if (autoShow && recommendations.length > 0) {\n      const timer = setTimeout(() => {\n        setIsVisible(true);\n        setHasAnimated(true);\n      }, showDelay);\n\n      return () => clearTimeout(timer);\n    }\n  }, [autoShow, recommendations.length, showDelay]);\n\n  const handleDismiss = () => {\n    setIsVisible(false);\n    onDismiss?.();\n  };\n\n  // Get widget dimensions based on size\n  const getWidgetDimensions = () => {\n    switch (size) {\n      case 'sm': return 'w-72 max-h-80';\n      case 'md': return 'w-80 max-h-96';\n      case 'lg': return 'w-96 max-h-[28rem]';\n      default: return 'w-80 max-h-96';\n    }\n  };\n\n  // Get position classes\n  const getPositionClasses = () => {\n    switch (position) {\n      case 'bottom-right': return 'bottom-4 right-4';\n      case 'bottom-left': return 'bottom-4 left-4';\n      case 'top-right': return 'top-4 right-4';\n      case 'top-left': return 'top-4 left-4';\n      default: return 'bottom-4 right-4';\n    }\n  };\n\n  if (!isVisible || recommendations.length === 0) {\n    return null;\n  }\n\n  const containerClasses = classNames(\n    'admesh-auto-recommendation-widget',\n    'fixed z-50 transition-all duration-500 ease-out',\n    getPositionClasses(),\n    getWidgetDimensions(),\n    {\n      'opacity-0 scale-95 translate-y-2': !hasAnimated,\n      'opacity-100 scale-100 translate-y-0': hasAnimated,\n    },\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      <div className=\"bg-white dark:bg-slate-900 rounded-lg shadow-2xl border border-gray-200 dark:border-slate-700 overflow-hidden\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center\">\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n              </svg>\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-sm\">{title}</h3>\n              {trigger && (\n                <p className=\"text-xs text-blue-100 truncate max-w-48\">\n                  Based on: \"{trigger}\"\n                </p>\n              )}\n            </div>\n          </div>\n          <button\n            onClick={handleDismiss}\n            className=\"p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors\"\n            aria-label=\"Dismiss recommendations\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-4 max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600\">\n          {/* Recommendations count */}\n          <div className=\"flex items-center gap-2 mb-3\">\n            <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n            <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n              {recommendations.length} intelligent match{recommendations.length > 1 ? 'es' : ''} found\n            </span>\n          </div>\n\n          {/* Recommendations */}\n          <AdMeshConversationalUnit\n            recommendations={recommendations}\n            config={{\n              displayMode: 'inline',\n              context: 'assistant',\n              maxRecommendations: 3,\n              showPoweredBy: false,\n              autoShow: true,\n              delayMs: 200\n            }}\n            theme={theme}\n            onRecommendationClick={onRecommendationClick}\n          />\n        </div>\n\n        {/* Footer */}\n        <div className=\"px-4 py-3 bg-gray-50 dark:bg-slate-800 border-t border-gray-200 dark:border-slate-700\">\n          <div className=\"flex items-center justify-between\">\n            <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n              Powered by AdMesh\n            </span>\n            <button\n              onClick={handleDismiss}\n              className=\"text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors\"\n            >\n              Dismiss\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n", "import type { AdMeshTheme } from '../types';\n\n/**\n * Utility functions for theme customization in AdMesh UI SDK\n */\n\n/**\n * Creates a theme with sensible defaults and custom overrides\n */\nexport const createAdMeshTheme = (customTheme: Partial<AdMeshTheme> = {}): AdMeshTheme => {\n  const baseTheme: AdMeshTheme = {\n    mode: 'light',\n    primaryColor: '#3b82f6',\n    secondaryColor: '#10b981',\n    accentColor: '#3b82f6',\n    backgroundColor: '#ffffff',\n    surfaceColor: '#f9fafb',\n    borderColor: '#e5e7eb',\n    textColor: '#111827',\n    textSecondaryColor: '#6b7280',\n    fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n    fontSize: {\n      small: '12px',\n      base: '14px',\n      large: '16px',\n      title: '18px'\n    },\n    borderRadius: '8px',\n    spacing: {\n      small: '4px',\n      medium: '8px',\n      large: '16px'\n    },\n    shadows: {\n      small: '0 1px 3px rgba(0, 0, 0, 0.1)',\n      medium: '0 4px 6px rgba(0, 0, 0, 0.1)',\n      large: '0 10px 15px rgba(0, 0, 0, 0.1)'\n    },\n    icons: {\n      expandIcon: '▼',\n      collapseIcon: '▲',\n      starIcon: '★',\n      checkIcon: '✓',\n      arrowIcon: '→'\n    }\n  };\n\n  return {\n    ...baseTheme,\n    ...customTheme,\n    fontSize: {\n      ...baseTheme.fontSize,\n      ...customTheme.fontSize\n    },\n    spacing: {\n      ...baseTheme.spacing,\n      ...customTheme.spacing\n    },\n    shadows: {\n      ...baseTheme.shadows,\n      ...customTheme.shadows\n    },\n    icons: {\n      ...baseTheme.icons,\n      ...customTheme.icons\n    },\n    components: {\n      ...baseTheme.components,\n      ...customTheme.components\n    }\n  };\n};\n\n/**\n * Creates a dark theme variant\n */\nexport const createDarkTheme = (customTheme: Partial<AdMeshTheme> = {}): AdMeshTheme => {\n  const darkDefaults: Partial<AdMeshTheme> = {\n    mode: 'dark',\n    backgroundColor: '#1f2937',\n    surfaceColor: '#374151',\n    borderColor: '#4b5563',\n    textColor: '#f9fafb',\n    textSecondaryColor: '#9ca3af',\n    shadows: {\n      small: '0 1px 3px rgba(0, 0, 0, 0.3)',\n      medium: '0 4px 6px rgba(0, 0, 0, 0.3)',\n      large: '0 10px 15px rgba(0, 0, 0, 0.3)'\n    }\n  };\n\n  return createAdMeshTheme({\n    ...darkDefaults,\n    ...customTheme\n  });\n};\n\n/**\n * Predefined theme presets for common AI platforms\n */\nexport const themePresets = {\n  // Clean, minimal theme\n  minimal: createAdMeshTheme({\n    primaryColor: '#000000',\n    secondaryColor: '#666666',\n    borderRadius: '4px',\n    shadows: {\n      small: 'none',\n      medium: '0 1px 3px rgba(0, 0, 0, 0.1)',\n      large: '0 2px 6px rgba(0, 0, 0, 0.1)'\n    }\n  }),\n\n  // Modern, colorful theme\n  vibrant: createAdMeshTheme({\n    primaryColor: '#8b5cf6',\n    secondaryColor: '#06b6d4',\n    accentColor: '#f59e0b',\n    borderRadius: '12px',\n    gradients: {\n      primary: 'linear-gradient(135deg, #8b5cf6 0%, #06b6d4 100%)',\n      secondary: 'linear-gradient(135deg, #06b6d4 0%, #10b981 100%)',\n      accent: 'linear-gradient(135deg, #f59e0b 0%, #ef4444 100%)'\n    }\n  }),\n\n  // Professional, corporate theme\n  corporate: createAdMeshTheme({\n    primaryColor: '#1e40af',\n    secondaryColor: '#059669',\n    backgroundColor: '#f8fafc',\n    surfaceColor: '#ffffff',\n    borderColor: '#cbd5e1',\n    borderRadius: '6px',\n    fontFamily: '\"Inter\", -apple-system, BlinkMacSystemFont, sans-serif'\n  }),\n\n  // High contrast theme for accessibility\n  highContrast: createAdMeshTheme({\n    primaryColor: '#000000',\n    secondaryColor: '#ffffff',\n    backgroundColor: '#ffffff',\n    surfaceColor: '#f5f5f5',\n    borderColor: '#000000',\n    textColor: '#000000',\n    textSecondaryColor: '#333333',\n    borderRadius: '0px',\n    shadows: {\n      small: 'none',\n      medium: '0 0 0 2px #000000',\n      large: '0 0 0 3px #000000'\n    }\n  })\n};\n\n/**\n * Utility to merge multiple theme objects\n */\nexport const mergeThemes = (...themes: Partial<AdMeshTheme>[]): AdMeshTheme => {\n  const baseTheme = createAdMeshTheme();\n  return themes.reduce((merged, theme) => {\n    if (!theme) return merged;\n    return createAdMeshTheme({\n      ...merged,\n      ...theme\n    });\n  }, baseTheme);\n};\n\n/**\n * Utility to create a theme from CSS custom properties\n */\nexport const themeFromCSSProperties = (element: HTMLElement): Partial<AdMeshTheme> => {\n  const computedStyle = getComputedStyle(element);\n  \n  return {\n    primaryColor: computedStyle.getPropertyValue('--admesh-primary-color')?.trim() || undefined,\n    secondaryColor: computedStyle.getPropertyValue('--admesh-secondary-color')?.trim() || undefined,\n    backgroundColor: computedStyle.getPropertyValue('--admesh-background-color')?.trim() || undefined,\n    textColor: computedStyle.getPropertyValue('--admesh-text-color')?.trim() || undefined,\n    borderRadius: computedStyle.getPropertyValue('--admesh-border-radius')?.trim() || undefined,\n    fontFamily: computedStyle.getPropertyValue('--admesh-font-family')?.trim() || undefined\n  };\n};\n", "// AdMesh UI SDK - Main Entry Point\n\n// Export all components\nexport {\n  AdMeshProductCard,\n  AdMeshCompareTable,\n  AdMeshBadge,\n\n  AdMeshLinkTracker,\n\n  AdMeshExpandableUnit,\n  AdMeshConversationSummary,\n  AdMeshCitationUnit,\n  AdMeshInlineRecommendation,\n  AdMeshConversationalUnit,\n  AdMeshCitationReference,\n  AdMeshFloatingChat,\n  AdMeshChatInterface,\n  AdMeshChatMessage,\n  AdMeshChatInput,\n  AdMeshSidebar,\n  AdMeshSidebarHeader,\n  AdMeshSidebarContent,\n  AdMeshAutoRecommendationWidget\n} from './components';\n\n// Export hooks\nexport {\n  useAdMeshTracker,\n  setAdMeshTrackerConfig,\n  buildAdMeshLink,\n  extractTrackingData\n} from './hooks/useAdMeshTracker';\n\nexport {\n  useAdMeshStyles\n} from './hooks/useAdMeshStyles';\n\n// Export theme utilities\nexport {\n  createAdMeshTheme,\n  createDarkTheme,\n  themePresets,\n  mergeThemes,\n  themeFromCSSProperties\n} from './utils/themeUtils';\n\n// Export disclosure utilities\nexport {\n  getRecommendationLabel,\n  getLabelTooltip,\n  getSectionDisclosure,\n  getInlineDisclosure,\n  getInlineTooltip,\n  getBadgeText,\n  getCtaText,\n  hasHighQualityMatches,\n  getPoweredByText\n} from './utils/disclosureUtils';\n\nexport type {\n  DisclosureConfig\n} from './utils/disclosureUtils';\n\n// Export types\nexport type {\n  AdMeshRecommendation,\n  AdMeshTheme,\n  IntentType,\n  BadgeType,\n  BadgeVariant,\n  BadgeSize,\n  TrackingData,\n  AdMeshProductCardProps,\n  AdMeshCompareTableProps,\n  AdMeshBadgeProps,\n\n  AdMeshLinkTrackerProps,\n\n  UseAdMeshTrackerReturn,\n  AgentRecommendationResponse,\n  AdMeshConfig,\n  ConversationalDisplayMode,\n  ConversationContext,\n  ConversationalAdConfig,\n  AdMeshConversationSummaryProps,\n  AdMeshCitationUnitProps,\n  AdMeshInlineRecommendationProps,\n  AdMeshChatInputProps,\n  AdMeshChatMessageProps,\n  AdMeshChatInterfaceProps,\n  AdMeshFloatingChatProps,\n  AdMeshCitationReferenceProps,\n  AdMeshConversationalUnitProps,\n  ChatMessage,\n  SidebarPosition,\n  SidebarSize,\n  SidebarDisplayMode,\n  AdMeshSidebarConfig,\n  AdMeshSidebarProps,\n  SidebarFilters,\n  AdMeshSidebarHeaderProps,\n  AdMeshSidebarContentProps,\n\n} from './types/index';\n\n// Version info\nexport const VERSION = '0.2.1';\n\n// Default configuration\nexport const DEFAULT_CONFIG = {\n  trackingEnabled: true,\n  debug: false,\n  theme: {\n    mode: 'light' as const,\n    accentColor: '#2563eb'\n  }\n};\n"], "names": ["REACT_ELEMENT_TYPE", "REACT_FRAGMENT_TYPE", "jsxProd", "type", "config", "<PERSON><PERSON><PERSON>", "key", "propName", "reactJsxRuntime_production", "getComponentNameFromType", "REACT_CLIENT_REFERENCE", "REACT_PROFILER_TYPE", "REACT_STRICT_MODE_TYPE", "REACT_SUSPENSE_TYPE", "REACT_SUSPENSE_LIST_TYPE", "REACT_ACTIVITY_TYPE", "REACT_PORTAL_TYPE", "REACT_CONTEXT_TYPE", "REACT_CONSUMER_TYPE", "REACT_FORWARD_REF_TYPE", "innerType", "REACT_MEMO_TYPE", "REACT_LAZY_TYPE", "testStringCoercion", "value", "checkKeyStringCoercion", "JSCompiler_inline_result", "JSCompiler_temp_const", "JSCompiler_inline_result$jscomp$0", "getTaskName", "name", "get<PERSON>wner", "dispatcher", "ReactSharedInternals", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasOwnProperty", "getter", "defineKeyPropWarningGetter", "props", "displayName", "warnAboutAccessingKey", "specialPropKeyWarningShown", "elementRefGetterWithDeprecationWarning", "componentName", "didWarnAboutElementRef", "ReactElement", "self", "source", "owner", "debugStack", "debugTask", "jsxDEVImpl", "isStaticChildren", "children", "isArrayImpl", "validate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keys", "k", "didWarnAboutKeySpread", "node", "React", "require$$0", "createTask", "callStackForError", "unknownOwnerDebugStack", "unknownOwnerDebugTask", "reactJsxRuntime_development", "trackActualOwner", "jsxRuntimeModule", "require$$1", "hasOwn", "classNames", "classes", "i", "arg", "appendClass", "parseValue", "newClass", "module", "DEFAULT_TRACKING_URL", "globalConfig", "setAdMeshTrackerConfig", "useAdMeshTracker", "isTracking", "setIsTracking", "useState", "error", "setError", "mergedConfig", "useMemo", "log", "useCallback", "message", "data", "sendTrackingEvent", "eventType", "errorMsg", "payload", "lastError", "attempt", "response", "result", "err", "resolve", "trackClick", "trackView", "trackConversion", "buildAdMeshLink", "baseLink", "adId", "additionalParams", "url", "extractTrackingData", "recommendation", "additionalData", "AdMeshLinkTracker", "admeshLink", "productId", "trackingData", "className", "style", "elementRef", "useRef", "hasTrackedView", "useEffect", "observer", "entries", "entry", "handleClick", "event", "jsx", "ADMESH_STYLES", "stylesInjected", "useAdMeshStyles", "styleElement", "existingStyle", "getRecommendationLabel", "matchScore", "customLabels", "getLabelTooltip", "_label", "getSectionDisclosure", "hasHighMatches", "isExpanded", "getInlineDisclosure", "compact", "getInlineTooltip", "getBadgeText", "badgeType", "getCtaText", "context", "productName", "hasHighQualityMatches", "recommendations", "rec", "getPoweredByText", "AdMeshProductCard", "theme", "showMatchScore", "showBadges", "variation", "setIsExpanded", "badges", "generatedBadges", "aiKeywords", "_a", "keyword", "ai", "badge", "inlineDisclosure", "inlineTooltip", "matchScorePercentage", "content", "variations", "cardClasses", "cardStyle", "_b", "_c", "_d", "_e", "_f", "_g", "_h", "_i", "_j", "jsxs", "_k", "_l", "e", "feature", "j", "integration", "Fragment", "_m", "AdMeshCompareTable", "maxProducts", "showMatchScores", "showFeatures", "productsToCompare", "containerClasses", "containerStyle", "product", "index", "badgeTypeVariants", "badgeTypeIcons", "AdMeshBadge", "variant", "size", "effectiveVariant", "icon", "badgeClasses", "AdMeshExpandableUnit", "showPoweredBy", "initialExpanded", "sections", "ctaText", "collapsible", "handleToggleExpand", "apiFeatureSections", "defaultSections", "displaySections", "displayCtaText", "colors", "customStyles", "section", "AdMeshInlineRecommendation", "showReason", "AdMeshConversationSummary", "conversation<PERSON><PERSON><PERSON><PERSON>", "showTopRecommendations", "onRecommendationClick", "onStartNewConversation", "topRecommendations", "a", "b", "AdMeshCitationReference", "citationNumber", "citationStyle", "showTooltip", "onHover", "isHovered", "setIsHovered", "handleMouseEnter", "handleMouseLeave", "getCitationDisplay", "citationClasses", "AdMeshCitationUnit", "conversationText", "showCitationList", "onCitationHover", "hoveredRecommendation", "setHoveredRecommendation", "processedContent", "processedText", "citationMap", "title", "titleRegex", "match", "keywords", "inserted", "keywordRegex", "renderTextWithCitations", "text", "part", "citationMatch", "AdMeshConversationalUnit", "sessionId", "on<PERSON><PERSON><PERSON>", "isVisible", "setIsVisible", "hasAnimated", "setHasAnimated", "timer", "maxRecommendations", "displayRecommendations", "handleRecommendationClick", "handle<PERSON><PERSON><PERSON>", "renderContent", "AdMeshChatMessage", "isUser", "isAssistant", "messageClasses", "bubbleClasses", "formatTime", "timestamp", "AdMeshChatInput", "placeholder", "disabled", "suggestions", "onSendMessage", "setMessage", "showSuggestions", "setShowSuggestions", "filteredSuggestions", "setFilteredSuggestions", "inputRef", "handleInputChange", "filtered", "suggestion", "handleKeyDown", "handleSend", "trimmedMessage", "handleSuggestionClick", "inputClasses", "sendButtonClasses", "AdMeshChatInterface", "messages", "isLoading", "messagesEndRef", "messagesContainerRef", "displayMessages", "AdMeshFloatingChat", "subtitle", "controlledIsOpen", "onToggle", "autoRecommendations", "autoRecommendationTrigger", "showInputField", "autoShowRecommendations", "onAutoRecommendationDismiss", "internalIsOpen", "setInternalIsOpen", "setMessages", "setIsLoading", "hasInteracted", "setHasInteracted", "isOpen", "welcomeMessage", "autoMessage", "prev", "msg", "handleToggle", "handleSendMessage", "messageContent", "userMessage", "errorMessage", "getChatDimensions", "chatClasses", "AdMeshSidebarHeader", "isCollapsed", "onSearch", "showSearch", "searchQuery", "setSearch<PERSON>uery", "isSearchFocused", "setIsSearchFocused", "isMobile", "setIsMobile", "checkMobile", "handleSearchChange", "handleSearchClear", "headerClasses", "AdMeshSidebarContent", "displayMode", "showFilters", "setShowFilters", "activeTab", "setActiveTab", "tabRecommendations", "contentClasses", "renderRecommendations", "AdMeshSidebar", "containerMode", "setIsCollapsed", "filters", "originalStyle", "interval", "filteredRecommendations", "query", "cat", "handleSearch", "sidebarClasses", "AdMeshAutoRecommendationWidget", "trigger", "position", "autoShow", "showDelay", "getWidgetDimensions", "getPositionClasses", "createAdMeshTheme", "customTheme", "baseTheme", "createDarkTheme", "themePresets", "mergeThemes", "themes", "merged", "themeFromCSSProperties", "element", "computedStyle", "VERSION", "DEFAULT_CONFIG"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACWiB,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;IDA7B,IAAIA,IAAqB,OAAO,GAAA,CAAI,4BAA4B,GAC9DC,IAAsB,OAAO,GAAA,CAAI,gBAAgB;IACnD,SAASC,EAAQC,CAAAA,EAAMC,CAAAA,EAAQC,CAAAA,EAAU;QACvC,IAAIC,IAAM;QAGV,IAFWD,MAAX,KAAA,KAAA,CAAwBC,IAAM,KAAKD,CAAAA,GACxBD,EAAO,GAAA,KAAlB,KAAA,KAAA,CAA0BE,IAAM,KAAKF,EAAO,GAAA,GACxC,SAASA,GAAQ;YACnBC,IAAW,CAAA;YACX,IAAA,IAASE,KAAYH,EACTG,MAAV,SAAA,CAAuBF,CAAAA,CAASE,CAAQ,CAAA,GAAIH,CAAAA,CAAOG,CAAQ,CAAA;QAAA,MACxD,CAAAF,IAAWD;QAClB,OAAAA,IAASC,EAAS,GAAA,EACX;YACL,UAAUL;YACV,MAAMG;YACN,KAAKG;YACL,KAAgBF,MAAX,KAAA,IAAoBA,IAAS;YAClC,OAAOC;QAAA;IAEX;IACA,OAAAG,EAAA,QAAA,GAAmBP,GACnBO,EAAA,GAAA,GAAcN,GACdM,EAAA,IAAA,GAAeN,GAAAA;;;;;;;;;;;;;8ECtBf,gBACG,WAAY;QACX,SAASO,EAAyBN,CAAAA,EAAM;YACtC,IAAYA,KAAR,KAAc,CAAA,OAAO;YACzB,IAAmB,OAAOA,KAAtB,YACF,OAAOA,EAAK,QAAA,KAAaO,IACrB,OACAP,EAAK,WAAA,IAAeA,EAAK,IAAA,IAAQ;YACvC,IAAiB,OAAOA,KAApB,SAA0B,CAAA,OAAOA;YACrC,OAAQA,GAAI;gBACV,KAAKF;oBACH,OAAO;gBACT,KAAKU;oBACH,OAAO;gBACT,KAAKC;oBACH,OAAO;gBACT,KAAKC;oBACH,OAAO;gBACT,KAAKC;oBACH,OAAO;gBACT,KAAKC;oBACH,OAAO;YACjB;YACM,IAAiB,OAAOZ,KAApB,UACF,OACgB,OAAOA,EAAK,GAAA,IAAzB,YACC,QAAQ,KAAA,CACN,sHAEJA,EAAK,QAAA,EACf;gBACU,KAAKa;oBACH,OAAO;gBACT,KAAKC;oBACH,OAAA,CAAQd,EAAK,WAAA,IAAe,SAAA,IAAa;gBAC3C,KAAKe;oBACH,OAAA,CAAQf,EAAK,QAAA,CAAS,WAAA,IAAe,SAAA,IAAa;gBACpD,KAAKgB;oBACH,IAAIC,IAAYjB,EAAK,MAAA;oBACrB,OAAAA,IAAOA,EAAK,WAAA,EACZA,KAAAA,CACIA,IAAOiB,EAAU,WAAA,IAAeA,EAAU,IAAA,IAAQ,IACnDjB,IAAcA,MAAP,KAAc,gBAAgBA,IAAO,MAAM,YAAA,GAC9CA;gBACT,KAAKkB;oBACH,OACGD,IAAYjB,EAAK,WAAA,IAAe,MACxBiB,MAAT,OACIA,IACAX,EAAyBN,EAAK,IAAI,KAAK;gBAE/C,KAAKmB;oBACHF,IAAYjB,EAAK,QAAA,EACjBA,IAAOA,EAAK,KAAA;oBACZ,IAAI;wBACF,OAAOM,EAAyBN,EAAKiB,CAAS,CAAC;oBAAA,EAAA,OACrC,CAAA;YACxB;YACM,OAAO;QACb;QACI,SAASG,EAAmBC,CAAAA,EAAO;YACjC,OAAO,KAAKA;QAClB;QACI,SAASC,EAAuBD,CAAAA,EAAO;YACrC,IAAI;gBACFD,EAAmBC,CAAK;gBACxB,IAAIE,IAA2B,CAAA;YAAA,EAAA,OACrB;gBACVA,IAA2B,CAAA;YACnC;YACM,IAAIA,GAA0B;gBAC5BA,IAA2B;gBAC3B,IAAIC,IAAwBD,EAAyB,KAAA,EACjDE,IACc,OAAO,UAAtB,cACC,OAAO,WAAA,IACPJ,CAAAA,CAAM,OAAO,WAAW,CAAA,IAC1BA,EAAM,WAAA,CAAY,IAAA,IAClB;gBACF,OAAAG,EAAsB,IAAA,CACpBD,GACA,4GACAE,IAEKL,EAAmBC,CAAK;YACvC;QACA;QACI,SAASK,EAAY1B,CAAAA,EAAM;YACzB,IAAIA,MAASF,EAAqB,CAAA,OAAO;YACzC,IACe,OAAOE,KAApB,YACSA,MAAT,QACAA,EAAK,QAAA,KAAamB,GAElB,OAAO;YACT,IAAI;gBACF,IAAIQ,IAAOrB,EAAyBN,CAAI;gBACxC,OAAO2B,IAAO,MAAMA,IAAO,MAAM;YAAA,EAAA,OACvB;gBACV,OAAO;YACf;QACA;QACI,SAASC,IAAW;YAClB,IAAIC,IAAaC,EAAqB,CAAA;YACtC,OAAgBD,MAAT,OAAsB,OAAOA,EAAW,QAAA,CAAQ;QAC7D;QACI,SAASE,IAAe;YACtB,OAAO,MAAM,uBAAuB;QAC1C;QACI,SAASC,EAAY/B,CAAAA,EAAQ;YAC3B,IAAIgC,EAAe,IAAA,CAAKhC,GAAQ,KAAK,GAAG;gBACtC,IAAIiC,IAAS,OAAO,wBAAA,CAAyBjC,GAAQ,KAAK,EAAE,GAAA;gBAC5D,IAAIiC,KAAUA,EAAO,cAAA,CAAgB,CAAA,OAAO,CAAA;YACpD;YACM,OAAkBjC,EAAO,GAAA,KAAlB,KAAA;QACb;QACI,SAASkC,EAA2BC,CAAAA,EAAOC,CAAAA,EAAa;YACtD,SAASC,IAAwB;gBAC/BC,KAAAA,CACIA,IAA6B,CAAA,GAC/B,QAAQ,KAAA,CACN,2OACAF,EACZ;YACA;YACMC,EAAsB,cAAA,GAAiB,CAAA,GACvC,OAAO,cAAA,CAAeF,GAAO,OAAO;gBAClC,KAAKE;gBACL,cAAc,CAAA;YACtB,CAAO;QACP;QACI,SAASE,IAAyC;YAChD,IAAIC,IAAgBnC,EAAyB,IAAA,CAAK,IAAI;YACtD,OAAAoC,CAAAA,CAAuBD,CAAa,CAAA,IAAA,CAChCC,CAAAA,CAAuBD,CAAa,CAAA,GAAI,CAAA,GAC1C,QAAQ,KAAA,CACN,8IACV,GACMA,IAAgB,IAAA,CAAK,KAAA,CAAM,GAAA,EACTA,MAAX,KAAA,IAA2BA,IAAgB;QACxD;QACI,SAASE,EACP3C,CAAAA,EACAG,CAAAA,EACAyC,CAAAA,EACAC,CAAAA,EACAC,CAAAA,EACAV,CAAAA,EACAW,EAAAA,EACAC,EAAAA,EACA;YACA,OAAAJ,IAAOR,EAAM,GAAA,EACbpC,IAAO;gBACL,UAAUH;gBACV,MAAMG;gBACN,KAAKG;gBACL,OAAOiC;gBACP,QAAQU;YAAA,GAAA,CAEWF,MAAX,KAAA,IAAkBA,IAAO,IAAA,MAAnC,OACI,OAAO,cAAA,CAAe5C,GAAM,OAAO;gBACjC,YAAY,CAAA;gBACZ,KAAKwC;YAAA,CACN,IACD,OAAO,cAAA,CAAexC,GAAM,OAAO;gBAAE,YAAY,CAAA;gBAAI,OAAO;YAAA,CAAM,GACtEA,EAAK,MAAA,GAAS,CAAA,GACd,OAAO,cAAA,CAAeA,EAAK,MAAA,EAAQ,aAAa;gBAC9C,cAAc,CAAA;gBACd,YAAY,CAAA;gBACZ,UAAU,CAAA;gBACV,OAAO;YACf,CAAO,GACD,OAAO,cAAA,CAAeA,GAAM,cAAc;gBACxC,cAAc,CAAA;gBACd,YAAY,CAAA;gBACZ,UAAU,CAAA;gBACV,OAAO;YACf,CAAO,GACD,OAAO,cAAA,CAAeA,GAAM,eAAe;gBACzC,cAAc,CAAA;gBACd,YAAY,CAAA;gBACZ,UAAU,CAAA;gBACV,OAAO+C;YACf,CAAO,GACD,OAAO,cAAA,CAAe/C,GAAM,cAAc;gBACxC,cAAc,CAAA;gBACd,YAAY,CAAA;gBACZ,UAAU,CAAA;gBACV,OAAOgD;YACf,CAAO,GACD,OAAO,MAAA,IAAA,CAAW,OAAO,MAAA,CAAOhD,EAAK,KAAK,GAAG,OAAO,MAAA,CAAOA,CAAI,CAAA,GACxDA;QACb;QACI,SAASiD,EACPjD,CAAAA,EACAC,CAAAA,EACAC,CAAAA,EACAgD,CAAAA,EACAL,CAAAA,EACAD,CAAAA,EACAG,EAAAA,EACAC,EAAAA,EACA;YACA,IAAIG,IAAWlD,EAAO,QAAA;YACtB,IAAekD,MAAX,KAAA,GACF,IAAID,GACF,IAAIE,EAAYD,CAAQ,GAAG;gBACzB,IACED,IAAmB,GACnBA,IAAmBC,EAAS,MAAA,EAC5BD,IAEAG,EAAkBF,CAAAA,CAASD,CAAgB,CAAC;gBAC9C,OAAO,MAAA,IAAU,OAAO,MAAA,CAAOC,CAAQ;YAAA,OAEvC,QAAQ,KAAA,CACN;iBAEDE,EAAkBF,CAAQ;YACjC,IAAIlB,EAAe,IAAA,CAAKhC,GAAQ,KAAK,GAAG;gBACtCkD,IAAW7C,EAAyBN,CAAI;gBACxC,IAAIsD,IAAO,OAAO,IAAA,CAAKrD,CAAM,EAAE,MAAA,CAAO,SAAUsD,EAAAA,EAAG;oBACjD,OAAiBA,OAAV;gBACjB,CAAS;gBACDL,IACE,IAAII,EAAK,MAAA,GACL,oBAAoBA,EAAK,IAAA,CAAK,SAAS,IAAI,WAC3C,kBACNE,CAAAA,CAAsBL,IAAWD,CAAgB,CAAA,IAAA,CAC7CI,IACA,IAAIA,EAAK,MAAA,GAAS,MAAMA,EAAK,IAAA,CAAK,SAAS,IAAI,WAAW,MAC5D,QAAQ,KAAA,CACN,CAAA;;;;;iCAAA,CAAA,EACAJ,GACAC,GACAG,GACAH,IAEDK,CAAAA,CAAsBL,IAAWD,CAAgB,CAAA,GAAI,CAAA,CAAA;YAChE;YAMM,IALAC,IAAW,MACAjD,MAAX,KAAA,KAAA,CACGoB,EAAuBpB,CAAQ,GAAIiD,IAAW,KAAKjD,CAAAA,GACtD8B,EAAY/B,CAAM,KAAA,CACfqB,EAAuBrB,EAAO,GAAG,GAAIkD,IAAW,KAAKlD,EAAO,GAAA,GAC3D,SAASA,GAAQ;gBACnBC,IAAW,CAAA;gBACX,IAAA,IAASE,MAAYH,EACTG,OAAV,SAAA,CAAuBF,CAAAA,CAASE,EAAQ,CAAA,GAAIH,CAAAA,CAAOG,EAAQ,CAAA;YAAA,MACxD,CAAAF,IAAWD;YAClB,OAAAkD,KACEhB,EACEjC,GACe,OAAOF,KAAtB,aACIA,EAAK,WAAA,IAAeA,EAAK,IAAA,IAAQ,YACjCA,IAED2C,EACL3C,GACAmD,GACAP,GACAC,GACAjB,EAAQ,GACR1B,GACA6C,IACAC;QAER;QACI,SAASK,EAAkBI,CAAAA,EAAM;YAClB,OAAOA,KAApB,YACWA,MAAT,QACAA,EAAK,QAAA,KAAa5D,KAClB4D,EAAK,MAAA,IAAA,CACJA,EAAK,MAAA,CAAO,SAAA,GAAY,CAAA;QACjC;QACI,IAAIC,IAAQC,wKAAAA,EACV9D,IAAqB,OAAO,GAAA,CAAI,4BAA4B,GAC5DgB,IAAoB,OAAO,GAAA,CAAI,cAAc,GAC7Cf,IAAsB,OAAO,GAAA,CAAI,gBAAgB,GACjDW,IAAyB,OAAO,GAAA,CAAI,mBAAmB,GACvDD,IAAsB,OAAO,GAAA,CAAI,gBAAgB,GAE/CO,IAAsB,OAAO,GAAA,CAAI,gBAAgB,GACnDD,IAAqB,OAAO,GAAA,CAAI,eAAe,GAC/CE,IAAyB,OAAO,GAAA,CAAI,mBAAmB,GACvDN,IAAsB,OAAO,GAAA,CAAI,gBAAgB,GACjDC,IAA2B,OAAO,GAAA,CAAI,qBAAqB,GAC3DO,IAAkB,OAAO,GAAA,CAAI,YAAY,GACzCC,IAAkB,OAAO,GAAA,CAAI,YAAY,GACzCP,IAAsB,OAAO,GAAA,CAAI,gBAAgB,GACjDL,IAAyB,OAAO,GAAA,CAAI,wBAAwB,GAC5DuB,IACE4B,EAAM,+DAAA,EACRzB,IAAiB,OAAO,SAAA,CAAU,cAAA,EAClCmB,IAAc,MAAM,OAAA,EACpBQ,IAAa,QAAQ,UAAA,GACjB,QAAQ,UAAA,GACR,WAAY;YACV,OAAO;QAAA;QAEfF,IAAQ;YACN,4BAA4B,SAAUG,CAAAA,EAAmB;gBACvD,OAAOA,EAAiB;YAChC;QAAA;QAEI,IAAItB,GACAG,IAAyB,CAAA,GACzBoB,IAAyBJ,CAAAA,CAAM,0BAA0B,CAAA,CAAE,IAAA,CAC7DA,GACA3B,MAEEgC,IAAwBH,EAAWlC,EAAYK,CAAY,CAAC,GAC5DyB,IAAwB,CAAA;QAC5BQ,GAAA,QAAA,GAAmBlE,GACnBkE,GAAA,GAAA,GAAc,SAAUhE,CAAAA,EAAMC,CAAAA,EAAQC,CAAAA,EAAU2C,CAAAA,EAAQD,CAAAA,EAAM;YAC5D,IAAIqB,IACF,MAAMnC,EAAqB,0BAAA;YAC7B,OAAOmB,EACLjD,GACAC,GACAC,GACA,CAAA,GACA2C,GACAD,GACAqB,IACI,MAAM,uBAAuB,IAC7BH,GACJG,IAAmBL,EAAWlC,EAAY1B,CAAI,CAAC,IAAI+D;WAGvDC,GAAA,IAAA,GAAe,SAAUhE,CAAAA,EAAMC,CAAAA,EAAQC,CAAAA,EAAU2C,CAAAA,EAAQD,CAAAA,EAAM;YAC7D,IAAIqB,IACF,MAAMnC,EAAqB,0BAAA;YAC7B,OAAOmB,EACLjD,GACAC,GACAC,GACA,CAAA,GACA2C,GACAD,GACAqB,IACI,MAAM,uBAAuB,IAC7BH,GACJG,IAAmBL,EAAWlC,EAAY1B,CAAI,CAAC,IAAI+D;;IAG3D,EAAG,CAAA,GAAA;;;;0BCnWC,QAAQ,IAAI,aAAa,eAC3BG,GAAA,UAAiBP,GAAA,qBAEjBO,GAAA,OAAA,GAAiBC,GAAA,CAAA,GAAA,GAAA,OAAA;;;;;;;;;;;;QCEnB,CAAC,WAAY;YAGZ,IAAIC,IAAS,CAAA,EAAG,cAAA;YAEhB,SAASC,IAAc;gBAGtB,IAAA,IAFIC,IAAU,IAELC,IAAI,GAAGA,IAAI,UAAU,MAAA,EAAQA,IAAK;oBAC1C,IAAIC,IAAM,SAAA,CAAUD,CAAC,CAAA;oBACjBC,KAAAA,CACHF,IAAUG,EAAYH,GAASI,EAAWF,CAAG,CAAC,CAAA;gBAElD;gBAEE,OAAOF;YACT;YAEC,SAASI,EAAYF,CAAAA,EAAK;gBACzB,IAAI,OAAOA,KAAQ,YAAY,OAAOA,KAAQ,UAC7C,OAAOA;gBAGR,IAAI,OAAOA,KAAQ,UAClB,OAAO;gBAGR,IAAI,MAAM,OAAA,CAAQA,CAAG,GACpB,OAAOH,EAAW,KAAA,CAAM,MAAMG,CAAG;gBAGlC,IAAIA,EAAI,QAAA,KAAa,OAAO,SAAA,CAAU,QAAA,IAAY,CAACA,EAAI,QAAA,CAAS,QAAA,CAAQ,EAAG,QAAA,CAAS,eAAe,GAClG,OAAOA,EAAI,QAAA,CAAQ;gBAGpB,IAAIF,IAAU;gBAEd,IAAA,IAASnE,KAAOqE,EACXJ,EAAO,IAAA,CAAKI,GAAKrE,CAAG,KAAKqE,CAAAA,CAAIrE,CAAG,CAAA,IAAA,CACnCmE,IAAUG,EAAYH,GAASnE,CAAG,CAAA;gBAIpC,OAAOmE;YACT;YAEC,SAASG,EAAapD,CAAAA,EAAOsD,CAAAA,EAAU;gBACtC,OAAKA,IAIDtD,IACIA,IAAQ,MAAMsD,IAGftD,IAAQsD,IAPPtD;YAQV;YAEsCuD,EAAO,OAAA,GAAA,CAC3CP,EAAW,OAAA,GAAUA,GACrBO,EAAAA,OAAAA,GAAiBP,CAAAA,IAOjB,OAAO,UAAA,GAAaA;QAEtB,CAAA;;;;kCCxEMQ,KAAuB;AAW7B,IAAIC,KAA+B;IACjC,YAAYD;IACZ,SAAS,CAAA;IACT,OAAO,CAAA;IACP,eAAe;IACf,YAAY;AACd;AAEO,MAAME,KAAyB,CAAC9E,MAAoC;IACzE6E,KAAe;QAAE,GAAGA,EAAAA;QAAc,GAAG7E,CAAAA;IAAA;AACvC,GAEa+E,KAAmB,CAAC/E,MAA6D;IAC5F,MAAM,CAACgF,GAAYC,CAAa,CAAA,gLAAIC,EAAS,CAAA,CAAK,GAC5C,CAACC,GAAOC,CAAQ,CAAA,IAAIF,4KAAAA,EAAwB,IAAI,GAEhDG,sKAAeC,UAAAA,EAAQ,IAAA,CAAO;YAAE,GAAGT,EAAAA;YAAc,GAAG7E,CAAAA;QAAA,CAAA,GAAW;QAACA,CAAM;KAAC,GAEvEuF,oLAAMC,EAAY,CAACC,GAAiBC,MAAmB;QACvDL,EAAa,KAAA,IACf,QAAQ,GAAA,CAAI,CAAA,iBAAA,EAAoBI,CAAO,EAAA,EAAIC,CAAI;IACjD,GACC;QAACL,EAAa,KAAK;KAAC,GAEjBM,oLAAoBH,EAAY,OACpCI,GACAF,MACkB;QAClB,IAAI,CAACL,EAAa,OAAA,EAAS;YACzBE,EAAI,qCAAqC;gBAAE,WAAAK;gBAAW,MAAAF;YAAA,CAAM;YAC5D;QAAA;QAGF,IAAI,CAACA,EAAK,IAAA,IAAQ,CAACA,EAAK,UAAA,EAAY;YAClC,MAAMG,IAAW;YACjBN,EAAIM,GAAUH,CAAI,GAClBN,EAASS,CAAQ;YACjB;QAAA;QAGFZ,EAAc,CAAA,CAAI,GAClBG,EAAS,IAAI;QAEb,MAAMU,IAAU;YACd,YAAYF;YACZ,OAAOF,EAAK,IAAA;YACZ,aAAaA,EAAK,UAAA;YAClB,YAAYA,EAAK,SAAA;YACjB,SAASA,EAAK,MAAA;YACd,YAAYA,EAAK,SAAA;YACjB,SAASA,EAAK,OAAA;YACd,iBAAiBA,EAAK,cAAA;YACtB,UAAUA,EAAK,QAAA;YACf,WAAA,AAAW,aAAA,GAAA,IAAI,KAAA,EAAO,WAAA,CAAA;YACtB,YAAY,UAAU,SAAA;YACtB,UAAU,SAAS,QAAA;YACnB,UAAU,OAAO,QAAA,CAAS,IAAA;QAAA;QAG5BH,EAAI,CAAA,QAAA,EAAWK,CAAS,CAAA,MAAA,CAAA,EAAUE,CAAO;QAEzC,IAAIC,IAA0B;QAE9B,IAAA,IAASC,IAAU,GAAGA,KAAAA,CAAYX,EAAa,aAAA,IAAiB,CAAA,GAAIW,IAClE,IAAI;YACF,MAAMC,IAAW,MAAM,MAAM,GAAGZ,EAAa,UAAU,CAAA,OAAA,CAAA,EAAW;gBAChE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAAA;gBAElB,MAAM,KAAK,SAAA,CAAUS,CAAO;YAAA,CAC7B;YAED,IAAI,CAACG,EAAS,EAAA,EACZ,MAAM,IAAI,MAAM,CAAA,KAAA,EAAQA,EAAS,MAAM,CAAA,EAAA,EAAKA,EAAS,UAAU,EAAE;YAGnE,MAAMC,IAAS,MAAMD,EAAS,IAAA,CAAA;YAC9BV,EAAI,GAAGK,CAAS,CAAA,2BAAA,CAAA,EAA+BM,CAAM,GACrDjB,EAAc,CAAA,CAAK;YACnB;QAAA,EAAA,OAEOkB,GAAK;YACZJ,IAAYI,GACZZ,EAAI,CAAA,QAAA,EAAWS,CAAO,CAAA,YAAA,EAAeJ,CAAS,CAAA,MAAA,CAAA,EAAUO,CAAG,GAEvDH,IAAAA,CAAWX,EAAa,aAAA,IAAiB,CAAA,KAC3C,MAAM,IAAI,QAAQ,CAAA,IAChB,WAAWe,GAAAA,CAAUf,EAAa,UAAA,IAAc,GAAA,IAAQW,CAAO;QAEnE;QAKJ,MAAMH,IAAW,CAAA,gBAAA,EAAmBD,CAAS,CAAA,aAAA,EAAgBP,EAAa,aAAa,CAAA,WAAA,EAAcU,KAAA,OAAA,KAAA,IAAAA,EAAW,OAAO,EAAA;QACvHR,EAAIM,GAAUE,CAAS,GACvBX,EAASS,CAAQ,GACjBZ,EAAc,CAAA,CAAK;IAAA,GAClB;QAACI;QAAcE,CAAG;KAAC,GAEhBc,IAAab,gLAAAA,EAAY,OAAOE,IAC7BC,EAAkB,SAASD,CAAI,GACrC;QAACC,CAAiB;KAAC,GAEhBW,oLAAYd,EAAY,OAAOE,IAC5BC,EAAkB,QAAQD,CAAI,GACpC;QAACC,CAAiB;KAAC,GAEhBY,oLAAkBf,EAAY,OAAOE,IAAAA,CACrC,CAACA,EAAK,OAAA,IAAW,CAACA,EAAK,cAAA,IACzBH,EAAI,mEAAmEG,CAAI,GAEtEC,EAAkB,cAAcD,CAAI,CAAA,GAC1C;QAACC;QAAmBJ,CAAG;KAAC;IAE3B,OAAO;QACL,YAAAc;QACA,WAAAC;QACA,iBAAAC;QACA,YAAAvB;QACA,OAAAG;IAAA;AAEJ,GAGaqB,KAAkB,CAC7BC,GACAC,GACAC,MACW;IACX,IAAI;QACF,MAAMC,IAAM,IAAI,IAAIH,CAAQ;QAC5B,OAAAG,EAAI,YAAA,CAAa,GAAA,CAAI,SAASF,CAAI,GAClCE,EAAI,YAAA,CAAa,GAAA,CAAI,cAAc,QAAQ,GAC3CA,EAAI,YAAA,CAAa,GAAA,CAAI,cAAc,gBAAgB,GAE/CD,KACF,OAAO,OAAA,CAAQA,CAAgB,EAAE,OAAA,CAAQ,CAAC,CAACzG,GAAKkB,CAAK,CAAA,KAAM;YACzDwF,EAAI,YAAA,CAAa,GAAA,CAAI1G,GAAKkB,CAAK;QAAA,CAChC,GAGIwF,EAAI,QAAA,CAAA;IAAS,EAAA,OACbT,GAAK;QACZ,OAAA,QAAQ,IAAA,CAAK,qDAAqDM,GAAUN,CAAG,GACxEM;IAAA;AAEX,GAGaI,KAAsB,CACjCC,GACAC,IAAAA,CAEO;QACL,MAAMD,EAAe,KAAA;QACrB,YAAYA,EAAe,WAAA;QAC3B,WAAWA,EAAe,UAAA;QAC1B,GAAGC,CAAAA;IAAA,CAAA,GC1KMC,IAAsD,CAAC,EAClE,MAAAN,CAAAA,EACA,YAAAO,CAAAA,EACA,WAAAC,CAAAA,EACA,UAAAhE,CAAAA,EACA,cAAAiE,CAAAA,EACA,WAAAC,CAAAA,EACA,OAAAC,CAAAA,EACF,KAAM;IACJ,MAAM,EAAE,YAAAhB,CAAAA,EAAY,WAAAC,CAAAA,CAAA,CAAA,GAAcvB,GAAA,GAC5BuC,sKAAaC,SAAAA,EAAuB,IAAI,GACxCC,sKAAiBD,SAAAA,EAAO,CAAA,CAAK;kLAGnCE,EAAU,MAAM;QACd,IAAI,CAACH,EAAW,OAAA,IAAWE,EAAe,OAAA,CAAS,CAAA;QAEnD,MAAME,IAAW,IAAI,qBACnB,CAACC,MAAY;YACXA,EAAQ,OAAA,CAAQ,CAACC,MAAU;gBACrBA,EAAM,cAAA,IAAkB,CAACJ,EAAe,OAAA,IAAA,CAC1CA,EAAe,OAAA,GAAU,CAAA,GACzBlB,EAAU;oBACR,MAAAI;oBACA,YAAAO;oBACA,WAAAC;oBACA,GAAGC,CAAAA;gBAAA,CACJ,EAAE,KAAA,CAAM,QAAQ,KAAK,CAAA;YACxB,CACD;QAAA,GAEH;YACE,WAAW;YAAA,2CAAA;YACX,YAAY;QAAA;QAIhB,OAAAO,EAAS,OAAA,CAAQJ,EAAW,OAAO,GAE5B,MAAM;YACXI,EAAS,UAAA,CAAA;QAAW;IACtB,GACC;QAAChB;QAAMO;QAAYC;QAAWC;QAAcb,CAAS;KAAC;IAEzD,MAAMuB,KAAcrC,+KAAAA,EAAY,OAAOsC,MAA4B;QAEjE,IAAI;YACF,MAAMzB,EAAW;gBACf,MAAAK;gBACA,YAAAO;gBACA,WAAAC;gBACA,GAAGC,CAAAA;YAAA,CACJ;QAAA,EAAA,OACMhC,GAAO;YACd,QAAQ,KAAA,CAAM,0BAA0BA,CAAK;QAAA;QAOhC2C,EAAM,MAAA,CACD,OAAA,CAAQ,GAAG,KAI7B,OAAO,IAAA,CAAKb,GAAY,UAAU,qBAAqB;IACzD,GAEC;QAACP;QAAMO;QAAYC;QAAWC;QAAcd,CAAU;KAAC;IAE1D,OACE0B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;QACC,KAAKT;QACL,WAAAF;QACA,SAASS;QACT,OAAO;YACL,QAAQ;YACR,GAAGR,CAAAA;QAAA;QAGJ,UAAAnE;IAAA;AAGP;AAEA8D,EAAkB,WAAA,GAAc;ACvFhC,MAAMgB,KAAgB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,CAAA;AAsoBtB,IAAIC,KAAiB,CAAA;AAEd,MAAMC,KAAkB,MAAM;kLACnCT,EAAU,MAAM;QACd,IAAIQ,GAAgB,CAAA;QAGpB,MAAME,IAAe,SAAS,aAAA,CAAc,OAAO;QACnD,OAAAA,EAAa,EAAA,GAAK,wBAClBA,EAAa,WAAA,GAAcH,IAGtB,SAAS,cAAA,CAAe,sBAAsB,KAAA,CACjD,SAAS,IAAA,CAAK,WAAA,CAAYG,CAAY,GACtCF,KAAiB,CAAA,CAAA,GAIZ,MAAM;YACX,MAAMG,IAAgB,SAAS,cAAA,CAAe,sBAAsB;YAChEA,KAAiB,SAAS,IAAA,CAAK,QAAA,CAASA,CAAa,KAAA,CACvD,SAAS,IAAA,CAAK,WAAA,CAAYA,CAAa,GACvCH,KAAiB,CAAA,CAAA;QACnB;IACF,GACC,EAAE;AACP,GC/oBaI,IAAyB,CACpCvB,GACA9G,IAA2B,CAAA,CAAA,KAChB;IACX,MAAMsI,IAAaxB,EAAe,kBAAA,IAAsB,GAClDyB,IAAevI,EAAO,YAAA,IAAgB,CAAA;IAG5C,OAAIsI,KAAc,MACTC,EAAa,SAAA,IAAa,eAI/BD,KAAc,MACTC,EAAa,YAAA,IAAgB,kBAIlCD,KAAc,MACTC,EAAa,cAAA,IAAkB,oBAIjCA,EAAa,aAAA,IAAiB;AACvC,GAKaC,KAAkB,CAC7B1B,GACA2B,MACW;IACX,MAAMH,IAAaxB,EAAe,kBAAA,IAAsB;IAExD,OAAIwB,KAAc,MACT,kIAGLA,KAAc,MACT,sHAGLA,KAAc,MACT,sHAGF;AACT,GAKaI,KAAuB,CAClCC,IAA0B,CAAA,CAAA,EAC1BC,IAAsB,CAAA,CAAA,GAEjBD,IAIDC,IACK,qFAGF,qLAPE,iKAaEC,KAAsB,CACjC/B,GACAgC,IAAmB,CAAA,CAAA,KACR;IACX,MAAMR,IAAaxB,EAAe,kBAAA,IAAsB;IAExD,OAAIgC,IACK,mBAGLR,KAAc,MACT,eAGLA,KAAc,MACT,kBAGF;AACT,GAKaS,KAAmB,IACvB,uKAMIC,KAAe,CAACC,IAAAA,CACc;QACvC,aAAa;QACb,cAAc;QACd,eAAe;QACf,eAAe;QACf,aAAe;QACf,YAAY;QACZ,UAAY;QACZ,kBAAkB;QAClB,gBAAgB;QAChB,aAAa;QACb,cAAc;QACd,SAAW;QACX,KAAO;QACP,mBAAmB;QACnB,kBAAkB;QAClB,wBAAwB;QACxB,kBAAkB;IAAA,CAAA,CAAA,CAGJA,CAAS,CAAA,IAAKA,GAMnBC,KAAa,CACxBpC,GACAqC,IAA6B,QAAA,KAClB;IACX,MAAMC,IAActC,EAAe,oBAAA,IAAwBA,EAAe,KAAA;IAE1E,OAAIqC,MAAY,SACPC,IAILtC,EAAe,UAAA,IAAcA,EAAe,UAAA,GAAa,IACpD,CAAA,IAAA,EAAOsC,CAAW,EAAA,GAGpB;AACT,GAKaC,KAAwB,CAACC,IAC7BA,EAAgB,IAAA,CAAK,CAAAC,IAAAA,CAAQA,EAAI,kBAAA,IAAsB,CAAA,KAAM,GAAG,GAM5DC,KAAmB,CAACV,IAAmB,CAAA,CAAA,GAC9CA,IACK,sBAGF,qCCzKIW,KAAsD,CAAC,EAClE,gBAAA3C,CAAAA,EACA,OAAA4C,CAAAA,EACA,gBAAAC,IAAiB,CAAA,CAAA,EACjB,YAAAC,IAAa,CAAA,CAAA,EACb,WAAAC,IAAY,SAAA,EACZ,WAAAzC,CAAAA,EACA,OAAAC,CAAAA,EACF,KAAM;;IAEJa,GAAA;IAGA,MAAM,CAACU,GAAYkB,CAAa,CAAA,GAAI5E,6KAAAA,EAAS,CAAA,CAAK,GAE5C6E,sKAASzE,UAAAA,EAAQ,MAAmB;;QACxC,MAAM0E,IAA+B,CAAA,CAAA;QAGhB3B,EAAuBvB,CAAc,MACrC,gBACnBkD,EAAgB,IAAA,CAAK,WAAW,GAI9BlD,EAAe,UAAA,IAAcA,EAAe,UAAA,GAAa,KAC3DkD,EAAgB,IAAA,CAAK,iBAAiB;QAIxC,MAAMC,IAAa;YAAC;YAAM;YAA2B;YAAoB;YAAM,YAAY;SAAA;QAK3F,OAAA,CAAA,CAAA,CAJsBC,IAAApD,EAAe,QAAA,KAAf,OAAA,KAAA,IAAAoD,EAAyB,IAAA,CAAK,CAAAC,IAClDF,EAAW,IAAA,CAAK,CAAAG,IAAMD,EAAQ,WAAA,CAAA,EAAc,QAAA,CAASC,CAAE,CAAC,EAAA,KACrDtD,EAAe,KAAA,CAAM,WAAA,CAAA,EAAc,QAAA,CAAS,IAAI,CAAA,KAGnDkD,EAAgB,IAAA,CAAK,YAAY,GAI/BlD,EAAe,MAAA,IAAUA,EAAe,MAAA,CAAO,MAAA,GAAS,KAC1DA,EAAe,MAAA,CAAO,OAAA,CAAQ,CAAAuD,MAAS;YAEjC;gBAAC;gBAAa;gBAAa;gBAAc;gBAAW;gBAAO,iBAAiB;aAAA,CAAE,QAAA,CAASA,CAAK,KAC5F,CAACL,EAAgB,QAAA,CAASK,CAAkB,KAC9CL,EAAgB,IAAA,CAAKK,CAAkB;QACzC,CACD,GAKIL;IAAA,GACN;QAAClD,CAAc;KAAC,GAGbwD,IAAmBzB,GAAoB/B,GAAgB,CAAA,CAAK,GAC5DyD,IAAgBxB,GAAA,GAGhByB,IAAuB,KAAK,KAAA,CAAM1D,EAAe,kBAAA,GAAqB,GAAG,GAmCzE2D,IAAAA,CAhCsB,MAAM;QAChC,MAAMC,IAAa5D,EAAe,kBAAA;QAElC,OAAI+C,MAAc,WACT;YACL,OAAO/C,EAAe,oBAAA,IAAwBA,EAAe,KAAA;YAC7D,aAAaA,EAAe,0BAAA,IAA8BA,EAAe,WAAA,IAAeA,EAAe,MAAA;YACvG,SAASA,EAAe,oBAAA,IAAwBA,EAAe,KAAA;YAC/D,UAAU,CAAA;QAAA,IAEH+C,MAAc,cAAca,KAAA,QAAAA,EAAY,QAAA,GAC1C;YACL,OAAOA,EAAW,QAAA,CAAS,GAAA,IAAO5D,EAAe,oBAAA,IAAwBA,EAAe,KAAA;YACxF,aAAa4D,EAAW,QAAA,CAAS,IAAA;YACjC,SAASA,EAAW,QAAA,CAAS,GAAA,IAAO5D,EAAe,oBAAA,IAAwBA,EAAe,KAAA;QAAA,IAEnF+C,MAAc,eAAea,KAAA,QAAAA,EAAY,SAAA,GAC3C;YACL,OAAO5D,EAAe,oBAAA,IAAwBA,EAAe,KAAA;YAC7D,aAAa4D,EAAW,SAAA,CAAU,IAAA;YAClC,SAASA,EAAW,SAAA,CAAU,GAAA,IAAO5D,EAAe,oBAAA,IAAwBA,EAAe,KAAA;QAAA,IAItF;YACL,OAAOA,EAAe,oBAAA,IAAwBA,EAAe,KAAA;YAC7D,aAAaA,EAAe,0BAAA,IAA8BA,EAAe,WAAA,IAAeA,EAAe,MAAA;YACvG,SAASA,EAAe,oBAAA,IAAwBA,EAAe,KAAA;QAAA;IAEnE,CAAA,EAGc,GAEV6D,IAAcvG,EAClB,oBACA,eACA,8OACAgD,IAGIwD,IAAYlB,IAAQ;QACxB,oBAAoBA,EAAM,YAAA,IAAgBA,EAAM,WAAA,IAAe;QAC/D,sBAAsBA,EAAM,cAAA,IAAkB;QAC9C,mBAAmBA,EAAM,WAAA,IAAe;QACxC,uBAAuBA,EAAM,eAAA;QAC7B,oBAAoBA,EAAM,YAAA;QAC1B,mBAAmBA,EAAM,WAAA;QACzB,iBAAiBA,EAAM,SAAA;QACvB,2BAA2BA,EAAM,kBAAA;QACjC,mBAAmBA,EAAM,YAAA,IAAgB;QACzC,sBAAA,CAAsBQ,IAAAR,EAAM,OAAA,KAAN,OAAA,KAAA,IAAAQ,EAAe,KAAA;QACrC,sBAAA,CAAsBW,IAAAnB,EAAM,OAAA,KAAN,OAAA,KAAA,IAAAmB,EAAe,MAAA;QACrC,sBAAA,CAAsBC,IAAApB,EAAM,OAAA,KAAN,OAAA,KAAA,IAAAoB,EAAe,KAAA;QACrC,uBAAA,CAAuBC,IAAArB,EAAM,OAAA,KAAN,OAAA,KAAA,IAAAqB,EAAe,KAAA;QACtC,uBAAA,CAAuBC,IAAAtB,EAAM,OAAA,KAAN,OAAA,KAAA,IAAAsB,EAAe,MAAA;QACtC,uBAAA,CAAuBC,IAAAvB,EAAM,OAAA,KAAN,OAAA,KAAA,IAAAuB,EAAe,KAAA;QACtC,yBAAA,CAAyBC,IAAAxB,EAAM,QAAA,KAAN,OAAA,KAAA,IAAAwB,EAAgB,KAAA;QACzC,2BAAA,CAA2BC,IAAAzB,EAAM,QAAA,KAAN,OAAA,KAAA,IAAAyB,EAAgB,IAAA;QAC3C,yBAAA,CAAyBC,IAAA1B,EAAM,QAAA,KAAN,OAAA,KAAA,IAAA0B,EAAgB,KAAA;QACzC,4BAAA,CAA4BC,IAAA3B,EAAM,QAAA,KAAN,OAAA,KAAA,IAAA2B,EAAgB,KAAA;QAC5C,YAAY3B,EAAM,UAAA;IAAA,IACO,KAAA;IAG3B,OAAIG,MAAc,WAGdyB,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;QACC,WAAWlH,EACT,qCACA,wCACAgD;QAEF,OAAO;YACL,YAAA,CAAYsC,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAc;YACjC,GAAA,CAAG6B,IAAA7B,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAA6B,EAAmB,WAAA;YACtB,GAAGlE,CAAAA;QAAA;QAEL,qBAAmBqC,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAG1B,UAAA;YAAA3B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;gBACC,OAAO;oBACL,UAAU;oBACV,YAAY;oBACZ,OAAA,CAAO2B,KAAA,OAAA,KAAA,IAAAA,EAAO,WAAA,KAAe;oBAC7B,iBAAA,CAAiBA,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA,MAAS,SAAS,YAAY;oBACtD,SAAS;oBACT,cAAc;oBACd,aAAa;gBAAA;gBAEf,OAAOlB,GAAgB1B,GAAgBuB,EAAuBvB,CAAc,CAAC;gBAE5E,UAAA,EAAuBA,CAAc;YAAA;YAIxCwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;gBACC,OAAO;oBACL,OAAA,CAAO5B,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA,MAAS,SAAS,YAAY;oBAC5C,aAAa;gBAAA;gBAGd,UAAA;oBAAAe,EAAQ,WAAA;oBAAa;iBAAA;YAAA;YAIxB1C,aAAAA,GAAAA,EAAAA,GAAAA,CAACf,GAAA;gBACC,MAAMF,EAAe,KAAA;gBACrB,YAAYA,EAAe,WAAA;gBAC3B,WAAWA,EAAe,UAAA;gBAC1B,cAAc;oBACZ,OAAOA,EAAe,KAAA;oBACtB,YAAYA,EAAe,kBAAA;oBAC3B,WAAW;gBAAA;gBAGb,UAAAiB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;oBACC,OAAO;wBACL,OAAA,CAAO2B,KAAA,OAAA,KAAA,IAAAA,EAAO,WAAA,KAAe;wBAC7B,gBAAgB;wBAChB,QAAQ;wBACR,UAAU;wBACV,YAAY;oBAAA;oBAGb,UAAAe,EAAQ,OAAA;gBAAA;YACX;YAIFa,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;gBACC,OAAO;oBACL,UAAU;oBACV,OAAA,CAAO5B,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA,MAAS,SAAS,YAAY;oBAC5C,YAAY;gBAAA;gBAEd,OAAOa;gBACR,UAAA;oBAAA;oBACGD;oBAAiB;iBAAA;YAAA;SACrB;IAAA,KAKFT,MAAc,cAAcA,MAAc,cAG1C9B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;QACC,WAAW3D,EACT,4EACAwE,IACI,qKACA,oHACJxB;QAEF,OAAO;YACL,YAAA,CAAYsC,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAc;YACjC,GAAA,CAAG8B,IAAA9B,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAA8B,EAAmB,WAAA;YACtB,GAAGnE,CAAAA;QAAA;QAEL,qBAAmBqC,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAEzB,UAACd,IAAA,wDAAA;QA+DA0C,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;YACC,WAAU;YACV,OAAOV;YACP,qBAAmBlB,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;YAG1B,UAAA;gBAAA4B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACZ,UAAA;gCAAA1B,KAAcG,EAAO,QAAA,CAAS,WAAW,KACxChC,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;oCACC,WAAU;oCACV,OAAO;wCACL,iBAAA,CAAiB2B,KAAA,OAAA,KAAA,IAAAA,EAAO,YAAA,KAAA,CAAgBA,KAAA,OAAA,KAAA,IAAAA,EAAO,WAAA,KAAe;wCAC9D,cAAA,CAAcA,KAAA,OAAA,KAAA,IAAAA,EAAO,YAAA,KAAgB;oCAAA;oCAEvC,OAAOlB,GAAgB1B,CAA4B;oCAElD,UAAA,GAAa,WAAW;gCAAA;gCAG7BwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oCAAI,WAAU;oCACZ,UAAA;wCAAAxE,EAAe,YAAA,IACdiB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4CACC,KAAKjB,EAAe,YAAA,CAAa,GAAA;4CACjC,KAAK,GAAGA,EAAe,KAAK,CAAA,KAAA,CAAA;4CAC5B,WAAU;4CACV,SAAS,CAAC2E,MAAM;gDAEbA,EAAE,MAAA,CAA4B,KAAA,CAAM,OAAA,GAAU;4CAAA;wCACjD;wCAGJ1D,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;4CAAG,WAAU;4CACX,UAAA,EAAQ,KAAA;wCAAA,CACX;qCAAA;gCAAA,CACF;6BAAA;wBAAA,CACF;wBAEAuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAA;gCAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,UAAA;oCACC,SAAS,IAAMxB,EAAc,CAAA,CAAK;oCAClC,WAAU;oCACV,OAAM;oCAEN,UAAA;wCAAA/B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;4CAAK,UAAA;wCAAA,CAAY;wCAClBA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;4CACjE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;4CAAA,CAAW;wCAAA,CAClF;qCAAA;gCAAA;gCAEFA,aAAAA,GAAAA,EAAAA,GAAAA,CAACf,GAAA;oCACC,MAAMF,EAAe,KAAA;oCACrB,YAAYA,EAAe,WAAA;oCAC3B,WAAWA,EAAe,UAAA;oCAC1B,cAAc;wCACZ,OAAOA,EAAe,KAAA;wCACtB,YAAYA,EAAe,kBAAA;wCAC3B,WAAW;oCAAA;oCAGb,UAAAwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,UAAA;wCAAO,WAAU;wCACf,UAAA;4CAAAzB,MAAc,aAAa,QAAQ;4CAAQ;4CAAEY,EAAQ,OAAA;4CACtD1C,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;gDACtE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;gDAAA,CAA+E;4CAAA,CACtJ;yCAAA;oCAAA,CACF;gCAAA;6BACF;wBAAA,CACF;qBAAA;gBAAA,CACF;gBAGAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,KAAA;wBAAE,WAAU;wBACV,UAAA0C,EAAQ,WAAA;oBAAA,CACX;gBAAA,CACF;gBAGA1C,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,KAAA;wBACC,WAAU;wBACV,OAAOwC;wBAEN,UAAAD;oBAAA;gBAAA,CAEL;gBAGCX,KAAkB,OAAO7C,EAAe,kBAAA,IAAuB,YAC9DwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAA;gCAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;oCAAK,WAAU;oCAAc,UAAA;gCAAA,CAAW;gCACzCuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;oCAAK,WAAU;oCAAyG,UAAA;wCAAAd;wCAAqB;qCAAA;gCAAA,CAAO;6BAAA;wBAAA,CACvJ;wBACAzC,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO,GAAGyC,CAAoB,CAAA,CAAA,CAAA;gCAAA;4BAAI;wBAC7C,CACF;qBAAA;gBAAA,CACF;gBAGFc,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACZ,UAAA;wBAAAxE,EAAe,OAAA,IACdwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;4BAAK,WAAU;4BACd,UAAA;gCAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;oCAAe,MAAK;oCAAO,QAAO;oCAAe,SAAQ;oCACtE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;oCAAA,CAA4I;gCAAA,CACnN;gCACCjB,EAAe,OAAA;6BAAA;wBAAA,CAClB;wBAGDA,EAAe,UAAA,IAAcA,EAAe,UAAA,GAAa,KACxDwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;4BAAK,WAAU;4BACd,UAAA;gCAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;oCAAe,MAAK;oCAAO,QAAO;oCAAe,SAAQ;oCACtE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;oCAAA,CAA4D;gCAAA,CACnI;gCACCjB,EAAe,UAAA;gCAAW;6BAAA;wBAAA,CAC7B;qBAAA;gBAAA,CAEJ;gBAGCA,EAAe,QAAA,IAAYA,EAAe,QAAA,CAAS,MAAA,GAAS,KAC3DwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BAA4D,UAAA;wBAAA,CAE3E;wBACAuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACZ,UAAA;gCAAAxE,EAAe,QAAA,CAAS,KAAA,CAAM,GAAG,CAAC,EAAE,GAAA,CAAI,CAAC4E,GAASC,IACjDL,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;wCAEC,WAAU;wCAEV,UAAA;4CAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gDAAI,WAAU;gDAAwC,MAAK;gDAAO,QAAO;gDAAe,SAAQ;gDAC/F,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;gDAAA,CAAgD;4CAAA,CACvH;4CACC2D;yCAAA;oCAAA,GANIC;gCASR7E,EAAe,QAAA,CAAS,MAAA,GAAS,KAChCwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;oCAAK,WAAU;oCAAqD,UAAA;wCAAA;wCACjExE,EAAe,QAAA,CAAS,MAAA,GAAS;wCAAE;qCAAA;gCAAA,CACvC;6BAAA;wBAAA,CAEJ;qBAAA;gBAAA,CACF;gBAIDA,EAAe,YAAA,IAAgBA,EAAe,YAAA,CAAa,MAAA,GAAS,KACnEwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BAA4D,UAAA;wBAAA,CAE3E;wBACAuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACZ,UAAA;gCAAAxE,EAAe,YAAA,CAAa,KAAA,CAAM,GAAG,CAAC,EAAE,GAAA,CAAI,CAAC8E,GAAaD,IACzDL,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;wCAEC,WAAU;wCAEV,UAAA;4CAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gDAAI,WAAU;gDAAwC,MAAK;gDAAO,QAAO;gDAAe,SAAQ;gDAC/F,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;gDAAA,CAAyJ;4CAAA,CAChO;4CACC6D;yCAAA;oCAAA,GANID;gCASR7E,EAAe,YAAA,CAAa,MAAA,GAAS,KACpCwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;oCAAK,WAAU;oCAAqD,UAAA;wCAAA;wCACjExE,EAAe,YAAA,CAAa,MAAA,GAAS;wCAAE;qCAAA;gCAAA,CAC3C;6BAAA;wBAAA,CAEJ;qBAAA;gBAAA,CACF;gBAIFiB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;wBAAK,WAAU;wBAA2C,UAAA;oBAAA,CAE3D;gBAAA,CACF;aAAA;QAAA,KAAA,sCAAA;QAlPFuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAAO,EAAAA,QAAAA,EAAA;YAEE,UAAA;gBAAA9D,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;wBACC,OAAO;4BACL,UAAU;4BACV,YAAY;4BACZ,OAAA,CAAO2B,KAAA,OAAA,KAAA,IAAAA,EAAO,WAAA,KAAe;4BAC7B,iBAAA,CAAiBA,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA,MAAS,SAAS,YAAY;4BACtD,SAAS;4BACT,cAAc;wBAAA;wBAEhB,OAAOlB,GAAgB1B,GAAgBuB,EAAuBvB,CAAc,CAAC;wBAE5E,UAAA,EAAuBA,CAAc;oBAAA;gBAAA,CAE1C;gBAEAwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBAEb,UAAA;wBAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAAuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,KAAA;gCAAE,WAAU;gCACV,UAAA;oCAAAb,EAAQ,WAAA;oCAAa;oCACtB1C,aAAAA,GAAAA,EAAAA,GAAAA,CAACf,GAAA;wCACC,MAAMF,EAAe,KAAA;wCACrB,YAAYA,EAAe,WAAA;wCAC3B,WAAWA,EAAe,UAAA;wCAC1B,cAAc;4CACZ,OAAOA,EAAe,KAAA;4CACtB,YAAYA,EAAe,kBAAA;4CAC3B,WAAW;wCAAA;wCAGb,UAAAiB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;4CACC,WAAU;4CAET,UAAA0C,EAAQ,OAAA;wCAAA;oCACX;iCACF;4BAAA,CACF;wBAAA,CACF;wBAGA1C,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAAuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,UAAA;gCACC,SAAS,IAAMxB,EAAc,CAAA,CAAI;gCACjC,WAAU;gCACV,OAAM;gCAEN,UAAA;oCAAA/B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;wCAAK,UAAA;oCAAA,CAAY;oCAClBA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;wCACjE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;wCAAA,CAA4D;oCAAA,CACnI;iCAAA;4BAAA;wBACF,CACF;qBAAA;gBAAA,CACF;aAAA;QAAA,CAGF;IAyLA,KAQNA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;QACC,WAAW4C;QACX,OAAO;YACL,YAAA,CAAYjB,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAc;YACjC,GAAA,CAAGoC,IAAApC,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAoC,EAAmB,WAAA;YACtB,GAAGzE,CAAAA;QAAA;QAEL,qBAAmBqC,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAE1B,UAAA4B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;YACC,WAAU;YACV,OAAOV;YAGP,UAAA;gBAAA7C,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;wBACC,OAAO;4BACL,UAAU;4BACV,YAAY;4BACZ,OAAA,CAAO2B,KAAA,OAAA,KAAA,IAAAA,EAAO,WAAA,KAAe;4BAC7B,iBAAA,CAAiBA,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA,MAAS,SAAS,YAAY;4BACtD,SAAS;4BACT,cAAc;wBAAA;wBAEhB,OAAOlB,GAAgB1B,GAAgBuB,EAAuBvB,CAAc,CAAC;wBAE5E,UAAA,EAAuBA,CAAc;oBAAA;gBAAA,CAE1C;gBAGAwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACZ,UAAA;gCAAAxE,EAAe,YAAA,IACdiB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCACC,KAAKjB,EAAe,YAAA,CAAa,GAAA;oCACjC,KAAK,GAAGA,EAAe,KAAK,CAAA,KAAA,CAAA;oCAC5B,WAAU;oCACV,SAAS,CAAC2E,MAAM;wCAEbA,EAAE,MAAA,CAA4B,KAAA,CAAM,OAAA,GAAU;oCAAA;gCACjD;gCAGJ1D,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;oCAAG,WAAU;oCACX,UAAA,EAAQ,KAAA;gCAAA,CACX;6BAAA;wBAAA,CACF;wBAEAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAACf,GAAA;gCACC,MAAMF,EAAe,KAAA;gCACrB,YAAYA,EAAe,WAAA;gCAC3B,WAAWA,EAAe,UAAA;gCAC1B,cAAc;oCACZ,OAAOA,EAAe,KAAA;oCACtB,YAAYA,EAAe,kBAAA;oCAC3B,WAAW;gCAAA;gCAGb,UAAAwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,UAAA;oCAAO,WAAU;oCAA8O,UAAA;wCAAA;wCACvPb,EAAQ,OAAA;wCACf1C,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4CAAI,WAAU;4CAAe,MAAK;4CAAO,QAAO;4CAAe,SAAQ;4CACtE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;4CAAA,CAA+E;wCAAA,CACtJ;qCAAA;gCAAA,CACF;4BAAA;wBACF,CACF;qBAAA;gBAAA,CACF;gBAGAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,KAAA;wBAAE,WAAU;wBACV,UAAA0C,EAAQ,WAAA;oBAAA,CACX;gBAAA,CACF;gBAGCd,KAAkB,OAAO7C,EAAe,kBAAA,IAAuB,YAC9DwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAA;gCAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;oCAAK,WAAU;oCAAc,UAAA;gCAAA,CAAW;gCACzCuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;oCAAK,WAAU;oCAAyG,UAAA;wCAAAd;wCAAqB;qCAAA;gCAAA,CAAO;6BAAA;wBAAA,CACvJ;wBACAzC,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO,GAAGyC,CAAoB,CAAA,CAAA,CAAA;gCAAA;4BAAI;wBAC7C,CACF;qBAAA;gBAAA,CACF;gBAGFc,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACZ,UAAA;wBAAAxE,EAAe,OAAA,IACdwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;4BAAK,WAAU;4BACd,UAAA;gCAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;oCAAe,MAAK;oCAAO,QAAO;oCAAe,SAAQ;oCACtE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;oCAAA,CAA4I;gCAAA,CACnN;gCACCjB,EAAe,OAAA;6BAAA;wBAAA,CAClB;wBAGDA,EAAe,UAAA,IAAcA,EAAe,UAAA,GAAa,KACxDwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;4BAAK,WAAU;4BACd,UAAA;gCAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;oCAAe,MAAK;oCAAO,QAAO;oCAAe,SAAQ;oCACtE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;oCAAA,CAA4D;gCAAA,CACnI;gCACCjB,EAAe,UAAA;gCAAW;6BAAA;wBAAA,CAC7B;qBAAA;gBAAA,CAEJ;gBAGCA,EAAe,QAAA,IAAYA,EAAe,QAAA,CAAS,MAAA,GAAS,KAC3DwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BAA4D,UAAA;wBAAA,CAE3E;wBACAuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACZ,UAAA;gCAAAxE,EAAe,QAAA,CAAS,KAAA,CAAM,GAAG,CAAC,EAAE,GAAA,CAAI,CAAC4E,GAASC,IACjDL,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;wCAEC,WAAU;wCAEV,UAAA;4CAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gDAAI,WAAU;gDAAwC,MAAK;gDAAO,QAAO;gDAAe,SAAQ;gDAC/F,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;gDAAA,CAAgD;4CAAA,CACvH;4CACC2D;yCAAA;oCAAA,GANIC;gCASR7E,EAAe,QAAA,CAAS,MAAA,GAAS,KAChCwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;oCAAK,WAAU;oCAAqD,UAAA;wCAAA;wCACjExE,EAAe,QAAA,CAAS,MAAA,GAAS;wCAAE;qCAAA;gCAAA,CACvC;6BAAA;wBAAA,CAEJ;qBAAA;gBAAA,CACF;gBAIDA,EAAe,YAAA,IAAgBA,EAAe,YAAA,CAAa,MAAA,GAAS,KACnEwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BAA4D,UAAA;wBAAA,CAE3E;wBACAuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACZ,UAAA;gCAAAxE,EAAe,YAAA,CAAa,KAAA,CAAM,GAAG,CAAC,EAAE,GAAA,CAAI,CAAC8E,GAAaD,IACzDL,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;wCAEC,WAAU;wCAEV,UAAA;4CAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gDAAI,WAAU;gDAAwC,MAAK;gDAAO,QAAO;gDAAe,SAAQ;gDAC/F,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;gDAAA,CAAyJ;4CAAA,CAChO;4CACC6D;yCAAA;oCAAA,GANID;gCASR7E,EAAe,YAAA,CAAa,MAAA,GAAS,KACpCwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;oCAAK,WAAU;oCAAqD,UAAA;wCAAA;wCACjExE,EAAe,YAAA,CAAa,MAAA,GAAS;wCAAE;qCAAA;gCAAA,CAC3C;6BAAA;wBAAA,CAEJ;qBAAA;gBAAA,CACF;gBAAA,aAAA,GAAA,EAAA,GAAA,CAYD,OAAA;oBAAI,WAAU;oBACb,UAAAwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;wBAAI,WAAU;wBACb,UAAA;4BAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;gCAAK,OAAOwC;gCACV,UAAAD;4BAAAA,CACH;4BACAvC,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;gCAAK,WAAU;gCAAmC,UAAA;4BAAA,CAEnD;yBAAA;oBAAA,CACF;gBAAA,CACF;aAAA;QAAA;IACF;AAGN;AAEA0B,GAAkB,WAAA,GAAc;ACtqBzB,MAAMsC,KAAwD,CAAC,EACpE,iBAAAzC,CAAAA,EACA,OAAAI,CAAAA,EACA,aAAAsC,IAAc,CAAA,EACd,iBAAAC,IAAkB,CAAA,CAAA,EAClB,cAAAC,IAAe,CAAA,CAAA,EACf,WAAA9E,CAAAA,EACA,OAAAC,CAAAA,EACF,KAAM;;IAEJa,GAAA;IAGA,MAAMiE,sKAAoB7G,UAAAA,EAAQ,IACzBgE,EAAgB,KAAA,CAAM,GAAG0C,CAAW,GAC1C;QAAC1C;QAAiB0C,CAAW;KAAC,GAI3BI,IAAmBhI,EACvB,oBACA,yBACAgD,IAGIiF,IAAiB3C,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA;IAE3B,OAAIyC,EAAkB,MAAA,KAAW,IAE7BpE,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;QACC,WAAWqE;QACX,OAAO;YACL,GAAGC,CAAAA;YACH,YAAA,CAAY3C,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAc;YACjC,GAAA,CAAGQ,IAAAR,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAQ,EAAmB,YAAA;YACtB,GAAG7C,CAAAA;QAAA;QAEL,qBAAmBqC,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAE1B,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,OAAA;YAAI,WAAU;YACb,UAAA3B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,KAAA;gBAAE,UAAA;YAAA,CAAsB;QAAA,CAC3B;IAAA,KAMJA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;QACC,WAAWqE;QACX,OAAO;YACL,GAAGC,CAAAA;YACH,YAAA,CAAY3C,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAc;YACjC,GAAA,CAAGmB,IAAAnB,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAmB,EAAmB,YAAA;YACtB,GAAGxD,CAAAA;QAAA;QAEL,qBAAmBqC,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAE1B,UAAA4B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;YAAI,WAAU;YAEb,UAAA;gBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAA;gCAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;oCAA2C,MAAK;oCAAO,QAAO;oCAAe,SAAQ;oCAClG,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;oCAAA,CAAuM;gCAAA,CAC9Q;gCACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;oCAAG,WAAU;oCAAyD,UAAA;gCAAA,CAEvE;6BAAA;wBAAA,CACF;wBACAuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,KAAA;4BAAE,WAAU;4BACV,UAAA;gCAAAa,EAAkB,MAAA;gCAAO;6BAAA;wBAAA,CAC5B;qBAAA;gBAAA,CACF;gBAGApE,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACZ,UAAA,EAAkB,GAAA,CAAI,CAACuE,GAASC,IAC/BjB,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAEC,WAAU;4BAGV,UAAA;gCAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oCAAI,WAAU;oCACb,UAAA;wCAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4CAAI,WAAU;4CACZ,UAAA;gDAAAiB,MAAU,KACTxE,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;oDAAK,WAAU;oDAAqE,UAAA;gDAAA,CAErF;gDAEFuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;oDAAK,WAAU;oDAA2C,UAAA;wDAAA;wDACvDiB,IAAQ;qDAAA;gDAAA,CACZ;6CAAA;wCAAA,CACF;wCACCN,KACCX,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4CAAI,WAAU;4CACZ,UAAA;gDAAA,KAAK,KAAA,CAAMgB,EAAQ,kBAAA,GAAqB,GAAG;gDAAE;6CAAA;wCAAA,CAChD;qCAAA;gCAAA,CAEJ;gCAGAvE,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;oCAAG,WAAU;oCACX,UAAA,EAAQ,KAAA;gCAAA,CACX;gCAGCkE,KACCX,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oCAAI,WAAU;oCACb,UAAA;wCAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4CAAI,WAAU;4CACb,UAAA;gDAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;oDAAK,UAAA;gDAAA,CAAW;gDACjBuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;oDAAK,WAAU;oDAAqB,UAAA;wDAAA,KAAK,KAAA,CAAMgB,EAAQ,kBAAA,GAAqB,GAAG;wDAAE;qDAAA;gDAAA,CAAO;6CAAA;wCAAA,CAC3F;wCACAvE,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4CAAI,WAAU;4CACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO,GAAG,KAAK,KAAA,CAAMuE,EAAQ,kBAAA,GAAqB,GAAG,CAAC,CAAA,CAAA,CAAA;gDAAA;4CAAI;wCACrE,CACF;qCAAA;gCAAA,CACF;gCAIFhB,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oCAAI,WAAU;oCACZ,UAAA;wCAAAgB,EAAQ,OAAA,IACPhB,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;4CAAK,WAAU;4CACd,UAAA;gDAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;oDACtE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;oDAAA,CAA4I;gDAAA,CACnN;gDACCuE,EAAQ,OAAA;6CAAA;wCAAA,CACX;wCAKDA,EAAQ,UAAA,IAAcA,EAAQ,UAAA,GAAa,KAC1ChB,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;4CAAK,WAAU;4CACd,UAAA;gDAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;oDACtE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;oDAAA,CAA4D;gDAAA,CACnI;gDACCuE,EAAQ,UAAA;gDAAW;6CAAA;wCAAA,CACtB;qCAAA;gCAAA,CAEJ;gCAGCJ,KAAgBI,EAAQ,QAAA,IAAYA,EAAQ,QAAA,CAAS,MAAA,GAAS,KAC7DhB,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oCAAI,WAAU;oCACb,UAAA;wCAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4CAAI,WAAU;4CAAgD,UAAA;wCAAA,CAE/D;wCACAuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4CAAI,WAAU;4CACZ,UAAA;gDAAAgB,EAAQ,QAAA,CAAS,KAAA,CAAM,GAAG,CAAC,EAAE,GAAA,CAAI,CAACZ,GAASC,IAC1CL,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;wDAEC,WAAU;wDAEV,UAAA;4DAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gEAAI,WAAU;gEAAsC,MAAK;gEAAO,QAAO;gEAAe,SAAQ;gEAC7F,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;gEAAA,CAAgD;4DAAA,CACvH;4DACC2D;yDAAA;oDAAA,GANIC;gDAQR,CACCW,EAAQ,QAAA,CAAS,MAAA,IAAU,CAAA,IAAK,KAChChB,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;oDAAK,WAAU;oDAAkD,UAAA;wDAAA;wDAC9DgB,EAAQ,QAAA,CAAS,MAAA,GAAS;wDAAE;qDAAA;gDAAA,CAChC;6CAAA;wCAAA,CAEJ;qCAAA;gCAAA,CACF;gCAIFvE,aAAAA,GAAAA,EAAAA,GAAAA,CAACf,GAAA;oCACC,MAAMsF,EAAQ,KAAA;oCACd,YAAYA,EAAQ,WAAA;oCACpB,WAAWA,EAAQ,UAAA;oCACnB,cAAc;wCACZ,OAAOA,EAAQ,KAAA;wCACf,YAAYA,EAAQ,kBAAA;wCACpB,WAAW;oCAAA;oCAGb,UAAAhB,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,UAAA;wCAAO,WAAU;wCAA6I,UAAA;4CAAA;4CAE7JvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;gDACjE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;gDAAA,CAA+E;4CAAA,CACtJ;yCAAA;oCAAA,CACF;gCAAA;6BACF;wBAAA,GA9GKuE,EAAQ,UAAA,IAAcC;gBAgH9B,CACH;gBAAA,aAAA,GAAA,EAAA,GAAA,CAGC,OAAA;oBAAI,WAAU;oBACb,UAAAjB,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;wBAAK,WAAU;wBACd,UAAA;4BAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCAAI,WAAU;gCAA0B,MAAK;gCAAe,SAAQ;gCACnE,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;oCAAK,UAAS;oCAAU,GAAE;oCAAmH,UAAS;gCAAA,CAAU;4BAAA,CACnK;4BACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;gCAAK,WAAU;gCAAc,UAAA;4BAAA,CAAU;4BACxCA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;gCAAK,WAAU;gCAAqD,UAAA;4BAAA,CAAM;yBAAA;oBAAA,CAC7E;gBAAA,CACF;aAAA;QAAA,CACF;IAAA;AAGN;AAEAgE,GAAmB,WAAA,GAAc;AClNjC,MAAMS,KAA+C;IACnD,aAAa;IACb,aAAa;IACb,cAAc;IACd,SAAW;IACX,KAAO;IACP,mBAAmB;AACrB,GAGMC,KAAqD;IACzD,aAAa;IACb,aAAa;IACb,cAAc;IACd,SAAW;IACX,KAAO;IACP,mBAAmB;AACrB,GAEaC,KAA0C,CAAC,EACtD,MAAA3M,CAAAA,EACA,SAAA4M,CAAAA,EACA,MAAAC,IAAO,IAAA,EACP,WAAAxF,CAAAA,EACA,OAAAC,CAAAA,EACF,KAAM;IACJ,MAAMwF,IAAmBF,KAAWH,EAAAA,CAAkBzM,CAAI,CAAA,IAAK,aACzD+M,IAAOL,EAAAA,CAAe1M,CAAI,CAAA,EAE1BgN,IAAe3I,EACnB,oBACA,gBACA,CAAA,cAAA,EAAiByI,CAAgB,EAAA,EACjC,CAAA,cAAA,EAAiBD,CAAI,EAAA,EACrBxF;IAGF,OACEkE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;QACC,WAAWyB;QACX,OAAA1F;QAEC,UAAA;YAAAyF,KAAQ/E,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;gBAAK,WAAU;gBAAsB,UAAA+E;YAAAA,CAAK;YACpD/E,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;gBAAK,WAAU;gBAAsB,UAAAhI;YAAA,CAAK;SAAA;IAAA;AAGjD;AAEA2M,GAAY,WAAA,GAAc;ACVnB,MAAMM,KAA4D,CAAC,EACxE,gBAAAlG,CAAAA,EACA,OAAA4C,IAAQ;IAAE,MAAM;AAAA,CAAA,EAChB,WAAAtC,IAAY,EAAA,EACZ,OAAAC,CAAAA,EACA,eAAA4F,IAAgB,CAAA,CAAA,EAChB,iBAAAC,IAAkB,CAAA,CAAA,EAClB,UAAAC,CAAAA,EACA,SAAAC,CAAAA,EACA,aAAAC,IAAc,CAAA,CAAA,EAChB,KAAM;;IAEJnF,GAAA;IAEA,MAAM,CAACU,GAAYkB,CAAa,CAAA,gLAAI5E,EAASgI,CAAe,GAItDI,IAAqB,MAAM;QAC3BD,KACFvD,EAAc,CAAClB,CAAU;IAC3B,GAII2E,IAAqBzG,EAAe,gBAAA,IAAoB,CAAA,CAAA,EAExD0G,IAAkB;QACtB;YACE,OAAO;YACP,aAAa,CAAA,iBAAA,EAAoB1G,EAAe,oBAAA,IAAwBA,EAAe,KAAK,CAAA,gDAAA,CAAA;YAC5F,MAAM;QAAA;QAER;YACE,OAAO;YACP,aAAa,CAAA,0BAAA,EAA6BA,EAAe,oBAAA,IAAwBA,EAAe,KAAK,CAAA,+DAAA,CAAA;YACrG,MAAM;QAAA;QAER;YACE,OAAO,GAAGA,EAAe,oBAAA,IAAwBA,EAAe,KAAK,CAAA,SAAA,CAAA;YACrE,aAAaA,EAAe,0BAAA,IAA8BA,EAAe,WAAA,IAAe,GAAGA,EAAe,oBAAA,IAAwBA,EAAe,KAAK,CAAA,4EAAA,CAAA;YACtJ,MAAM;QAAA;QAER;YACE,OAAO;YACP,aAAa,CAAA,8BAAA,EAAiCA,EAAe,oBAAA,IAAwBA,EAAe,KAAK,CAAA,2BAAA,CAAA;YACzG,MAAM;QAAA;KACR,EAIIwD,IAAmBzB,GAAoB/B,GAAgB,CAAA,CAAK,GAC5DyD,IAAgBxB,GAAA,GAGhB0E,IAAkBN,KAAAA,CAAaI,EAAmB,MAAA,GAAS,IAAIA,IAAqBC,CAAAA,GACpFE,IAAiBN,KAAW,CAAA,IAAA,EAAOtG,EAAe,oBAAA,IAAwBA,EAAe,KAAK,EAAA,EAG9F6G,IAAS;QACb,YAAYjE,EAAM,eAAA,IAAA,CAAoBA,EAAM,IAAA,KAAS,SAAS,YAAY,SAAA;QAC1E,SAASA,EAAM,YAAA,IAAA,CAAiBA,EAAM,IAAA,KAAS,SAAS,YAAY,SAAA;QACpE,QAAQA,EAAM,WAAA,IAAA,CAAgBA,EAAM,IAAA,KAAS,SAAS,YAAY,SAAA;QAClE,MAAMA,EAAM,SAAA,IAAA,CAAcA,EAAM,IAAA,KAAS,SAAS,YAAY,SAAA;QAC9D,eAAeA,EAAM,kBAAA,IAAA,CAAuBA,EAAM,IAAA,KAAS,SAAS,YAAY,SAAA;QAChF,QAAQA,EAAM,WAAA,IAAeA,EAAM,YAAA,IAAgB;QACnD,WAAWA,EAAM,cAAA,IAAkB;QAAA,yEAAA;QAEnC,UAAA,CAAA,CAAUQ,IAAAR,EAAM,SAAA,KAAN,OAAA,KAAA,IAAAQ,EAAiB,OAAA,KAAA,CAAYR,EAAM,IAAA,KAAS,SAAS,YAAY,SAAA;QAC3E,WAAA,CAAA,CAAWmB,IAAAnB,EAAM,SAAA,KAAN,OAAA,KAAA,IAAAmB,EAAiB,SAAA,KAAA,CAAcnB,EAAM,IAAA,KAAS,SAAS,YAAY,SAAA;IAAA,GAI1EkE,IAAelE,EAAM,oBAAA,GAAuB,CAAA,IAAK;QACrD,YAAYA,EAAM,UAAA,IAAc;QAChC,cAAcA,EAAM,YAAA,IAAgB;QACpC,QAAQ,CAAA,UAAA,EAAaiE,EAAO,MAAM,EAAA;QAClC,YAAYA,EAAO,UAAA;QACnB,UAAU;QACV,UAAU;QACV,WAAA,CAAA,CAAW7C,IAAApB,EAAM,OAAA,KAAN,OAAA,KAAA,IAAAoB,EAAe,MAAA,KAAA,CAAWpB,EAAM,IAAA,KAAS,SAChD,yEACA,uEAAA;QACJ,UAAU;QACV,YAAY;IAAA;IAGd,OACE4B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;QACC,WAAW,CAAA,wCAAA,EAA2ClE,CAAS,EAAA;QAC/D,OAAO;YACL,GAAGwG,CAAAA;YACH,GAAA,CAAG7C,IAAArB,EAAM,UAAA,KAAN,OAAA,KAAA,IAAAqB,EAAkB,cAAA;YACrB,GAAG1D,CAAAA;QAAA;QAEL,qBAAmBqC,EAAM,IAAA;QAGzB,UAAA;YAAA3B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBACC,OAAO;oBACL,YAAY4F,EAAO,QAAA;oBACnB,SAAS;oBACT,cAAc/E,KAAc,CAACyE,IAAc,CAAA,UAAA,EAAaM,EAAO,MAAM,EAAA,GAAK;oBAC1E,UAAU;oBACV,YAAY;gBAAA;gBAGd,UAAArC,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,OAAO;wBAAE,SAAS;wBAAQ,YAAY;wBAAU,gBAAgB;wBAAiB,KAAK;oBAAA;oBACzF,UAAA;wBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,YAAY;gCAAU,KAAK;gCAAQ,MAAM;gCAAG,UAAU;4BAAA;4BACnF,UAAA;gCAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCACC,OAAO;wCACL,OAAO;wCACP,QAAQ;wCACR,cAAc2B,EAAM,YAAA,IAAgB;wCACpC,YAAYiE,EAAO,MAAA;wCACnB,SAAS;wCACT,YAAY;wCACZ,gBAAgB;wCAChB,OAAO;wCACP,UAAA,CAAA,CAAU3C,IAAAtB,EAAM,QAAA,KAAN,OAAA,KAAA,IAAAsB,EAAgB,IAAA,KAAQ;wCAClC,YAAY;wCACZ,WAAA,CAAA,CAAWC,IAAAvB,EAAM,OAAA,KAAN,OAAA,KAAA,IAAAuB,EAAe,KAAA,KAAS;wCACnC,QAAQ,CAAA,UAAA,EAAa0C,EAAO,MAAM,EAAA;oCAAA;oCAGlC,UAAA,CAAA,EAAe,oBAAA,IAAwB7G,EAAe,KAAA,EAAO,MAAA,CAAO,CAAC,EAAE,WAAA,CAAA;gCAAY;gCAEvFwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oCAAI,OAAO;wCAAE,MAAM;wCAAG,UAAU;oCAAA;oCAC/B,UAAA;wCAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;4CACC,OAAO;gDACL,QAAQ;gDACR,UAAU;gDACV,YAAY;gDACZ,OAAO4F,EAAO,IAAA;gDACd,YAAY;gDACZ,UAAU;gDACV,cAAc;gDACd,YAAY;4CAAA;4CAGb,UAAA7G,EAAe,oBAAA,IAAwBA,EAAe,KAAA;wCAAA;wCAEzDwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,KAAA;4CACC,OAAO;gDACL,QAAQ;gDACR,UAAU;gDACV,OAAOqC,EAAO,aAAA;gDACd,YAAY;gDACZ,UAAU;gDACV,cAAc;gDACd,YAAY;4CAAA;4CAEd,OAAOpD;4CAEN,UAAA;gDAAAD;gDAAiB;gDAAI,IAAI,IAAIxD,EAAe,GAAA,IAAOA,EAAe,WAAW,EAAE,QAAA;6CAAA;wCAAA;qCAClF;gCAAA,CACF;6BAAA;wBAAA,CACF;wBAEAwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,YAAY;gCAAU,KAAK;4BAAA;4BAEvD,UAAA;gCAAA,CAAC1C,KAAcyE,KACdtF,aAAAA,GAAAA,EAAAA,GAAAA,CAACf,GAAA;oCACC,MAAMF,EAAe,KAAA;oCACrB,YAAYA,EAAe,WAAA;oCAC3B,WAAWA,EAAe,UAAA;oCAC1B,cAAc;wCACZ,OAAOA,EAAe,oBAAA,IAAwBA,EAAe,KAAA;wCAC7D,WAAW;wCACX,UAAU,CAAA;wCACV,UAAU;oCAAA;oCAGZ,UAAAiB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;wCACC,OAAO;4CACL,SAAA,CAASmD,IAAAxB,EAAM,OAAA,KAAN,QAAAwB,EAAe,KAAA,GAAQ,GAAGxB,EAAM,OAAA,CAAQ,KAAK,CAAA,CAAA,EAAIA,EAAM,OAAA,CAAQ,MAAA,IAAU,MAAM,EAAA,GAAK;4CAC7F,iBAAiBiE,EAAO,MAAA;4CACxB,OAAO;4CACP,QAAQ;4CACR,cAAcjE,EAAM,YAAA,IAAgB;4CACpC,UAAA,CAAA,CAAUyB,IAAAzB,EAAM,QAAA,KAAN,OAAA,KAAA,IAAAyB,EAAgB,KAAA,KAAS;4CACnC,YAAY;4CACZ,QAAQ;4CACR,YAAY;4CACZ,WAAA,CAAA,CAAWC,IAAA1B,EAAM,OAAA,KAAN,OAAA,KAAA,IAAA0B,EAAe,KAAA,KAAS;4CACnC,YAAY;4CACZ,GAAA,CAAGC,IAAA3B,EAAM,UAAA,KAAN,OAAA,KAAA,IAAA2B,EAAkB,MAAA;wCAAA;wCAEvB,aAAa,CAACI,MAAM;;4CACb/B,EAAM,oBAAA,IAAA,CACT+B,EAAE,aAAA,CAAc,KAAA,CAAM,SAAA,GAAY,oBAClCA,EAAE,aAAA,CAAc,KAAA,CAAM,SAAA,GAAA,CAAA,CAAYvB,IAAAR,EAAM,OAAA,KAAN,OAAA,KAAA,IAAAQ,EAAe,MAAA,KAAU,+BAAA;wCAC7D;wCAEF,YAAY,CAACuB,MAAM;;4CACZ/B,EAAM,oBAAA,IAAA,CACT+B,EAAE,aAAA,CAAc,KAAA,CAAM,SAAA,GAAY,iBAClCA,EAAE,aAAA,CAAc,KAAA,CAAM,SAAA,GAAA,CAAA,CAAYvB,IAAAR,EAAM,OAAA,KAAN,OAAA,KAAA,IAAAQ,EAAe,KAAA,KAAS,8BAAA;wCAC5D;wCAGD,UAAAwD;oCAAA;gCACH;gCAKHL,KACC/B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,UAAA;oCACC,SAASgC;oCACT,OAAO;wCACL,SAAS;wCACT,YAAY;wCACZ,KAAK;wCACL,SAAS;wCACT,YAAY5D,EAAM,IAAA,KAAS,SAAS,YAAY;wCAChD,QAAQ,CAAA,UAAA,EAAaA,EAAM,IAAA,KAAS,SAAS,YAAY,SAAS,EAAA;wCAClE,cAAc;wCACd,QAAQ;wCACR,OAAOA,EAAM,WAAA,IAAe;wCAC5B,UAAU;wCACV,YAAY;wCACZ,YAAY;oCAAA;oCAEd,cAAc,CAAC+B,MAAM;wCACnBA,EAAE,aAAA,CAAc,KAAA,CAAM,UAAA,GAAa/B,EAAM,IAAA,KAAS,SAAS,YAAY,WACvE+B,EAAE,aAAA,CAAc,KAAA,CAAM,WAAA,GAAc/B,EAAM,WAAA,IAAe;oCAAA;oCAE3D,cAAc,CAAC+B,MAAM;wCACnBA,EAAE,aAAA,CAAc,KAAA,CAAM,UAAA,GAAa/B,EAAM,IAAA,KAAS,SAAS,YAAY,WACvE+B,EAAE,aAAA,CAAc,KAAA,CAAM,WAAA,GAAc/B,EAAM,IAAA,KAAS,SAAS,YAAY;oCAAA;oCAE1E,cAAYd,IAAa,sBAAsB;oCAE/C,UAAA;wCAAAb,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;4CAAM,UAAAa,IAAa,iBAAiB;wCAAA,CAAe;wCACpDb,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4CACC,OAAM;4CACN,QAAO;4CACP,SAAQ;4CACR,MAAK;4CACL,QAAO;4CACP,aAAY;4CACZ,eAAc;4CACd,gBAAe;4CAEd,UAAAa,IAAA,0BAAA;4CAECb,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;gDAAK,GAAE;4CAAA,CAAW,IAAA,uBAAA;4CAGnBuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAAO,EAAAA,QAAAA,EAAA;gDACE,UAAA;oDAAA9D,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;wDAAO,IAAG;wDAAK,IAAG;wDAAK,GAAE;oDAAA,CAAK;oDAC/BA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;wDAAK,GAAE;oDAAA,CAAY;oDACpBA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;wDAAK,GAAE;oDAAA,CAAY;iDAAA;4CAAA,CACtB;wCAAA;qCAEJ;gCAAA;6BACF;wBAAA,CAEJ;qBAAA;gBAAA,CACF;YAAA;YAAA,CAIAa,KAAc,CAACyE,CAAAA,KACf/B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gBAAI,OAAO;oBAAE,SAAS;gBAAA;gBAEpB,UAAA;oBAAAmC,EAAgB,GAAA,CAAI,CAACI,GAAStB,IAC7BjB,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAEC,OAAO;gCACL,SAAS;gCACT,iBAAiBiB,IAAQ,MAAM,IAAIoB,EAAO,UAAA,GAAaA,EAAO,SAAA;gCAC9D,cAAcpB,IAAQkB,EAAgB,MAAA,GAAS,IAAI,CAAA,UAAA,EAAaE,EAAO,MAAM,EAAA,GAAK;4BAAA;4BAGpF,UAAA;gCAAArC,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,MAAA;oCACC,OAAO;wCACL,QAAQ;wCACR,UAAU;wCACV,YAAY;wCACZ,OAAOqC,EAAO,IAAA;wCACd,SAAS;wCACT,YAAY;wCACZ,KAAK;oCAAA;oCAGN,UAAA;wCAAAE,EAAQ,IAAA,IAAQ9F,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;4CAAM,UAAA8F,EAAQ,IAAA;wCAAA,CAAK;wCACpCA,EAAQ,KAAA;qCAAA;gCAAA;gCAEX9F,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,KAAA;oCACC,OAAO;wCACL,QAAQ;wCACR,UAAU;wCACV,OAAO4F,EAAO,aAAA;wCACd,YAAY;oCAAA;oCAGb,UAAAE,EAAQ,WAAA;gCAAA;6BACX;wBAAA,GA9BKtB;oBAgCR,CAGC3D,KAAc,CAACyE,CAAAA,KACftF,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,WAAW,CAAA,UAAA,EAAa4F,EAAO,MAAM,EAAA;4BAAI,iBAAiBA,EAAO,UAAA;wBAAA;wBAC9F,UAAA5F,aAAAA,GAAAA,EAAAA,GAAAA,CAACf,GAAA;4BACC,MAAMF,EAAe,KAAA;4BACrB,YAAYA,EAAe,WAAA;4BAC3B,WAAWA,EAAe,UAAA;4BAC1B,cAAc;gCACZ,OAAOA,EAAe,KAAA;gCACtB,WAAW;gCACX,UAAU8B;gCACV,UAAU;4BAAA;4BAGZ,UAAAb,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;gCACC,OAAO;oCACL,OAAO;oCACP,SAAS;oCACT,YAAY4F,EAAO,MAAA;oCACnB,OAAO;oCACP,QAAQ;oCACR,cAAc;oCACd,UAAU;oCACV,YAAY;oCACZ,QAAQ;oCACR,YAAY;oCACZ,WAAW;oCACX,UAAU;oCACV,UAAU;gCAAA;gCAEZ,aAAa,CAAClC,MAAM;oCAClBA,EAAE,aAAA,CAAc,KAAA,CAAM,SAAA,GAAY,gCAClCA,EAAE,aAAA,CAAc,KAAA,CAAM,SAAA,GAAY;gCAAA;gCAEpC,YAAY,CAACA,MAAM;oCACjBA,EAAE,aAAA,CAAc,KAAA,CAAM,SAAA,GAAY,0BAClCA,EAAE,aAAA,CAAc,KAAA,CAAM,SAAA,GAAY;gCAAA;gCAGnC,UAAAiC;4BAAA;wBACH;oBAAA,CAEJ;oBAIDT,KACClF,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBACC,OAAO;4BACL,SAAS;4BACT,WAAW,CAAA,UAAA,EAAa4F,EAAO,MAAM,EAAA;4BACrC,iBAAiBA,EAAO,QAAA;wBAAA;wBAG1B,UAAArC,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BACC,OAAO;gCACL,UAAU;gCACV,OAAOqC,EAAO,aAAA;gCACd,WAAW;4BAAA;4BAEd,UAAA;gCAAA;gCACY5F,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;oCAAO,OAAO;wCAAE,OAAO4F,EAAO,IAAA;oCAAA;oCAAQ,UAAA;gCAAA,CAAM;6BAAA;wBAAA;oBAC1D;iBACF;YAAA,CAEJ;SAAA;IAAA;AAIR,GCtZaG,IAAwE,CAAC,EACpF,gBAAAhH,CAAAA,EACA,OAAA4C,CAAAA,EACA,SAAAZ,IAAU,CAAA,CAAA,EACV,YAAAiF,IAAa,CAAA,CAAA,EACb,WAAA3G,CAAAA,EACA,OAAAC,CAAAA,EACF,KAAM;;IACJ,MAAMmD,IAAuB,KAAK,KAAA,CAAM1D,EAAe,kBAAA,GAAqB,GAAG,GAGzEwD,IAAmBzB,GAAoB/B,GAAgBgC,CAAO,GAC9DyB,IAAgBxB,GAAA,GAEhBqD,IAAmBhI,EACvB,gCACA,oDACA;QACE,gJAAgJ,CAAC0E;QACjJ,oEAAoEA;IAAA,GAEtE1B,IAGIiF,IAAiB3C,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA;IAE3B,OACE3B,aAAAA,GAAAA,EAAAA,GAAAA,CAACf,GAAA;QACC,MAAMF,EAAe,KAAA;QACrB,YAAYA,EAAe,WAAA;QAC3B,WAAWA,EAAe,UAAA;QAC1B,cAAc;YACZ,OAAOA,EAAe,KAAA;YACtB,YAAYA,EAAe,kBAAA;QAAA;QAE7B,WAAWsF;QACX,OAAO;YACL,YAAA,CAAY1C,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAc;YACjC,GAAA,CAAGQ,IAAAR,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAQ,EAAmB,oBAAA;YACtB,GAAG7C,CAAAA;QAAA;QAGL,UAAAiE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;YACC,WAAU;YACV,OAAOe;YACP,qBAAmB3C,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;YAG1B,UAAA;gBAAA3B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACZ,UAAAjB,EAAe,YAAA,IAAgBA,EAAe,YAAA,CAAa,MAAA,GAAS,IACnEiB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAU;wBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BACC,KAAKjB,EAAe,YAAA,CAAa,CAAC,CAAA,CAAE,GAAA;4BACpC,KAAKA,EAAe,oBAAA,IAAwBA,EAAe,KAAA;4BAC3D,WAAU;wBAAA;oBAAA,CAEd,IACEA,EAAe,YAAA,GACjBiB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAU;wBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BACC,KAAKjB,EAAe,YAAA,CAAa,GAAA;4BACjC,KAAKA,EAAe,oBAAA,IAAwBA,EAAe,KAAA;4BAC3D,WAAU;wBAAA;oBACZ,CACF,IACEA,EAAe,kBAAA,IAAsB,MACvCiB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAU;oBAAA,CAAoC,IAEnDA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAU;oBAAA,CAAmC;gBAAA,CAEtD;gBAGAuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAA;gCAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;oCAAG,WAAW3D,EACb,4DACA,iFACA,kCACA0E,IAAU,yBAAyB;oCAElC,UAAAhC,EAAe,oBAAA,IAAwBA,EAAe,KAAA;gCAAA,CACzD;gCAGCA,EAAe,kBAAA,IAAsB,OACpCwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;oCAAK,WAAWlH,EACf,2GACA0C,EAAe,kBAAA,IAAsB,MACjC,yEACA;oCAEH,UAAA;wCAAA0D;wCAAqB;qCAAA;gCAAA,CACxB;6BAAA;wBAAA,CAEJ;wBAGCuD,KAAAA,CAAejH,EAAe,0BAAA,IAA8BA,EAAe,MAAA,KAC1EiB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,KAAA;4BAAE,WAAW3D,EACZ,iDACA0E,IAAU,YAAY;4BAErB,UAAAhC,EAAe,0BAAA,IAA8BA,EAAe,MAAA;wBAAA,CAC/D;wBAIFiB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,KAAA;4BACC,WAAW3D,EACT,yCACU;4BAEZ,OAAOmG;4BAEN,UAAAD;wBAAA;wBAIF,CAACxB,KAAWhC,EAAe,QAAA,IAAYA,EAAe,QAAA,CAAS,MAAA,GAAS,KACvEwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACZ,UAAA;gCAAAxE,EAAe,QAAA,CAAS,KAAA,CAAM,GAAG,CAAC,EAAE,GAAA,CAAI,CAACqD,GAASoC,IACjDxE,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;wCAEC,WAAU;wCAET,UAAAoC;oCAAA,GAHIoC;gCAMRzF,EAAe,QAAA,CAAS,MAAA,GAAS,KAChCwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;oCAAK,WAAU;oCAA2C,UAAA;wCAAA;wCACvDxE,EAAe,QAAA,CAAS,MAAA,GAAS;wCAAE;qCAAA;gCAAA,CACvC;6BAAA;wBAAA,CAEJ;wBAID,CAACgC,KAAWhC,EAAe,UAAA,IAAcA,EAAe,UAAA,GAAa,KACpEiB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAAuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;gCAAK,WAAU;gCACb,UAAA;oCAAAxE,EAAe,UAAA;oCAAW;iCAAA;4BAAA,CAC7B;wBAAA,CACF;qBAAA;gBAAA,CAEJ;gBAGAiB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,SAAQ;wBAER,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;4BACC,eAAc;4BACd,gBAAe;4BACf,aAAa;4BACb,GAAE;wBAAA;oBACJ;gBACF,CACF;aAAA;QAAA;IACF;AAGN,GC5KaiG,KAAsE,CAAC,EAClF,iBAAA1E,CAAAA,EACA,qBAAA2E,CAAAA,EACA,OAAAvE,CAAAA,EACA,wBAAAwE,IAAyB,CAAA,EACzB,uBAAAC,CAAAA,EACA,wBAAAC,CAAAA,EACA,WAAAhH,CAAAA,EACF,KAAM;IACJ,MAAMiH,IAAqB/E,EACxB,IAAA,CAAK,CAACgF,GAAGC,IAAMA,EAAE,kBAAA,GAAqBD,EAAE,kBAAkB,EAC1D,KAAA,CAAM,GAAGJ,CAAsB,GAE5B9B,IAAmBhI,EACvB,+BACA,0BACA,qEACA,aAAA,0BAAA;IACAgD,IAGIiF,IAAiB3C,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA;IAE3B,OACE4B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;QACC,WAAWc;QACX,OAAOC;QACP,qBAAmB3C,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAG1B,UAAA;YAAA4B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAA;oBAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAU;wBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCAAI,WAAU;gCAAmD,MAAK;gCAAO,QAAO;gCAAe,SAAQ;gCAC1G,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;gCAAA,CAAgD;4BAAA,CACvH;wBAAA,CACF;oBAAA,CACF;oBACAuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;wBAAI,WAAU;wBACb,UAAA;4BAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;gCAAG,WAAU;gCAAgE,UAAA;4BAAA,CAE9E;4BACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,KAAA;gCAAE,WAAU;gCAAsD,UAAA;4BAAA,CAEnE;yBAAA;oBAAA,CACF;iBAAA;YAAA,CACF;YAGAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,KAAA;wBAAE,WAAU;wBACV,UAAAkG;oBAAA,CACH;gBAAA,CACF;YAAA,CACF;YAGCI,EAAmB,MAAA,GAAS,KAC3B/C,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAA;oBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;wBAAI,WAAU;wBACb,UAAA;4BAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCAAI,WAAU;gCAAqC,MAAK;gCAAO,QAAO;gCAAe,SAAQ;gCAC5F,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;gCAAA,CAA6B;4BAAA,CACpG;4BACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;gCAAG,WAAU;gCAAyC,UAAA;4BAAA,CAEvD;yBAAA;oBAAA,CACF;oBAEAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAU;wBACZ,UAAAsG,EAAmB,GAAA,CAAI,CAACvH,GAAgByF,IACvCjB,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gCAAwC,WAAU;gCAEjD,UAAA;oCAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wCAAI,WAAU;wCACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4CAAI,WAAW3D,EACd,2EACAmI,MAAU,IAAI,sDACdA,MAAU,IAAI,4DACd;4CAEC,UAAAA,IAAQ;wCAAA,CACX;oCAAA,CACF;oCAEAxE,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wCAAI,WAAU;wCACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC+F,GAAA;4CACC,gBAAAhH;4CACA,OAAA4C;4CACA,SAAS,CAAA;4CACT,YAAY,CAAA;4CACZ,SAASyE;wCAAA;oCACX,CACF;iCAAA;4BAAA,GArBQrH,EAAe,KAAA,IAASyF,CAsBlC,CACD;oBAAA,CACH;iBAAA;YAAA,CACF;YAIDjD,EAAgB,MAAA,GAAS4E,KACxBnG,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAAuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;wBAAI,WAAU;wBACb,UAAA;4BAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCAAI,WAAU;gCAAqC,MAAK;gCAAO,QAAO;gCAAe,SAAQ;gCAC5F,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;gCAAA,CAA4D;4BAAA,CACnI;4BACAuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;gCAAK,WAAU;gCACb,UAAA;oCAAAhC,EAAgB,MAAA,GAAS4E;oCAAuB;oCAA2B5E,EAAgB,MAAA,GAAS4E,IAAyB,IAAI,MAAM;oCAAG;iCAAA;4BAAA,CAC7I;yBAAA;oBAAA,CACF;gBAAA,CACF;YAAA,CACF;YAIF5C,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gBAAI,WAAU;gBACZ,UAAA;oBAAA8C,KACC9C,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,UAAA;wBACC,SAAS8C;wBACT,WAAU;wBAEV,UAAA;4BAAArG,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;gCACjE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;gCAAA,CAAgK;4BAAA,CACvO;4BAAM;yBAAA;oBAAA;oBAKVuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,UAAA;wBACC,SAAS,MAAM;4BACT+C,EAAmB,MAAA,GAAS,KAAA,CAC9BF,KAAA,QAAAA,EAAwBE,CAAAA,CAAmB,CAAC,CAAA,CAAE,KAAA,EAAOA,CAAAA,CAAmB,CAAC,CAAA,CAAE,WAAA,CAAA;wBAC7E;wBAEF,WAAU;wBAEV,UAAA;4BAAAtG,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;gCACjE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;gCAAA,CAA+E;4BAAA,CACtJ;4BAAM;yBAAA;oBAAA;iBAER;YAAA,CACF;YAGAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;oBAAK,WAAU;oBAA2C,UAAA;gBAAA,CAE3D;YAAA,CACF;SAAA;IAAA;AAGN,GC1JayG,KAAkE,CAAC,EAC9E,gBAAA1H,CAAAA,EACA,gBAAA2H,CAAAA,EACA,eAAAC,IAAgB,UAAA,EAChB,OAAAhF,CAAAA,EACA,aAAAiF,IAAc,CAAA,CAAA,EACd,SAAAC,CAAAA,EACA,WAAAxH,CAAAA,EACA,OAAAC,CAAAA,EACF,KAAM;IACJ,MAAM,CAACwH,GAAWC,CAAY,CAAA,IAAI5J,4KAAAA,EAAS,CAAA,CAAK,GAE1C6J,IAAmB,MAAM;QAC7BD,EAAa,CAAA,CAAI,GACjBF,KAAA,QAAAA,EAAU9H;IAAc,GAGpBkI,IAAmB,MAAM;QAC7BF,EAAa,CAAA,CAAK;IAAA,GAMdG,IAAqB,MAAM;QAC/B,OAAQP,GAAA;YACN,KAAK;gBACH,OAAO,CAAA,CAAA,EAAID,CAAc,CAAA,CAAA,CAAA;YAC3B,KAAK;gBACH,OAAOA,EAAe,QAAA,CAAA;YACxB,KAAK;YACL;gBACE,OAAOA,EAAe,QAAA,CAAA;QAAS;IACnC,GAGIS,IAAkB9K,EACtB,6BACA,2CACA,8CACA,iFACA,eACA;QAAA,2BAAA;QAEE,wJAAwJsK,MAAkB;QAAA,kBAAA;QAG1K,gCAAgCA,MAAkB;QAAA,oBAAA;QAGlD,uCAAuCA,MAAkB;IAAA,GAE3DtH,IAGIiF,IAAiB3C,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA;IAE3B,OACE4B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;QAAK,WAAU;QACd,UAAA;YAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAACf,GAAA;gBACC,MAAMF,EAAe,KAAA;gBACrB,YAAYA,EAAe,WAAA;gBAC3B,WAAWA,EAAe,UAAA;gBAC1B,cAAc;oBACZ,OAAOA,EAAe,KAAA;oBACtB,YAAYA,EAAe,kBAAA;oBAC3B,gBAAA2H;oBACA,eAAAC;gBAAA;gBAEF,WAAWQ;gBACX,OAAA7H;gBAEA,UAAAU,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;oBACC,OAAOsE;oBACP,qBAAmB3C,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;oBAC1B,cAAcqF;oBACd,cAAcC;oBAEb,UAAAC,EAAA;gBAAmB;YACtB;YAIDN,KAAeE,KACd9G,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAAuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BAAsB,UAAAjB,EAAe,KAAA;wBAAA,CAAM;wBACzDA,EAAe,MAAA,IACdiB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BACZ,UAAA,EAAe,MAAA,CAAO,MAAA,GAAS,MAC5B,GAAGjB,EAAe,MAAA,CAAO,SAAA,CAAU,GAAG,GAAG,CAAC,CAAA,GAAA,CAAA,GAC1CA,EAAe,MAAA;wBAAA,CAErB;wBAEDA,EAAe,kBAAA,IAAsB,OACpCwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACZ,UAAA;gCAAA,KAAK,KAAA,CAAMxE,EAAe,kBAAA,GAAqB,GAAG;gCAAE;6BAAA;wBAAA,CACvD;wBAEFiB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BAAuD,UAAA;wBAAA,CAEtE;wBAEAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;wBAAA,CAA6J;qBAAA;gBAAA,CAC9K;YAAA,CACF;SAAA;IAAA,CAEJ;AAEJ,GC/GaoH,KAAwD,CAAC,EACpE,iBAAA7F,CAAAA,EACA,kBAAA8F,CAAAA,EACA,OAAA1F,CAAAA,EACA,kBAAA2F,IAAmB,CAAA,CAAA,EACnB,eAAAX,IAAgB,UAAA,EAChB,iBAAAY,CAAAA,EACA,WAAAlI,CAAAA,EACA,OAAAC,CAAAA,EACF,KAAM;;IACJ,MAAM,CAACkI,GAAuBC,CAAwB,CAAA,gLAAItK,EAAsC,IAAI,GAG9FuK,sKAAmBnK,UAAAA,EAAQ,MAAM;QACrC,IAAI,CAAC8J,KAAoB9F,EAAgB,MAAA,KAAW,GAClD,OAAO;YAAE,MAAM8F;YAAkB,aAAa,aAAA,GAAA,IAAI;QAAI;QAGxD,IAAIM,IAAgBN;QACpB,MAAMO,IAAAA,aAAAA,GAAAA,IAAkB,IAAA;QAOxB,OAJ8B,CAAC;eAAGrG,CAAe;SAAA,CAC9C,IAAA,CAAK,CAACgF,GAAGC,IAAMA,EAAE,kBAAA,GAAqBD,EAAE,kBAAkB,EAGvC,OAAA,CAAQ,CAACxH,GAAgByF,MAAU;YACvD,MAAMkC,IAAiBlC,IAAQ,GACzBqD,IAAQ9I,EAAe,KAAA;YAG7B6I,EAAY,GAAA,CAAIlB,GAAgB3H,CAAc;YAG9C,MAAM+I,IAAa,IAAI,OAAO,CAAA,GAAA,EAAMD,EAAM,OAAA,CAAQ,uBAAuB,MAAM,CAAC,CAAA,GAAA,CAAA,EAAO,IAAI;YAG3F,IAAIC,EAAW,IAAA,CAAKH,CAAa,GAC/BA,IAAgBA,EAAc,OAAA,CAAQG,GAAY,CAACC,IAC1C,GAAGA,CAAK,CAAA,WAAA,EAAcrB,CAAc,CAAA,EAAA,CAC5C;iBACI;gBAGL,MAAMsB,IAAWjJ,EAAe,QAAA,IAAY,CAAA,CAAA;gBAC5C,IAAIkJ,IAAW,CAAA;gBAEf,KAAA,MAAW7F,KAAW4F,EAAU;oBAC9B,MAAME,IAAe,IAAI,OAAO,CAAA,GAAA,EAAM9F,EAAQ,OAAA,CAAQ,uBAAuB,MAAM,CAAC,CAAA,GAAA,CAAA,EAAO,IAAI;oBAC/F,IAAI8F,EAAa,IAAA,CAAKP,CAAa,KAAK,CAACM,GAAU;wBACjDN,IAAgBA,EAAc,OAAA,CAAQO,GAAc,CAACH,IAAAA,CACnDE,IAAW,CAAA,GACJ,GAAGF,CAAK,CAAA,WAAA,EAAcrB,CAAc,CAAA,EAAA,CAAA,CAC5C;wBACD;oBAAA;gBACF;gBAIGuB,KAAAA,CACHN,KAAiB,CAAA,WAAA,EAAcjB,CAAc,CAAA,EAAA,CAAA;YAC/C;QACF,CACD,GAEM;YAAE,MAAMiB;YAAe,aAAAC;QAAA;IAAY,GACzC;QAACP;QAAkB9F,CAAe;KAAC,GAGhC4G,IAA0B,MAAM;QACpC,MAAM,EAAE,MAAAC,CAAAA,EAAM,aAAAR,CAAAA,CAAA,CAAA,GAAgBF;QAG9B,OAFcU,EAAK,KAAA,CAAM,wBAAwB,EAEpC,GAAA,CAAI,CAACC,GAAM7D,MAAU;YAChC,MAAM8D,IAAgBD,EAAK,KAAA,CAAM,wBAAwB;YAEzD,IAAIC,GAAe;gBACjB,MAAM5B,IAAiB,SAAS4B,CAAAA,CAAc,CAAC,CAAC,GAC1CvJ,IAAiB6I,EAAY,GAAA,CAAIlB,CAAc;gBAErD,IAAI3H,GACF,OACEiB,aAAAA,GAAAA,EAAAA,GAAAA,CAACyG,IAAA;oBAEC,gBAAA1H;oBACA,gBAAA2H;oBACA,eAAAC;oBACA,OAAAhF;oBACA,aAAa,CAAA;oBACb,SAAS,CAACH,MAAQ;wBAChBiG,EAAyBjG,CAAG,GAC5B+F,KAAA,QAAAA,EAAkB/F;oBAAG;gBACvB,GATK,CAAA,SAAA,EAAYkF,CAAc,CAAA,CAAA,EAAIlC,CAAK,EAAA;YAY9C;YAGF,OAAOxE,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;gBAAkB,UAAAqI;YAAA,GAAR7D,CAAa;QAAA,CAChC;IAAA,GAGGH,IAAmBhI,EACvB,wBACA,aACAgD,IAGIiF,IAAiB3C,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA;IAE3B,OACE4B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;QACC,WAAWc;QACX,OAAO;YACL,GAAGC,CAAAA;YACH,YAAA,CAAY3C,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAc;YACjC,GAAA,CAAGQ,IAAAR,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAQ,EAAmB,YAAA;YACtB,GAAG7C,CAAAA;QAAA;QAEL,qBAAmBqC,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAG1B,UAAA;YAAA3B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAU;gBACZ,UAAAmI,EAAA;YAAA,CACH;YAGCb,KAAoB/F,EAAgB,MAAA,GAAS,KAC5CvB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAAuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,MAAA;4BAAG,WAAU;4BACZ,UAAA;gCAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;oCACjE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;oCAAA,CAAqI;gCAAA,CAC5M;gCAAM;6BAAA;wBAAA,CAER;wBAAA,aAAA,GAAA,EAAA,GAAA,CAEC,OAAA;4BAAI,WAAU;4BACZ,UAAAuB,EACE,IAAA,CAAK,CAACgF,GAAGC,IAAMA,EAAE,kBAAA,GAAqBD,EAAE,kBAAkB,EAC1D,GAAA,CAAI,CAACxH,GAAgByF,IACpBjB,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oCAEC,WAAWlH,EACT,wEACA;wCACE,8EAAA,CACEmL,KAAA,OAAA,KAAA,IAAAA,EAAuB,KAAA,MAAUzI,EAAe,KAAA;wCAClD,+CAAA,CACEyI,KAAA,OAAA,KAAA,IAAAA,EAAuB,KAAA,MAAUzI,EAAe,KAAA;oCAAA;oCAKtD,UAAA;wCAAAiB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4CAAI,WAAU;4CACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAACyG,IAAA;gDACC,gBAAA1H;gDACA,gBAAgByF,IAAQ;gDACxB,eAAAmC;gDACA,OAAAhF;gDACA,aAAa,CAAA;4CAAA;wCAAA,CAEjB;wCAGA3B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4CAAI,WAAU;4CACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC+F,GAAA;gDACC,gBAAAhH;gDACA,OAAA4C;gDACA,SAAS,CAAA;gDACT,YAAY,CAAA;4CAAA;wCACd,CACF;qCAAA;gCAAA,GA9BK5C,EAAe,KAAA,IAASyF;wBAgChC,CACL;qBAAA;gBAAA,CACF;YAAA,CACF;SAAA;IAAA;AAIR,GCrLa+D,KAAoE,CAAC,EAChF,iBAAAhH,CAAAA,EACA,QAAAtJ,CAAAA,EACA,OAAA0J,CAAAA,EACA,qBAAAuE,CAAAA,EACA,WAAAsC,CAAAA,EACA,uBAAApC,CAAAA,EACA,WAAAqC,CAAAA,EACA,WAAApJ,CAAAA,EACF,KAAM;IACJ,MAAM,CAACqJ,GAAWC,CAAY,CAAA,qKAAIxL,WAAAA,EAASlF,EAAO,QAAA,KAAa,CAAA,CAAK,GAC9D,CAAC2Q,GAAaC,CAAc,CAAA,oKAAI1L,YAAAA,EAAS,CAAA,CAAK;IAcpD,kLAZAuC,EAAU,MAAM;QACd,IAAIzH,EAAO,OAAA,IAAWA,EAAO,OAAA,GAAU,GAAG;YACxC,MAAM6Q,IAAQ,WAAW,MAAM;gBAC7BH,EAAa,CAAA,CAAI,GACjBE,EAAe,CAAA,CAAI;YAAA,GAClB5Q,EAAO,OAAO;YACjB,OAAO,IAAM,aAAa6Q,CAAK;QAAA,OAE/BD,EAAe,CAAA,CAAI;IACrB,GACC;QAAC5Q,EAAO,OAAO;KAAC,GAEf,CAACyQ,KAAanH,EAAgB,MAAA,KAAW,GAC3C,OAAO;IAGT,MAAMwH,IAAqB9Q,EAAO,kBAAA,IAAsB,GAClD+Q,IAAyBzH,EAAgB,KAAA,CAAM,GAAGwH,CAAkB,GAEpEE,IAA4B,CAACtK,GAAcO,MAAuB;QACtEkH,KAAA,QAAAA,EAAwBzH,GAAMO;IAAU,GAGpCgK,IAAgB,MAAM;QAC1BP,EAAa,CAAA,CAAK,GAClBF,KAAA,QAAAA;IAAY,GAIRU,IAAgB,MAAM;QAC1B,OAAQlR,EAAO,WAAA,EAAA;YACb,KAAK;gBACH,OAAOiO,IACLlG,aAAAA,GAAAA,EAAAA,GAAAA,CAACiG,IAAA;oBACC,iBAAiB+C;oBACjB,qBAAA9C;oBACA,OAAAvE;oBACA,wBAAwBoH;oBACxB,uBAAuBE;oBACvB,wBAAwBR;gBAAA,KAExB;YAEN,KAAK;gBACH,OACEzI,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACZ,UAAA,EAAuB,GAAA,CAAI,CAACjB,GAAgByF,IAC3CxE,aAAAA,GAAAA,EAAAA,GAAAA,CAAC+F,GAAA;4BAEC,gBAAAhH;4BACA,OAAA4C;4BACA,SAAS,CAAA;4BACT,YAAY,CAAA;4BACZ,SAASsH;wBAAA,GALJlK,EAAe,KAAA,IAASyF;gBAOhC,CACH;YAGJ,KAAK;gBACH,OAAOwE,EAAuB,MAAA,GAAS,IACrCzF,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAA;gCAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;gCAAA,CAAmC;gCAClDuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;oCAAK,WAAU;oCACb,UAAA;wCAAAyF,EAAuB,MAAA;wCAAO;wCAAmBA,EAAuB,MAAA,GAAS,IAAI,OAAO;wCAAG;qCAAA;gCAAA,CAClG;6BAAA;wBAAA,CACF;wBACAhJ,aAAAA,GAAAA,EAAAA,GAAAA,CAAC+F,GAAA;4BACC,gBAAgBiD,CAAAA,CAAuB,CAAC,CAAA;4BACxC,OAAArH;4BACA,SAAS,CAAA;4BACT,YAAY,CAAA;4BACZ,SAASsH;wBAAA;wBAEVD,EAAuB,MAAA,GAAS,KAC/BzF,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BAAgD,UAAA;gCAAA;gCAC3DyF,EAAuB,MAAA,GAAS;gCAAE;gCAAqBA,EAAuB,MAAA,GAAS,IAAI,MAAM;6BAAA;wBAAA,CACrG;qBAAA;gBAAA,CAEJ,IACE;YAEN,KAAK;gBACH,OAAO9C,IACLlG,aAAAA,GAAAA,EAAAA,GAAAA,CAACoH,IAAA;oBACC,iBAAiB4B;oBACjB,kBAAkB9C;oBAClB,OAAAvE;oBACA,kBAAkB,CAAA;oBAClB,eAAc;oBACd,uBAAuBsH;gBAAA,KAEvB;YAEN,KAAK;gBACH,OACE1F,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAA;gCAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oCAAI,WAAU;oCACb,UAAA;wCAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4CAAI,WAAU;wCAAA,CAAmC;wCAClDA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;4CAAK,WAAU;4CAAyD,UAAA;wCAAA,CAEzE;qCAAA;gCAAA,CACF;gCACCyI,KACCzI,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;oCACC,SAASkJ;oCACT,WAAU;oCACV,cAAW;oCAEX,UAAAlJ,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;wCACjE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;wCAAA,CAAuB;oCAAA,CAC9F;gCAAA;6BACF;wBAAA,CAEJ;wBACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BACZ,UAAA,EAAuB,GAAA,CAAI,CAACjB,GAAgByF,IAC3CxE,aAAAA,GAAAA,EAAAA,GAAAA,CAAC+F,GAAA;oCAEC,gBAAAhH;oCACA,OAAA4C;oCACA,SAAS,CAAA;oCACT,YAAY,CAAA;oCACZ,SAASsH;gCAAA,GALJlK,EAAe,KAAA,IAASyF;wBAOhC,CACH;qBAAA;gBAAA,CACF;YAGJ;gBACE,OACExE,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACZ,UAAA,EAAuB,GAAA,CAAI,CAACjB,GAAgByF,IAC3CxE,aAAAA,GAAAA,EAAAA,GAAAA,CAAC0B,IAAA;4BAEC,gBAAA3C;4BACA,OAAA4C;4BACA,gBAAgB,CAAA;4BAChB,YAAY,CAAA;4BACZ,SAASsH;wBAAA,GALJlK,EAAe,KAAA,IAASyF;gBAOhC,CACH;QAAA;IAEN,GAGIH,IAAmBhI,EACvB,8BACA,2CACA;QACE,2BAA2B,CAACuM;QAC5B,6BAA6BA;QAC7B,wCAAwC3Q,EAAO,WAAA,KAAgB;QAC/D,QAAQA,EAAO,WAAA,KAAgB;QAC/B,4DAA4DA,EAAO,WAAA,KAAgB;IAAA,GAErFoH,IAGIiF,IAAiB3C,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA;IAE3B,OACE4B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;QACC,WAAWc;QACX,OAAOC;QACP,qBAAmB3C,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAC1B,uBAAqB1J,EAAO,OAAA;QAC5B,mBAAiBuQ;QAEhB,UAAA;YAAAW,EAAA;YAGAlR,EAAO,aAAA,KAAkB,CAAA,KACxB+H,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;oBAAK,WAAU;oBAA2C,UAAA;gBAAA,CAE3D;YAAA,CACF;SAAA;IAAA;AAIR,GC3MaoJ,KAAsD,CAAC,EAClE,SAAA1L,CAAAA,EACA,OAAAiE,CAAAA,EACA,uBAAAyE,CAAAA,EACA,WAAA/G,CAAAA,EACF,KAAM;IACJ,MAAMgK,IAAS3L,EAAQ,IAAA,KAAS,QAC1B4L,IAAc5L,EAAQ,IAAA,KAAS,aAE/B6L,IAAiBlN,EACrB,uBACA,0BACA;QACE,oBAAoBgN;IAAA,GAEtBhK,IAGImK,IAAgBnN,EACpB,qDACA;QACE,2DAA2DgN;QAC3D,kEAAkEC;QAClE,yEAAyE5L,EAAQ,IAAA,KAAS;IAAA,IAIxF4G,IAAiB3C,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA,GAErB8H,IAAa,CAACC,IACXA,EAAU,kBAAA,CAAmB,EAAA,EAAI;YAAE,MAAM;YAAW,QAAQ;QAAA,CAAW;IAGhF,OACEnG,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;QACC,WAAWgG;QACX,OAAOjF;QACP,qBAAmB3C,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAGzB,UAAA;YAAA,CAAC0H,KACArJ,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBAAqB,MAAK;oBAAO,QAAO;oBAAe,SAAQ;oBAC5E,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;oBAAA,CAA6B;gBAAA,CACpG;YAAA,CACF;YAGDqJ,KACCrJ,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBAA2C,MAAK;oBAAO,QAAO;oBAAe,SAAQ;oBAClG,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;oBAAA,CAAsE;gBAAA,CAC7I;YAAA,CACF;YAAA,aAAA,GAAA,EAAA,IAAA,CAID,OAAA;gBAAI,WAAW,CAAA,cAAA,EAAiBqJ,IAAS,cAAc,aAAa,CAAA,OAAA,CAAA;gBAEnE,UAAA;oBAAArJ,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAWwJ;wBACd,UAAAxJ,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BACZ,UAAAtC,EAAQ,OAAA;wBAAA,CACX;oBAAA,CACF;oBAGAsC,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAW3D,EACd,iDACA;4BAAE,cAAcgN;wBAAA;wBAEf,UAAAI,EAAW/L,EAAQ,SAAS;oBAAA,CAC/B;oBAGCA,EAAQ,eAAA,IAAmBA,EAAQ,eAAA,CAAgB,MAAA,GAAS,KAC3D6F,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;wBAAI,WAAU;wBAEb,UAAA;4BAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gCAAI,WAAU;gCACb,UAAA;oCAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wCAAI,WAAU;wCAA2C,MAAK;wCAAO,QAAO;wCAAe,SAAQ;wCAClG,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;wCAAA,CAA6B;oCAAA,CACpG;oCACAuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;wCAAK,WAAU;wCACb,UAAA;4CAAA7F,EAAQ,eAAA,CAAgB,MAAA;4CAAO;4CAAgBA,EAAQ,eAAA,CAAgB,MAAA,GAAS,IAAI,MAAM;4CAAG;yCAAA;oCAAA,CAChG;iCAAA;4BAAA,CACF;4BAGAsC,aAAAA,GAAAA,EAAAA,GAAAA,CAACuI,IAAA;gCACC,iBAAiB7K,EAAQ,eAAA;gCACzB,QAAQ;oCACN,aAAa;oCACb,SAAS;oCACT,oBAAoB;oCACpB,eAAe,CAAA;oCACf,UAAU,CAAA;oCACV,SAAS;gCAAA;gCAEX,OAAAiE;gCACA,uBAAAyE;gCACA,WAAU;4BAAA;yBACZ;oBAAA,CACF;iBAAA;YAAA,CAEJ;SAAA;IAAA;AAGN,GC7GauD,KAAkD,CAAC,EAC9D,aAAAC,IAAc,sBAAA,EACd,UAAAC,IAAW,CAAA,CAAA,EACX,aAAAC,IAAc,CAAA,CAAA,EACd,OAAAnI,CAAAA,EACA,eAAAoI,CAAAA,EACA,WAAA1K,CAAAA,EACF,KAAM;IACJ,MAAM,CAAC3B,GAASsM,CAAU,CAAA,gLAAI7M,EAAS,EAAE,GACnC,CAAC8M,GAAiBC,CAAkB,CAAA,qKAAI/M,WAAAA,EAAS,CAAA,CAAK,GACtD,CAACgN,GAAqBC,CAAsB,CAAA,gLAAIjN,EAAmB,CAAA,CAAE,GACrEkN,sKAAW7K,SAAAA,EAA4B,IAAI,GAE3C8K,IAAoB,CAAC5G,MAA8C;QACvE,MAAMrK,IAAQqK,EAAE,MAAA,CAAO,KAAA;QAIvB,IAHAsG,EAAW3Q,CAAK,GAGZA,EAAM,IAAA,CAAA,KAAUyQ,EAAY,MAAA,GAAS,GAAG;YAC1C,MAAMS,IAAWT,EAAY,MAAA,CAAO,CAAA,IAClCU,EAAW,WAAA,CAAA,EAAc,QAAA,CAASnR,EAAM,WAAA,EAAa;YAEvD+Q,EAAuBG,CAAQ,GAC/BL,EAAmBK,EAAS,MAAA,GAAS,CAAC;QAAA,OAEtCL,EAAmB,CAAA,CAAK;QAItBG,EAAS,OAAA,IAAA,CACXA,EAAS,OAAA,CAAQ,KAAA,CAAM,MAAA,GAAS,QAChCA,EAAS,OAAA,CAAQ,KAAA,CAAM,MAAA,GAAS,GAAG,KAAK,GAAA,CAAIA,EAAS,OAAA,CAAQ,YAAA,EAAc,GAAG,CAAC,CAAA,EAAA,CAAA;IACjF,GAGII,IAAgB,CAAC/G,MAA0C;QAC3DA,EAAE,GAAA,KAAQ,WAAW,CAACA,EAAE,QAAA,IAAA,CAC1BA,EAAE,cAAA,CAAA,GACFgH,EAAA,CAAA;IACF,GAGIA,IAAa,MAAM;QACvB,MAAMC,IAAiBjN,EAAQ,IAAA,CAAA;QAC3BiN,KAAkB,CAACd,KAAYE,KAAAA,CACjCA,EAAcY,CAAc,GAC5BX,EAAW,EAAE,GACbE,EAAmB,CAAA,CAAK,GAGpBG,EAAS,OAAA,IAAA,CACXA,EAAS,OAAA,CAAQ,KAAA,CAAM,MAAA,GAAS,MAAA,CAAA;IAEpC,GAGIO,IAAwB,CAACJ,MAAuB;QACpDR,EAAWQ,CAAU,GACrBN,EAAmB,CAAA,CAAK,GACpBG,EAAS,OAAA,IACXA,EAAS,OAAA,CAAQ,KAAA,CAAA;IACnB,GAGIhG,IAAmBhI,EACvB,qBACA,YACAgD,IAGIwL,IAAexO,EACnB,8EACA,+DACA,kDACA,sFACA,sGACA,qCACA;QACE,iCAAiCwN;IAAA,IAI/BiB,IAAoBzO,EACxB,wEACA,oCACA;QACE,4CAA4CqB,EAAQ,IAAA,CAAA,KAAU,CAACmM;QAC/D,qFAAqF,CAACnM,EAAQ,IAAA,MAAUmM;IAAA,IAItGvF,IAAiB3C,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA;IAE3B,OACE4B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;QACC,WAAWc;QACX,OAAOC;QACP,qBAAmB3C,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAGzB,UAAA;YAAAsI,KAAmBE,EAAoB,MAAA,GAAS,KAC/CnK,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAU;gBACZ,UAAAmK,EAAoB,KAAA,CAAM,GAAG,CAAC,EAAE,GAAA,CAAI,CAACK,GAAYhG,IAChDxE,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;wBAEC,SAAS,IAAM4K,EAAsBJ,CAAU;wBAC/C,WAAU;wBAET,UAAAA;oBAAA,GAJIhG;YAMR,CACH;YAIFjB,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAA;oBAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,YAAA;wBACC,KAAKqK;wBACL,OAAO3M;wBACP,UAAU4M;wBACV,WAAWG;wBACX,aAAAb;wBACA,UAAAC;wBACA,MAAM;wBACN,WAAWgB;wBACX,OAAO;4BAAE,WAAW;4BAAQ,WAAW;wBAAA;oBAAQ;oBAIjD7K,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;wBACC,SAAS0K;wBACT,UAAU,CAAChN,EAAQ,IAAA,CAAA,KAAUmM;wBAC7B,WAAWiB;wBACX,cAAW;wBAEX,UAAA9K,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;4BACjE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;4BAAA,CAAmC;wBAAA,CAC1G;oBAAA;iBACF;YAAA,CACF;YAGAuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAA;oBAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;wBAAK,UAAA;oBAAA,CAA6C;oBACnDuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;wBAAK,WAAWlH,EACf,mCACA;4BAAE,aAAaqB,EAAQ,MAAA,GAAS;wBAAA;wBAE/B,UAAA;4BAAAA,EAAQ,MAAA;4BAAO;yBAAA;oBAAA,CAClB;iBAAA;YAAA,CACF;SAAA;IAAA;AAGN,GCzJaqN,KAA0D,CAAC,EACtE,UAAAC,CAAAA,EACA,QAAA/S,CAAAA,EACA,OAAA0J,CAAAA,EACA,WAAAsJ,IAAY,CAAA,CAAA,EACZ,eAAAlB,CAAAA,EACA,uBAAA3D,CAAAA,EACA,WAAA/G,CAAAA,EACF,KAAM;IACJ,MAAM6L,sKAAiB1L,SAAAA,EAAuB,IAAI,GAC5C2L,sKAAuB3L,SAAAA,EAAuB,IAAI;kLAGxDE,EAAU,MAAM;QACVwL,EAAe,OAAA,IACjBA,EAAe,OAAA,CAAQ,cAAA,CAAe;YAAE,UAAU;QAAA,CAAU;IAC9D,GACC;QAACF,CAAQ;KAAC;IAEb,MAAM3G,IAAmBhI,EACvB,yBACA,mDACAgD,IAGIiF,IAAiB3C,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA,GAGrByJ,IAAkBnT,EAAO,WAAA,GAC3B+S,EAAS,KAAA,CAAM,CAAC/S,EAAO,WAAW,IAClC+S;IAEJ,OACEzH,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;QACC,WAAWc;QACX,OAAOC;QACP,qBAAmB3C,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAG1B,UAAA;YAAA3B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBACC,KAAKmL;gBACL,WAAU;gBAET,UAAA,EAAgB,MAAA,KAAW,IAC1B5H,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCAAI,WAAU;gCAA2C,MAAK;gCAAO,QAAO;gCAAe,SAAQ;gCAClG,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;gCAAA,CAA6B;4BAAA,CACpG;wBAAA,CACF;wBACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;4BAAG,WAAU;4BAA8D,UAAA;wBAAA,CAE5E;wBACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,KAAA;4BAAE,WAAU;4BAAoD,UAAA;wBAAA,CAEjE;qBAAA;gBAAA,CACF,IAEAuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAAO,EAAAA,QAAAA,EAAA;oBACG,UAAA;wBAAAsH,EAAgB,GAAA,CAAI,CAAC1N,IACpBsC,aAAAA,GAAAA,EAAAA,GAAAA,CAACoJ,IAAA;gCAEC,SAAA1L;gCACA,OAAAiE;gCACA,uBAAAyE;4BAAA,GAHK1I,EAAQ,EAAA;wBAQhBuN,KAAahT,EAAO,qBAAA,KAA0B,CAAA,KAC7CsL,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAA;gCAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;oCACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wCAAI,WAAU;wCAAqB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;wCAC5E,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;wCAAA,CAA6B;oCAAA,CACpG;gCAAA,CACF;gCAAA,aAAA,GAAA,EAAA,GAAA,CACC,OAAA;oCAAI,WAAU;oCACb,UAAAuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;wCAAI,WAAU;wCACb,UAAA;4CAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gDAAI,WAAU;4CAAA,CAAmE;4CAClFA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gDAAI,WAAU;gDAAmE,OAAO;oDAAE,gBAAgB;gDAAA;4CAAA,CAAU;4CACrHA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gDAAI,WAAU;gDAAmE,OAAO;oDAAE,gBAAgB;gDAAA;4CAAO,CAAG;yCAAA;oCAAA,CACvH;gCAAA,CACF;6BAAA;wBAAA,CACF;wBAGFA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,KAAKkL;wBAAA,CAAgB;qBAAA;gBAAA,CAC5B;YAAA;YAKHjT,EAAO,iBAAA,IAAqBA,EAAO,WAAA,IAAeA,EAAO,WAAA,CAAY,MAAA,GAAS,KAAK+S,EAAS,MAAA,KAAW,KACtGzH,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAA;oBAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAU;wBAAgD,UAAA;oBAAA,CAAkB;oBACjFA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAU;wBACZ,UAAA/H,EAAO,WAAA,CAAY,KAAA,CAAM,GAAG,CAAC,EAAE,GAAA,CAAI,CAACuS,GAAYhG,IAC/CxE,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;gCAEC,SAAS,IAAM+J,KAAA,OAAA,KAAA,IAAAA,EAAgBS;gCAC/B,WAAU;gCAET,UAAAA;4BAAA,GAJIhG;oBAMR,CACH;iBAAA;YAAA,CACF;YAIDvM,EAAO,cAAA,KAAmB,CAAA,KAAS8R,KAClC/J,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC2J,IAAA;oBACC,aAAa1R,EAAO,WAAA,IAAe;oBACnC,UAAUgT;oBACV,aAAahT,EAAO,WAAA;oBACpB,OAAA0J;oBACA,eAAAoI;gBAAA;YACF,CACF;SAAA;IAAA;AAIR,GC9HasB,KAAwD,CAAC,EACpE,QAAApT,CAAAA,EACA,OAAA0J,CAAAA,EACA,OAAAkG,IAAQ,cAAA,EACR,UAAAyD,IAAW,kCAAA,EACX,QAAQC,CAAAA,EACR,UAAAC,CAAAA,EACA,eAAAzB,CAAAA,EACA,uBAAA3D,CAAAA,EACA,qBAAAqF,CAAAA,EACA,2BAAAC,CAAAA,EACA,gBAAAC,IAAiB,CAAA,CAAA,EACjB,yBAAAC,IAA0B,CAAA,CAAA,EAC1B,6BAAAC,CAAAA,EACA,WAAAxM,CAAAA,EACF,KAAM;IACJ,MAAM,CAACyM,GAAgBC,CAAiB,CAAA,gLAAI5O,EAASlF,EAAO,QAAA,IAAY,CAAA,CAAK,GACvE,CAAC+S,GAAUgB,CAAW,CAAA,gLAAI7O,EAAwB,CAAA,CAAE,GACpD,CAAC8N,GAAWgB,CAAY,CAAA,gLAAI9O,EAAS,CAAA,CAAK,GAC1C,CAAC+O,GAAeC,CAAgB,CAAA,gLAAIhP,EAAS,CAAA,CAAK,GAElDiP,IAASb,MAAqB,KAAA,IAAYA,IAAmBO;kLAGnEpM,EAAU,MAAM;QACd,IAAIzH,EAAO,kBAAA,IAAsBA,EAAO,cAAA,IAAkB+S,EAAS,MAAA,KAAW,GAAG;YAC/E,MAAMqB,IAA8B;gBAClC,IAAI;gBACJ,MAAM;gBACN,SAASpU,EAAO,cAAA;gBAChB,WAAA,aAAA,GAAA,IAAe,KAAA;YAAK;YAEtB+T,EAAY;gBAACK,CAAc;aAAC;QAAA;IAC9B,GACC;QAACpU,EAAO,kBAAA;QAAoBA,EAAO,cAAA;QAAgB+S,EAAS,MAAM;KAAC,oKAGtEtL,aAAAA,EAAU,MAAM;QACd,IAAI+L,KAAuBA,EAAoB,MAAA,GAAS,KAAKG,GAAyB;YACpF,MAAMU,IAA2B;gBAC/B,IAAI,CAAA,KAAA,EAAQ,KAAK,GAAA,CAAA,CAAK,EAAA;gBACtB,MAAM;gBACN,SAASZ,IACL,CAAA,UAAA,EAAaA,CAAyB,CAAA,0CAAA,CAAA,GACtC;gBACJ,WAAA,aAAA,GAAA,IAAe,KAAA;gBACf,iBAAiBD;YAAA;YAIfF,MAAqB,KAAA,KACvBQ,EAAkB,CAAA,CAAI,GAIxBC,EAAY,CAAAO,IAEaA,EAAK,IAAA,CAAK,CAAAC,IAAOA,EAAI,EAAA,CAAG,UAAA,CAAW,OAAO,CAAC,IAEzDD,EAAK,GAAA,CAAI,CAAA,IACdC,EAAI,EAAA,CAAG,UAAA,CAAW,OAAO,IAAIF,IAAcE,KAGxC,CAAC;uBAAGD;oBAAMD,CAAW;iBAC7B;QAAA;IACH,GACC;QAACb;QAAqBG;QAAyBF;QAA2BH,CAAgB;KAAC;IAE9F,MAAMkB,IAAe,MAAM;QACrBjB,IACFA,EAAA,IAEAO,EAAkB,CAACD,CAAc,GAEnCK,EAAiB,CAAA,CAAI;IAAA,GAGjBO,IAAoB,OAAOC,MAA2B;QAC1D,IAAI,CAAC5C,EAAe,CAAA;QAGpB,MAAM6C,IAA2B;YAC/B,IAAI,CAAA,KAAA,EAAQ,KAAK,GAAA,CAAA,CAAK,EAAA;YACtB,MAAM;YACN,SAASD;YACT,WAAA,aAAA,GAAA,IAAe,KAAA;QAAK;QAGtBX,EAAY,CAAAO,IAAQ,CAAC;mBAAGA;gBAAMK,CAAW;aAAC,GAC1CX,EAAa,CAAA,CAAI;QAEjB,IAAI;YAEF,MAAMlC,EAAc4C,CAAc;QAAA,EAAA,OAE3BvP,GAAO;YACd,QAAQ,KAAA,CAAM,0BAA0BA,CAAK;YAC7C,MAAMyP,IAA4B;gBAChC,IAAI,CAAA,MAAA,EAAS,KAAK,GAAA,CAAA,CAAK,EAAA;gBACvB,MAAM;gBACN,SAAS;gBACT,WAAA,aAAA,GAAA,IAAe,KAAA;YAAK;YAEtBb,EAAY,CAAAO,IAAQ,CAAC;uBAAGA;oBAAMM,CAAY;iBAAC;QAAA,SAC7C;YACEZ,EAAa,CAAA,CAAK;QAAA;IACpB,GAIIa,IAAoB,MAAM;QAC9B,OAAQ7U,EAAO,IAAA,EAAA;YACb,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAM,OAAO;YAClB;gBAAS,OAAO;QAAA;IAClB,GAcIoM,IAAmBhI,EACvB,wBACA,sDAAA,CAZyB,MAAM;QAC/B,OAAQpE,EAAO,QAAA,EAAA;YACb,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAAA;IAClB,CAAA,EAMA,GACAoH,IAGI0N,IAAc1Q,EAClB,iHACAyQ,EAAA,GACA;QACE,0CAA0C,CAACV;QAC3C,yBAAyBA;IAAA,IAIvB9H,IAAiB3C,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA;IAE3B,OACE4B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;QACC,WAAWc;QACX,OAAOC;QACP,qBAAmB3C,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAG1B,UAAA;YAAA3B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAW+M;gBACb,UAAAX,KACC7I,aAAAA,GAAAA,EAAAA,IAAAA,CAAAO,EAAAA,QAAAA,EAAA;oBAEE,UAAA;wBAAAP,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAA;gCAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oCAAI,WAAU;oCACb,UAAA;wCAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4CAAI,WAAU;4CACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;gDACjE,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;gDAAA,CAA6B;4CAAA,CACpG;wCAAA,CACF;wCAAA,aAAA,GAAA,EAAA,IAAA,CACC,OAAA;4CACC,UAAA;gDAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;oDAAG,WAAU;oDAAyB,UAAA6H;gDAAAA,CAAM;gDAC7C7H,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,KAAA;oDAAE,WAAU;oDAAyB,UAAAsL;gDAAA,CAAS;6CAAA;wCAAA,CACjD;qCAAA;gCAAA,CACF;gCACA/H,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oCAAI,WAAU;oCAEZ,UAAA;wCAAAkI,KAAuBA,EAAoB,MAAA,GAAS,KAAKI,KACxD7L,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;4CACC,SAAS,MAAM;gDACb6L,EAAA,GACAG,EAAY,CAAAO,IAAQA,EAAK,MAAA,CAAO,CAAAC,IAAO,CAACA,EAAI,EAAA,CAAG,UAAA,CAAW,OAAO,CAAC,CAAC;4CAAA;4CAErE,WAAU;4CACV,cAAW;4CACX,OAAM;4CAEN,UAAAxM,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;gDACjE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;gDAAA,CAAsL;4CAAA,CAC7P;wCAAA;wCAIJA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;4CACC,SAASyM;4CACT,WAAU;4CACV,cAAW;4CAEX,UAAAzM,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;gDACjE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;gDAAA,CAAuB;4CAAA,CAC9F;wCAAA;qCACF;gCAAA,CACF;6BAAA;wBAAA,CACF;wBAGAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC+K,IAAA;4BACC,UAAAC;4BACA,QAAQ;gCACN,GAAG/S,CAAAA;gCACH,gBAAA0T;4BAAA;4BAEF,OAAAhK;4BACA,WAAAsJ;4BACA,eAAeU,IAAiBe,IAAoB,KAAM,CAAA;4BAC1D,uBAAAtG;4BACA,WAAU;wBAAA;qBACZ;gBAAA,CACF;YAAA,CAEJ;YAGC,CAACgG,KACA7I,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,UAAA;gBACC,SAASkJ;gBACT,WAAWpQ,EACT,kGACA,iFACA;gBAEF,cAAW;gBAEX,UAAA;oBAAA2D,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;wBACjE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;wBAAA,CAAgK;oBAAA,CACvO;oBAGC,CAACkM,KACAlM,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAU;oBAAA,CAAyE;iBAAA;YAAA;YAM7FoM,KACCpM,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAU;gBAA4H,UAAA;YAAA,CAE3I;SAAA;IAAA;AAIR,GC5PagN,KAA0D,CAAC,EACtE,OAAAnF,CAAAA,EACA,OAAAlG,CAAAA,EACA,aAAA2D,IAAc,CAAA,CAAA,EACd,aAAA2H,IAAc,CAAA,CAAA,EACd,UAAAzB,CAAAA,EACA,UAAA0B,CAAAA,EACA,YAAAC,IAAa,CAAA,CAAA,EACb,WAAA9N,CAAAA,EACF,KAAM;IACJ,MAAM,CAAC+N,GAAaC,CAAc,CAAA,gLAAIlQ,EAAS,EAAE,GAC3C,CAACmQ,GAAiBC,CAAkB,CAAA,OAAIpQ,yKAAAA,EAAS,CAAA,CAAK,GACtD,CAACqQ,GAAUC,CAAW,CAAA,gLAAItQ,EAAS,CAAA,CAAK;qKAG9CuC,aAAAA,EAAU,MAAM;QACd,MAAMgO,IAAc,MAAM;YACxBD,EAAY,OAAO,UAAA,GAAa,GAAG;QAAA;QAGrC,OAAAC,EAAA,GACA,OAAO,gBAAA,CAAiB,UAAUA,CAAW,GACtC,IAAM,OAAO,mBAAA,CAAoB,UAAUA,CAAW;IAAA,GAC5D,EAAE;IAEL,MAAMC,IAAqB,CAACjK,MAA2C;QACrE,MAAMrK,IAAQqK,EAAE,MAAA,CAAO,KAAA;QACvB2J,EAAehU,CAAK,GACpB6T,KAAA,QAAAA,EAAW7T;IAAK,GAGZuU,IAAoB,MAAM;QAC9BP,EAAe,EAAE,GACjBH,KAAA,QAAAA,EAAW;IAAE,GAGTW,IAAgBxR,EACpB,yBACA,iGACAgD,IAGIiF,IAAiB3C,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA;IAE3B,OACE4B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;QACC,WAAWsK;QACX,OAAOvJ;QACP,qBAAmB3C,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAG1B,UAAA;YAAA4B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAA;oBAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;wBAAG,WAAU;wBACX,UAAA6H;oBAAAA,CACH;oBAEAtE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;wBAAI,WAAU;wBAEZ,UAAA;4BAAAiK,KAAYhC,KACXxL,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;gCACC,SAASwL;gCACT,WAAU;gCACV,OAAM;gCAEN,UAAAxL,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCACC,WAAU;oCACV,MAAK;oCACL,QAAO;oCACP,SAAQ;oCAER,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;oCAAA,CAAuB;gCAAA;4BAC9F;4BAKHsF,KACCtF,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;gCACC,SAASwL;gCACT,WAAU;gCACV,OAAOyB,IAAc,mBAAmB;gCAExC,UAAAjN,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCACC,WAAW3D,EACT,8EACA;wCAAE,cAAc4Q;oCAAA;oCAElB,MAAK;oCACL,QAAO;oCACP,SAAQ;oCAER,UAAAjN,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;oCAAA,CAAkB;gCAAA;4BACzF;yBACF;oBAAA,CAEJ;iBAAA;YAAA,CACF;YAGCmN,KAAc,CAACF,KACd1J,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAA;oBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;wBAAI,WAAWlH,EACd,0DACA;4BACE,2CAA2CiR;wBAAA;wBAI7C,UAAA;4BAAAtN,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCAAI,WAAU;gCACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;oCAA2C,MAAK;oCAAO,QAAO;oCAAe,SAAQ;oCAClG,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;oCAAA,CAA8C;gCAAA,CACrH;4BAAA,CACF;4BAGAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,SAAA;gCACC,MAAK;gCACL,OAAOoN;gCACP,UAAUO;gCACV,SAAS,IAAMJ,EAAmB,CAAA,CAAI;gCACtC,QAAQ,IAAMA,EAAmB,CAAA,CAAK;gCACtC,aAAY;gCACZ,WAAWlR,EACT,sHACA,mFACA,yGACA;4BACF;4BAID+Q,KACCpN,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;gCACC,SAAS4N;gCACT,WAAU;gCACV,OAAM;gCAEN,UAAA5N,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;oCAA2C,MAAK;oCAAO,QAAO;oCAAe,SAAQ;oCAClG,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;oCAAA,CAAuB;gCAAA,CAC9F;4BAAA;yBACF;oBAAA,CAEJ;oBAGCoN,KACCpN,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAU;wBAAgD,UAAA;oBAAA,CAE/D;iBAAA;YAAA,CAEJ;YAID,CAACiN,KACA1J,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAA;oBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;wBAAI,WAAU;wBACb,UAAA;4BAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCAAI,WAAU;4BAAA,CAAoC;4BACnDA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;gCAAK,UAAA;4BAAA,CAAoB;yBAAA;oBAAA,CAC5B;oBACAuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;wBAAI,WAAU;wBACb,UAAA;4BAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;gCACjE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;gCAAA,CAA6B;4BAAA,CACpG;4BACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;gCAAK,UAAA;4BAAA,CAAU;yBAAA;oBAAA,CAClB;iBAAA;YAAA,CACF;SAAA;IAAA;AAIR,GC1Ka8N,KAA4D,CAAC,EACxE,iBAAAvM,CAAAA,EACA,aAAAwM,CAAAA,EACA,OAAApM,CAAAA,EACA,oBAAAoH,CAAAA,EACA,uBAAA3C,CAAAA,EACA,WAAA/G,CAAAA,EACF,KAAM;IACJ,MAAM,CAAC2O,GAAaC,CAAc,CAAA,gLAAI9Q,EAAS,CAAA,CAAK,GAC9C,CAAC+Q,GAAWC,CAAY,CAAA,IAAIhR,4KAAAA,EAAmC,KAAK,GAEpE6L,IAAyBD,IAC3BxH,EAAgB,KAAA,CAAM,GAAGwH,CAAkB,IAC3CxH,GAeE6M,IAAAA,CAbwB,MAAM;QAClC,OAAQF,GAAA;YACN,KAAK;gBACH,OAAOlF,EACJ,MAAA,CAAO,CAAAxH,IAAOA,EAAI,kBAAA,IAAsB,GAAG,EAC3C,KAAA,CAAM,GAAG,CAAC;YACf,KAAK;gBACH,OAAOwH,EAAuB,KAAA,CAAM,GAAG,CAAC;YAC1C;gBACE,OAAOA;QAAA;IACX,CAAA,EAGyB,GAErBqF,IAAiBhS,EACrB,0BACA,wBACAgD,IAGIiF,IAAiB3C,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA,GAErB2M,IAAwB,MAAM;QAClC,IAAIF,EAAmB,MAAA,KAAW,GAChC,OACE7K,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;YAAI,WAAU;YACb,UAAA;gBAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAU;wBAA2C,MAAK;wBAAO,QAAO;wBAAe,SAAQ;wBAClG,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;wBAAA,CAA8C;oBAAA,CACrH;gBAAA,CACF;gBACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;oBAAG,WAAU;oBAA4D,UAAA;gBAAA,CAE1E;gBACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,KAAA;oBAAE,WAAU;oBAA2C,UAAA;gBAAA,CAExD;aAAA;QAAA,CACF;QAIJ,OAAQ+N,GAAA;YACN,KAAK;gBACH,OACE/N,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACZ,UAAA,EAAmB,GAAA,CAAI,CAACjB,GAAgByF,IACvCxE,aAAAA,GAAAA,EAAAA,GAAAA,CAAC+F,GAAA;4BAEC,gBAAAhH;4BACA,OAAA4C;4BACA,SAAS,CAAA;4BACT,YAAY,CAAA;4BACZ,SAASyE;wBAAA,GALJrH,EAAe,KAAA,IAASyF;gBAOhC,CACH;YAGJ,KAAK;gBACH,OACExE,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACZ,UAAAoO,EAAmB,GAAA,CAAI,CAACrP,GAAgByF,IACvCjB,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAwC,WAAU;4BACjD,UAAA;gCAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;gCAAA,CAAiD;gCAChEuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oCAAI,WAAU;oCACb,UAAA;wCAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4CAAI,WAAU;4CACZ,UAAAjB,EAAe,KAAA;wCAAA,CAClB;wCACAwE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4CAAI,WAAU;4CACZ,UAAA;gDAAA,KAAK,KAAA,CAAMxE,EAAe,kBAAA,GAAqB,GAAG;gDAAE;6CAAA;wCAAA,CACvD;qCAAA;gCAAA,CACF;6BAAA;wBAAA,GATQA,EAAe,KAAA,IAASyF,CAUlC,CACD;gBAAA,CACH;YAGJ,KAAK;gBACH,OAAA,aAAA,GAAA,EAAA,GAAA,CACG,OAAA;oBAAI,WAAU;oBACZ,UAAA4J,EAAmB,KAAA,CAAM,GAAG,CAAC,EAAE,GAAA,CAAI,CAACrP,GAAgByF,IACnDjB,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAwC,WAAU;4BACjD,UAAA;gCAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC+F,GAAA;oCACC,gBAAAhH;oCACA,OAAA4C;oCACA,SAAS,CAAA;oCACT,YAAY,CAAA;oCACZ,SAASyE;gCAAA;gCAAA,aAAA,GAAA,EAAA,GAAA,CAEV,UAAA;oCAAO,WAAU;oCAChB,UAAApG,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wCAAI,WAAU;wCAA0B,MAAK;wCAAe,SAAQ;wCACnE,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;4CAAK,GAAE;wCAAA,CAA+F;oCAAA,CACzG;gCAAA,CACF;6BAAA;wBAAA,GAZQjB,EAAe,KAAA,IAASyF,CAalC,CACD;gBAAA,CACH;YAGJ,KAAK;gBACH,OACEjB,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBAEZ,UAAA;wBAAA6K,CAAAA,CAAmB,CAAC,CAAA,IACnB7K,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BACC,UAAA;gCAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;oCAAG,WAAU;oCAAoF,UAAA;gCAAA,CAElG;gCACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC0B,IAAA;oCACC,gBAAgB0M,CAAAA,CAAmB,CAAC,CAAA;oCACpC,OAAAzM;oCACA,gBAAgB,CAAA;oCAChB,YAAY,CAAA;oCACZ,SAASyE;oCACT,WAAU;gCAAA;6BACZ;wBAAA,CACF;wBAIDgI,EAAmB,KAAA,CAAM,CAAC,EAAE,MAAA,GAAS,KAAA,aAAA,GAAA,EAAA,IAAA,CACnC,OAAA;4BACC,UAAA;gCAAApO,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;oCAAG,WAAU;oCAAoF,UAAA;gCAAA,CAElG;gCACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;oCACZ,UAAAoO,EAAmB,KAAA,CAAM,GAAG,CAAC,EAAE,GAAA,CAAI,CAACrP,GAAgByF,IACnDxE,aAAAA,GAAAA,EAAAA,GAAAA,CAAC+F,GAAA;4CAEC,gBAAAhH;4CACA,OAAA4C;4CACA,SAAS,CAAA;4CACT,YAAY,CAAA;4CACZ,SAASyE;wCAAA,GALJrH,EAAe,KAAA,IAASyF;gCAOhC,CACH;6BAAA;wBAAA,CACF;qBAAA;gBAAA,CAEJ;YAGJ;gBACE,OACExE,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACZ,UAAA,EAAmB,GAAA,CAAI,CAACjB,GAAgByF,IACvCxE,aAAAA,GAAAA,EAAAA,GAAAA,CAAC+F,GAAA;4BAEC,gBAAAhH;4BACA,OAAA4C;4BACA,SAAS,CAAA;4BACT,YAAY,CAAA;4BACZ,SAASyE;wBAAA,GALJrH,EAAe,KAAA,IAASyF;gBAOhC,CACH;QAAA;IAEN;IAGF,OACEjB,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;QACC,WAAW8K;QACX,OAAO/J;QACP,qBAAmB3C,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAG1B,UAAA;YAAA4B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAA;oBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,UAAA;wBACC,SAAS,IAAM4K,EAAa,KAAK;wBACjC,WAAW9R,EACT,0DACA6R,MAAc,QACV,qFACA;wBAEP,UAAA;4BAAA;4BACO3M,EAAgB,MAAA;4BAAO;yBAAA;oBAAA;oBAE/BvB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;wBACC,SAAS,IAAMmO,EAAa,KAAK;wBACjC,WAAW9R,EACT,0DACA6R,MAAc,QACV,qFACA;wBAEP,UAAA;oBAAA;oBAGDlO,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;wBACC,SAAS,IAAMmO,EAAa,QAAQ;wBACpC,WAAW9R,EACT,0DACA6R,MAAc,WACV,qFACA;wBAEP,UAAA;oBAAA;iBAED;YAAA,CACF;YAGAlO,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAU;gBAAqC,OAAO;oBACzD,yBAAyB;oBAAA,0BAAA;oBACzB,oBAAoB;gBAAA;gBAEnB,UAAA;YAAA,CACH;YAAA,aAAA,GAAA,EAAA,GAAA,CAGC,OAAA;gBAAI,WAAU;gBACb,UAAAuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;4BAAK,WAAU;4BACb,UAAA;gCAAA6K,EAAmB,MAAA;gCAAO;gCAAgBA,EAAmB,MAAA,KAAW,IAAI,MAAM;6BAAA;wBAAA,CACrF;wBACApO,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;4BACC,SAAS,IAAMiO,EAAe,CAACD,CAAW;4BAC1C,WAAU;4BACX,UAAA;wBAAA;qBAED;gBAAA,CACF;YAAA,CACF;SAAA;IAAA;AAGN,GCrPaO,KAA8C,CAAC,EAC1D,iBAAAhN,CAAAA,EACA,QAAAtJ,CAAAA,EACA,OAAA0J,CAAAA,EACA,OAAAkG,IAAQ,iBAAA,EACR,QAAAuE,IAAS,CAAA,CAAA,EACT,UAAAZ,CAAAA,EACA,uBAAApF,CAAAA,EACA,UAAA8G,CAAAA,EAAA,YAAA;AAEA,WAAA7N,CAAAA,EACA,eAAAmP,IAAgB,CAAA,CAAA,EAClB,KAAM;IACJ,MAAM,CAACvB,GAAawB,CAAc,CAAA,gLAAItR,EAASlF,EAAO,gBAAA,IAAoB,CAAA,CAAK,GACzE,CAACmV,GAAaC,CAAc,CAAA,GAAIlQ,6KAAAA,EAAS,EAAE,GAC3C,CAACuR,CAAO,CAAA,gLAAIvR,EAAyB,CAAA,CAAE,GACvC,CAACqQ,GAAUC,CAAW,CAAA,qKAAItQ,WAAAA,EAAS,CAAA,CAAK;kLAG9CuC,EAAU,MAAM;QACd,MAAMgO,IAAc,MAAM;YACxBD,EAAY,OAAO,UAAA,GAAa,GAAG;QAAA;QAGrC,OAAAC,EAAA,GACA,OAAO,gBAAA,CAAiB,UAAUA,CAAW,GACtC,IAAM,OAAO,mBAAA,CAAoB,UAAUA,CAAW;IAAA,GAC5D,EAAE,iLAGLhO,EAAU,MAAM;QACd,IAAI8N,KAAYpB,KAAU,CAACa,KAAe,CAACuB,GAAe;YACxD,MAAMG,IAAgB,OAAO,gBAAA,CAAiB,SAAS,IAAI,EAAE,QAAA;YAC7D,OAAA,SAAS,IAAA,CAAK,KAAA,CAAM,QAAA,GAAW,UAC/B,SAAS,IAAA,CAAK,KAAA,CAAM,QAAA,GAAW,SAC/B,SAAS,IAAA,CAAK,KAAA,CAAM,KAAA,GAAQ,QAErB,MAAM;gBACX,SAAS,IAAA,CAAK,KAAA,CAAM,QAAA,GAAWA,GAC/B,SAAS,IAAA,CAAK,KAAA,CAAM,QAAA,GAAW,IAC/B,SAAS,IAAA,CAAK,KAAA,CAAM,KAAA,GAAQ;YAAA;QAC9B;IACF,GACC;QAACnB;QAAUpB;QAAQa;QAAauB,CAAa;KAAC,iLAGjD9O,EAAU,MAAM;QACd,IAAIzH,EAAO,WAAA,IAAeA,EAAO,eAAA,EAAiB;YAChD,MAAM2W,IAAW,YAAY,MAAM;gBAEjC,QAAQ,GAAA,CAAI,oCAAoC;YAAA,GAC/C3W,EAAO,eAAe;YAEzB,OAAO,IAAM,cAAc2W,CAAQ;QAAA;IACrC,GACC;QAAC3W,EAAO,WAAA;QAAaA,EAAO,eAAe;KAAC;IAG/C,MAAM4W,sKAA0BtR,UAAAA,EAAQ,MAAM;QAC5C,IAAIgN,IAAW,CAAC;eAAGhJ,CAAe;SAAA;QAGlC,IAAI6L,EAAY,IAAA,IAAQ;YACtB,MAAM0B,IAAQ1B,EAAY,WAAA,CAAA;YAC1B7C,IAAWA,EAAS,MAAA,CAAO,CAAA/I,MAAA;;gBACzB,OAAAA,EAAI,KAAA,CAAM,WAAA,CAAA,EAAc,QAAA,CAASsN,CAAK,KACtCtN,EAAI,MAAA,CAAO,WAAA,CAAA,EAAc,QAAA,CAASsN,CAAK,KAAA,CAAA,CACvC3M,IAAAX,EAAI,QAAA,KAAJ,OAAA,KAAA,IAAAW,EAAc,IAAA,CAAK,CAAAC,IAAWA,EAAQ,WAAA,CAAA,EAAc,QAAA,CAAS0M,CAAK,EAAA;YAAA;QACpE;QAIF,OAAIJ,EAAQ,UAAA,IAAcA,EAAQ,UAAA,CAAW,MAAA,GAAS,KAAA,CACpDnE,IAAWA,EAAS,MAAA,CAAO,CAAA/I,MAAA;;YACzB,OAAA,CAAAW,IAAAX,EAAI,UAAA,KAAJ,OAAA,KAAA,IAAAW,EAAgB,IAAA,CAAK,CAAA;;gBAAO,OAAA,CAAAA,IAAAuM,EAAQ,UAAA,KAAR,OAAA,KAAA,IAAAvM,EAAoB,QAAA,CAAS4M;YAAA;QAAA,EAAI,GAK7DL,EAAQ,WAAA,EAORA,EAAQ,QAAA,IAAA,CACVnE,IAAWA,EAAS,MAAA,CAAO,CAAA/I,IAAOA,EAAI,UAAA,IAAcA,EAAI,UAAA,GAAa,CAAC,CAAA,GAIpEkN,EAAQ,aAAA,KAAkB,KAAA,KAAA,CAC5BnE,IAAWA,EAAS,MAAA,CAAO,CAAA/I,IAAOA,EAAI,kBAAA,IAAsBkN,EAAQ,aAAc,CAAA,GAIpFnE,EAAS,IAAA,CAAK,CAAChE,GAAGC,IAAMA,EAAE,kBAAA,GAAqBD,EAAE,kBAAkB,GAG/DtO,EAAO,kBAAA,IAAA,CACTsS,IAAWA,EAAS,KAAA,CAAM,GAAGtS,EAAO,kBAAkB,CAAA,GAGjDsS;IAAA,GACN;QAAChJ;QAAiB6L;QAAasB;QAASzW,EAAO,kBAAkB;KAAC,GAE/DwU,IAAe,MAAM;QACrBxU,EAAO,WAAA,IAAA,CACTwW,EAAe,CAACxB,CAAW,GAC3BzB,KAAA,QAAAA,GAAAA;IACF,GAGIwD,IAAe,CAACF,MAAkB;QACtCzB,EAAeyB,CAAK,GACpB5B,KAAA,QAAAA,EAAW4B;IAAK,GAsBZG,IAAiB5S,EACrB,kBACA,oIAAA,CAfsB,MAAM;QAC5B,IAAI4Q,EAAa,CAAA,OAAO;QAGxB,OAAQhV,EAAO,IAAA,EAAA;YACb,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAM,OAAO;YAClB;gBAAS,OAAO;QAAA;IAClB,CAAA,EAMA,GACA;QACE,YAAYA,EAAO,QAAA,KAAa;QAChC,YAAYA,EAAO,QAAA,KAAa;QAAA,0EAAA;QAAA,4DAAA;QAGhC,iCAAiC,CAACuW;QAClC,mBAAmBA;QACnB,UAAUvW,EAAO,QAAA,KAAa,UAAU,CAACuW;QACzC,WAAWvW,EAAO,QAAA,KAAa,WAAW,CAACuW;QAAA,mCAAA;QAE3C,+BAA+BvW,EAAO,QAAA,KAAa,UAAU,CAACmU,KAAU,CAACoC;QACzE,8BAA8BvW,EAAO,QAAA,KAAa,WAAW,CAACmU,KAAU,CAACoC;QAAA,+BAAA;QAEzE,WAAW,CAAA;QAAA,kCAAA;QACX,mBAAmB,CAACA;IAAA,GAEtBnP,IAGIiF,IAAiB3C,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA;IAE3B,OAAI,CAACyK,KAAU,CAACnU,EAAO,WAAA,GACd,OAIPsL,aAAAA,GAAAA,EAAAA,IAAAA,CAAAO,EAAAA,QAAAA,EAAA;QAEG,UAAA;YAAAsI,KAAU,CAACa,KACVjN,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBACC,WAAW3D,EACT,6EACAmS,IAAgB,qBAAqB;gBAEvC,SAAS,IAAMhD,KAAA,OAAA,KAAA,IAAAA;gBACf,OAAO;oBAAA,sDAAA;oBAEL,UAAUgD,IAAgB,aAAa;oBACvC,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,aAAa;gBAAA;YACf;YAKJjL,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gBACC,WAAW0L;gBACX,OAAO3K;gBACP,qBAAmB3C,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;gBAC1B,yBAAuB1J,EAAO,QAAA;gBAC9B,qBAAmBA,EAAO,IAAA;gBAC1B,oBAAkBuV,KAAYpB,KAAU,CAACa,IAAc,SAAS;gBAChE,uBAAqBuB,IAAgB,SAAS;gBAG7C,UAAA;oBAAAvW,EAAO,UAAA,KAAe,CAAA,KACrB+H,aAAAA,GAAAA,EAAAA,GAAAA,CAACgN,IAAA;wBACC,OAAAnF;wBACA,OAAAlG;wBACA,aAAa1J,EAAO,WAAA;wBACpB,aAAAgV;wBACA,UAAUR;wBACV,UAAUxU,EAAO,UAAA,GAAa+W,IAAe,KAAA;wBAC7C,YAAY/W,EAAO,UAAA,IAAc,CAACgV;oBAAA;oBAKrC,CAACA,KACAjN,aAAAA,GAAAA,EAAAA,GAAAA,CAAC8N,IAAA;wBACC,iBAAiBe;wBACjB,aAAa5W,EAAO,WAAA;wBACpB,OAAA0J;wBACA,oBAAoB1J,EAAO,kBAAA;wBAC3B,uBAAAmO;wBACA,WAAU;oBAAA;oBAKb6G,KAAehV,EAAO,WAAA,IACrBsL,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;wBAAI,WAAU;wBACb,UAAA;4BAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;gCACC,SAASyM;gCACT,WAAU;gCACV,OAAM;gCAEN,UAAAzM,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;oCAA2C,MAAK;oCAAO,QAAO;oCAAe,SAAQ;oCAClG,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;oCAAA,CAAe;gCAAA,CACtF;4BAAA;4BAEFA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCAAI,WAAU;gCACZ,UAAA,EAAwB,MAAA;4BAAA,CAC3B;yBAAA;oBAAA,CACF;oBAID,CAACiN,KACAjN,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAU;wBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BAAuD,UAAA;wBAAA,CAEtE;oBAAA,CACF;iBAAA;YAAA;SAEJ;IAAA,CACF;AAEJ,GCjPakP,KAAgF,CAAC,EAC5F,iBAAA3N,CAAAA,EACA,SAAA4N,CAAAA,EACA,OAAAxN,CAAAA,EACA,OAAAkG,IAAQ,oBAAA,EACR,UAAAuH,IAAW,cAAA,EACX,MAAAvK,IAAO,IAAA,EACP,UAAAwK,IAAW,CAAA,CAAA,EACX,WAAAC,IAAY,GAAA,EACZ,uBAAAlJ,CAAAA,EACA,WAAAqC,CAAAA,EACA,WAAApJ,CAAAA,EACF,KAAM;IACJ,MAAM,CAACqJ,GAAWC,CAAY,CAAA,OAAIxL,yKAAAA,EAAS,CAAA,CAAK,GAC1C,CAACyL,GAAaC,CAAc,CAAA,gLAAI1L,EAAS,CAAA,CAAK;QAGpDuC,0KAAAA,EAAU,MAAM;QACd,IAAI2P,KAAY9N,EAAgB,MAAA,GAAS,GAAG;YAC1C,MAAMuH,IAAQ,WAAW,MAAM;gBAC7BH,EAAa,CAAA,CAAI,GACjBE,EAAe,CAAA,CAAI;YAAA,GAClByG,CAAS;YAEZ,OAAO,IAAM,aAAaxG,CAAK;QAAA;IACjC,GACC;QAACuG;QAAU9N,EAAgB,MAAA;QAAQ+N,CAAS;KAAC;IAEhD,MAAMpG,IAAgB,MAAM;QAC1BP,EAAa,CAAA,CAAK,GAClBF,KAAA,QAAAA;IAAY,GAIR8G,IAAsB,MAAM;QAChC,OAAQ1K,GAAA;YACN,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAM,OAAO;YAClB;gBAAS,OAAO;QAAA;IAClB,GAII2K,IAAqB,MAAM;QAC/B,OAAQJ,GAAA;YACN,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAAA;IAClB;IAGF,IAAI,CAAC1G,KAAanH,EAAgB,MAAA,KAAW,GAC3C,OAAO;IAGT,MAAM8C,IAAmBhI,EACvB,qCACA,mDACAmT,EAAA,GACAD,EAAA,GACA;QACE,oCAAoC,CAAC3G;QACrC,uCAAuCA;IAAA,GAEzCvJ,IAGIiF,IAAiB3C,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA;IAE3B,OACE3B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;QACC,WAAWqE;QACX,OAAOC;QACP,qBAAmB3C,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAE1B,UAAA4B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;YAAI,WAAU;YAEb,UAAA;gBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAA;gCAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;oCACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;wCACjE,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;wCAAA,CAA6B;oCAAA,CACpG;gCAAA,CACF;gCAAA,aAAA,GAAA,EAAA,IAAA,CACC,OAAA;oCACC,UAAA;wCAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;4CAAG,WAAU;4CAAyB,UAAA6H;wCAAAA,CAAM;wCAC5CsH,KACC5L,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,KAAA;4CAAE,WAAU;4CAA0C,UAAA;gDAAA;gDACzC4L;gDAAQ;6CAAA;wCAAA,CACtB;qCAAA;gCAAA,CAEJ;6BAAA;wBAAA,CACF;wBACAnP,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;4BACC,SAASkJ;4BACT,WAAU;4BACV,cAAW;4BAEX,UAAAlJ,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;gCACjE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;gCAAA,CAAuB;4BAAA,CAC9F;wBAAA;qBACF;gBAAA,CACF;gBAGAuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBAEb,UAAA;wBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAA;gCAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;gCAAA,CAAkD;gCACjEuD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;oCAAK,WAAU;oCACb,UAAA;wCAAAhC,EAAgB,MAAA;wCAAO;wCAAmBA,EAAgB,MAAA,GAAS,IAAI,OAAO;wCAAG;qCAAA;gCAAA,CACpF;6BAAA;wBAAA,CACF;wBAGAvB,aAAAA,GAAAA,EAAAA,GAAAA,CAACuI,IAAA;4BACC,iBAAAhH;4BACA,QAAQ;gCACN,aAAa;gCACb,SAAS;gCACT,oBAAoB;gCACpB,eAAe,CAAA;gCACf,UAAU,CAAA;gCACV,SAAS;4BAAA;4BAEX,OAAAI;4BACA,uBAAAyE;wBAAA;qBACF;gBAAA,CACF;gBAAA,aAAA,GAAA,EAAA,GAAA,CAGC,OAAA;oBAAI,WAAU;oBACb,UAAA7C,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;wBAAI,WAAU;wBACb,UAAA;4BAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;gCAAK,WAAU;gCAA2C,UAAA;4BAAA,CAE3D;4BACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;gCACC,SAASkJ;gCACT,WAAU;gCACX,UAAA;4BAAA;yBAED;oBAAA,CACF;gBAAA,CACF;aAAA;QAAA,CACF;IAAA;AAGN,GClKauG,IAAoB,CAACC,IAAoC,CAAA,CAAA,KAAoB;IACxF,MAAMC,IAAyB;QAC7B,MAAM;QACN,cAAc;QACd,gBAAgB;QAChB,aAAa;QACb,iBAAiB;QACjB,cAAc;QACd,aAAa;QACb,WAAW;QACX,oBAAoB;QACpB,YAAY;QACZ,UAAU;YACR,OAAO;YACP,MAAM;YACN,OAAO;YACP,OAAO;QAAA;QAET,cAAc;QACd,SAAS;YACP,OAAO;YACP,QAAQ;YACR,OAAO;QAAA;QAET,SAAS;YACP,OAAO;YACP,QAAQ;YACR,OAAO;QAAA;QAET,OAAO;YACL,YAAY;YACZ,cAAc;YACd,UAAU;YACV,WAAW;YACX,WAAW;QAAA;IACb;IAGF,OAAO;QACL,GAAGA,CAAAA;QACH,GAAGD,CAAAA;QACH,UAAU;YACR,GAAGC,EAAU,QAAA;YACb,GAAGD,EAAY,QAAA;QAAA;QAEjB,SAAS;YACP,GAAGC,EAAU,OAAA;YACb,GAAGD,EAAY,OAAA;QAAA;QAEjB,SAAS;YACP,GAAGC,EAAU,OAAA;YACb,GAAGD,EAAY,OAAA;QAAA;QAEjB,OAAO;YACL,GAAGC,EAAU,KAAA;YACb,GAAGD,EAAY,KAAA;QAAA;QAEjB,YAAY;YACV,GAAGC,EAAU,UAAA;YACb,GAAGD,EAAY,UAAA;QAAA;IACjB;AAEJ,GAKaE,KAAkB,CAACF,IAAoC,CAAA,CAAA,GAe3DD,EAAkB;QACvB,GAfyC;YACzC,MAAM;YACN,iBAAiB;YACjB,cAAc;YACd,aAAa;YACb,WAAW;YACX,oBAAoB;YACpB,SAAS;gBACP,OAAO;gBACP,QAAQ;gBACR,OAAO;YAAA;QACT,CAAA;QAKA,GAAGC,CAAAA;IAAA,CACJ,GAMUG,KAAe;IAAA,uBAAA;IAE1B,SAASJ,EAAkB;QACzB,cAAc;QACd,gBAAgB;QAChB,cAAc;QACd,SAAS;YACP,OAAO;YACP,QAAQ;YACR,OAAO;QAAA;IACT,CACD;IAAA,yBAAA;IAGD,SAASA,EAAkB;QACzB,cAAc;QACd,gBAAgB;QAChB,aAAa;QACb,cAAc;QACd,WAAW;YACT,SAAS;YACT,WAAW;YACX,QAAQ;QAAA;IACV,CACD;IAAA,gCAAA;IAGD,WAAWA,EAAkB;QAC3B,cAAc;QACd,gBAAgB;QAChB,iBAAiB;QACjB,cAAc;QACd,aAAa;QACb,cAAc;QACd,YAAY;IAAA,CACb;IAAA,wCAAA;IAGD,cAAcA,EAAkB;QAC9B,cAAc;QACd,gBAAgB;QAChB,iBAAiB;QACjB,cAAc;QACd,aAAa;QACb,WAAW;QACX,oBAAoB;QACpB,cAAc;QACd,SAAS;YACP,OAAO;YACP,QAAQ;YACR,OAAO;QAAA;IACT,CACD;AACH,GAKaK,KAAc,CAAA,GAAIC,MAAgD;IAC7E,MAAMJ,IAAYF,EAAA;IAClB,OAAOM,EAAO,MAAA,CAAO,CAACC,GAAQrO,IACvBA,IACE8N,EAAkB;YACvB,GAAGO,CAAAA;YACH,GAAGrO,CAAAA;QAAA,CACJ,IAJkBqO,GAKlBL,CAAS;AACd,GAKaM,KAAyB,CAACC,MAA+C;;IACpF,MAAMC,IAAgB,iBAAiBD,CAAO;IAE9C,OAAO;QACL,cAAA,CAAA,CAAc/N,IAAAgO,EAAc,gBAAA,CAAiB,wBAAwB,CAAA,KAAvD,OAAA,KAAA,IAAAhO,EAA0D,IAAA,EAAA,KAAU,KAAA;QAClF,gBAAA,CAAA,CAAgBW,IAAAqN,EAAc,gBAAA,CAAiB,0BAA0B,CAAA,KAAzD,OAAA,KAAA,IAAArN,EAA4D,IAAA,EAAA,KAAU,KAAA;QACtF,iBAAA,CAAA,CAAiBC,IAAAoN,EAAc,gBAAA,CAAiB,2BAA2B,CAAA,KAA1D,OAAA,KAAA,IAAApN,EAA6D,IAAA,EAAA,KAAU,KAAA;QACxF,WAAA,CAAA,CAAWC,IAAAmN,EAAc,gBAAA,CAAiB,qBAAqB,CAAA,KAApD,OAAA,KAAA,IAAAnN,EAAuD,IAAA,EAAA,KAAU,KAAA;QAC5E,cAAA,CAAA,CAAcC,IAAAkN,EAAc,gBAAA,CAAiB,wBAAwB,CAAA,KAAvD,OAAA,KAAA,IAAAlN,EAA0D,IAAA,EAAA,KAAU,KAAA;QAClF,YAAA,CAAA,CAAYC,IAAAiN,EAAc,gBAAA,CAAiB,sBAAsB,CAAA,KAArD,OAAA,KAAA,IAAAjN,EAAwD,IAAA,EAAA,KAAU,KAAA;IAAA;AAElF,GC5EakN,KAAU,SAGVC,KAAiB;IAC5B,iBAAiB,CAAA;IACjB,OAAO,CAAA;IACP,OAAO;QACL,MAAM;QACN,aAAa;IAAA;AAEjB", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26], "debugId": null}}]}