{"version": 3, "file": "index.js", "sources": ["../../node_modules/react/cjs/react-jsx-runtime.production.js", "../../node_modules/react/cjs/react-jsx-runtime.development.js", "../../node_modules/react/jsx-runtime.js", "../../node_modules/classnames/index.js", "../src/hooks/useAdMeshTracker.ts", "../src/components/AdMeshLinkTracker.tsx", "../src/hooks/useAdMeshStyles.ts", "../src/utils/disclosureUtils.ts", "../src/components/AdMeshProductCard.tsx", "../src/components/AdMeshCompareTable.tsx", "../src/components/AdMeshBadge.tsx", "../src/components/AdMeshExpandableUnit.tsx", "../src/components/AdMeshInlineRecommendation.tsx", "../src/components/AdMeshConversationSummary.tsx", "../src/components/AdMeshCitationReference.tsx", "../src/components/AdMeshCitationUnit.tsx", "../src/components/AdMeshConversationalUnit.tsx", "../src/components/AdMeshChatMessage.tsx", "../src/components/AdMeshChatInput.tsx", "../src/components/AdMeshChatInterface.tsx", "../src/components/AdMeshFloatingChat.tsx", "../src/components/AdMeshSidebarHeader.tsx", "../src/components/AdMeshSidebarContent.tsx", "../src/components/AdMeshSidebar.tsx", "../src/components/AdMeshAutoRecommendationWidget.tsx", "../src/utils/themeUtils.ts", "../src/index.ts"], "sourcesContent": ["/**\n * @license React\n * react-jsx-runtime.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\nvar REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n  REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\");\nfunction jsxProd(type, config, maybeKey) {\n  var key = null;\n  void 0 !== maybeKey && (key = \"\" + maybeKey);\n  void 0 !== config.key && (key = \"\" + config.key);\n  if (\"key\" in config) {\n    maybeKey = {};\n    for (var propName in config)\n      \"key\" !== propName && (maybeKey[propName] = config[propName]);\n  } else maybeKey = config;\n  config = maybeKey.ref;\n  return {\n    $$typeof: REACT_ELEMENT_TYPE,\n    type: type,\n    key: key,\n    ref: void 0 !== config ? config : null,\n    props: maybeKey\n  };\n}\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsx = jsxProd;\nexports.jsxs = jsxProd;\n", "/**\n * @license React\n * react-jsx-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsx = function (type, config, maybeKey, source, self) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        !1,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n    exports.jsxs = function (type, config, maybeKey, source, self) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        !0,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-runtime.development.js');\n}\n", "/*!\n\tCopyright (c) 2018 <PERSON>.\n\tLicensed under the MIT License (MIT), see\n\thttp://jedwatson.github.io/classnames\n*/\n/* global define */\n\n(function () {\n\t'use strict';\n\n\tvar hasOwn = {}.hasOwnProperty;\n\n\tfunction classNames () {\n\t\tvar classes = '';\n\n\t\tfor (var i = 0; i < arguments.length; i++) {\n\t\t\tvar arg = arguments[i];\n\t\t\tif (arg) {\n\t\t\t\tclasses = appendClass(classes, parseValue(arg));\n\t\t\t}\n\t\t}\n\n\t\treturn classes;\n\t}\n\n\tfunction parseValue (arg) {\n\t\tif (typeof arg === 'string' || typeof arg === 'number') {\n\t\t\treturn arg;\n\t\t}\n\n\t\tif (typeof arg !== 'object') {\n\t\t\treturn '';\n\t\t}\n\n\t\tif (Array.isArray(arg)) {\n\t\t\treturn classNames.apply(null, arg);\n\t\t}\n\n\t\tif (arg.toString !== Object.prototype.toString && !arg.toString.toString().includes('[native code]')) {\n\t\t\treturn arg.toString();\n\t\t}\n\n\t\tvar classes = '';\n\n\t\tfor (var key in arg) {\n\t\t\tif (hasOwn.call(arg, key) && arg[key]) {\n\t\t\t\tclasses = appendClass(classes, key);\n\t\t\t}\n\t\t}\n\n\t\treturn classes;\n\t}\n\n\tfunction appendClass (value, newClass) {\n\t\tif (!newClass) {\n\t\t\treturn value;\n\t\t}\n\t\n\t\tif (value) {\n\t\t\treturn value + ' ' + newClass;\n\t\t}\n\t\n\t\treturn value + newClass;\n\t}\n\n\tif (typeof module !== 'undefined' && module.exports) {\n\t\tclassNames.default = classNames;\n\t\tmodule.exports = classNames;\n\t} else if (typeof define === 'function' && typeof define.amd === 'object' && define.amd) {\n\t\t// register as 'classnames', consistent with npm package name\n\t\tdefine('classnames', [], function () {\n\t\t\treturn classNames;\n\t\t});\n\t} else {\n\t\twindow.classNames = classNames;\n\t}\n}());\n", "import { useState, useCallback, useMemo } from 'react';\nimport type { TrackingData, UseAdMeshTrackerReturn } from '../types/index';\n\n// Default tracking endpoint - can be overridden via config\nconst DEFAULT_TRACKING_URL = 'https://api.useadmesh.com/track';\n\ninterface TrackingConfig {\n  apiBaseUrl?: string;\n  enabled?: boolean;\n  debug?: boolean;\n  retryAttempts?: number;\n  retryDelay?: number;\n}\n\n// Global config that can be set by the consuming application\nlet globalConfig: TrackingConfig = {\n  apiBaseUrl: DEFAULT_TRACKING_URL,\n  enabled: true,\n  debug: false,\n  retryAttempts: 3,\n  retryDelay: 1000\n};\n\nexport const setAdMeshTrackerConfig = (config: Partial<TrackingConfig>) => {\n  globalConfig = { ...globalConfig, ...config };\n};\n\nexport const useAdMeshTracker = (config?: Partial<TrackingConfig>): UseAdMeshTrackerReturn => {\n  const [isTracking, setIsTracking] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const mergedConfig = useMemo(() => ({ ...globalConfig, ...config }), [config]);\n\n  const log = useCallback((message: string, data?: unknown) => {\n    if (mergedConfig.debug) {\n      console.log(`[AdMesh Tracker] ${message}`, data);\n    }\n  }, [mergedConfig.debug]);\n\n  const sendTrackingEvent = useCallback(async (\n    eventType: 'click' | 'view' | 'conversion',\n    data: TrackingData\n  ): Promise<void> => {\n    if (!mergedConfig.enabled) {\n      log('Tracking disabled, skipping event', { eventType, data });\n      return;\n    }\n\n    if (!data.adId || !data.admeshLink) {\n      const errorMsg = 'Missing required tracking data: adId and admeshLink are required';\n      log(errorMsg, data);\n      setError(errorMsg);\n      return;\n    }\n\n    setIsTracking(true);\n    setError(null);\n\n    const payload = {\n      event_type: eventType,\n      ad_id: data.adId,\n      admesh_link: data.admeshLink,\n      product_id: data.productId,\n      user_id: data.userId,\n      session_id: data.sessionId,\n      revenue: data.revenue,\n      conversion_type: data.conversionType,\n      metadata: data.metadata,\n      timestamp: new Date().toISOString(),\n      user_agent: navigator.userAgent,\n      referrer: document.referrer,\n      page_url: window.location.href\n    };\n\n    log(`Sending ${eventType} event`, payload);\n\n    let lastError: Error | null = null;\n    \n    for (let attempt = 1; attempt <= (mergedConfig.retryAttempts || 3); attempt++) {\n      try {\n        const response = await fetch(`${mergedConfig.apiBaseUrl}/events`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify(payload),\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n\n        const result = await response.json();\n        log(`${eventType} event tracked successfully`, result);\n        setIsTracking(false);\n        return;\n\n      } catch (err) {\n        lastError = err as Error;\n        log(`Attempt ${attempt} failed for ${eventType} event`, err);\n        \n        if (attempt < (mergedConfig.retryAttempts || 3)) {\n          await new Promise(resolve => \n            setTimeout(resolve, (mergedConfig.retryDelay || 1000) * attempt)\n          );\n        }\n      }\n    }\n\n    // All attempts failed\n    const errorMsg = `Failed to track ${eventType} event after ${mergedConfig.retryAttempts} attempts: ${lastError?.message}`;\n    log(errorMsg, lastError);\n    setError(errorMsg);\n    setIsTracking(false);\n  }, [mergedConfig, log]);\n\n  const trackClick = useCallback(async (data: TrackingData): Promise<void> => {\n    return sendTrackingEvent('click', data);\n  }, [sendTrackingEvent]);\n\n  const trackView = useCallback(async (data: TrackingData): Promise<void> => {\n    return sendTrackingEvent('view', data);\n  }, [sendTrackingEvent]);\n\n  const trackConversion = useCallback(async (data: TrackingData): Promise<void> => {\n    if (!data.revenue && !data.conversionType) {\n      log('Warning: Conversion tracking without revenue or conversion type', data);\n    }\n    return sendTrackingEvent('conversion', data);\n  }, [sendTrackingEvent, log]);\n\n  return {\n    trackClick,\n    trackView,\n    trackConversion,\n    isTracking,\n    error\n  };\n};\n\n// Utility function to build admesh_link with tracking parameters\nexport const buildAdMeshLink = (\n  baseLink: string, \n  adId: string, \n  additionalParams?: Record<string, string>\n): string => {\n  try {\n    const url = new URL(baseLink);\n    url.searchParams.set('ad_id', adId);\n    url.searchParams.set('utm_source', 'admesh');\n    url.searchParams.set('utm_medium', 'recommendation');\n    \n    if (additionalParams) {\n      Object.entries(additionalParams).forEach(([key, value]) => {\n        url.searchParams.set(key, value);\n      });\n    }\n    \n    return url.toString();\n  } catch (err) {\n    console.warn('[AdMesh] Invalid URL provided to buildAdMeshLink:', baseLink, err);\n    return baseLink;\n  }\n};\n\n// Helper function to extract tracking data from recommendation\nexport const extractTrackingData = (\n  recommendation: { ad_id: string; admesh_link: string; product_id: string },\n  additionalData?: Partial<TrackingData>\n): TrackingData => {\n  return {\n    adId: recommendation.ad_id,\n    admeshLink: recommendation.admesh_link,\n    productId: recommendation.product_id,\n    ...additionalData\n  };\n};\n", "import React, { useCallback, useEffect, useRef } from 'react';\nimport type { AdMeshLinkTrackerProps } from '../types/index';\nimport { useAdMeshTracker } from '../hooks/useAdMeshTracker';\n\nexport const AdMeshLinkTracker: React.FC<AdMeshLinkTrackerProps> = ({\n  adId,\n  admeshLink,\n  productId,\n  children,\n  trackingData,\n  className,\n  style\n}) => {\n  const { trackClick, trackView } = useAdMeshTracker();\n  const elementRef = useRef<HTMLDivElement>(null);\n  const hasTrackedView = useRef(false);\n\n  // Track view when component becomes visible\n  useEffect(() => {\n    if (!elementRef.current || hasTrackedView.current) return;\n\n    const observer = new IntersectionObserver(\n      (entries) => {\n        entries.forEach((entry) => {\n          if (entry.isIntersecting && !hasTrackedView.current) {\n            hasTrackedView.current = true;\n            trackView({\n              adId,\n              admeshLink,\n              productId,\n              ...trackingData\n            }).catch(console.error);\n          }\n        });\n      },\n      {\n        threshold: 0.5, // Track when 50% of the element is visible\n        rootMargin: '0px'\n      }\n    );\n\n    observer.observe(elementRef.current);\n\n    return () => {\n      observer.disconnect();\n    };\n  }, [adId, admeshLink, productId, trackingData, trackView]);\n\n  const handleClick = useCallback(async (event: React.MouseEvent) => {\n    // Track the click\n    try {\n      await trackClick({\n        adId,\n        admeshLink,\n        productId,\n        ...trackingData\n      });\n    } catch (error) {\n      console.error('Failed to track click:', error);\n    }\n\n\n\n    // If the children contain a link, let the browser handle navigation\n    // Otherwise, navigate programmatically\n    const target = event.target as HTMLElement;\n    const link = target.closest('a');\n    \n    if (!link) {\n      // No link found, navigate programmatically\n      window.open(admeshLink, '_blank', 'noopener,noreferrer');\n    }\n    // If there's a link, let the browser handle it naturally\n  }, [adId, admeshLink, productId, trackingData, trackClick]);\n\n  return (\n    <div\n      ref={elementRef}\n      className={className}\n      onClick={handleClick}\n      style={{\n        cursor: 'pointer',\n        ...style\n      }}\n    >\n      {children}\n    </div>\n  );\n};\n\nAdMeshLinkTracker.displayName = 'AdMeshLinkTracker';\n", "import { useEffect } from 'react';\n\n// Complete CSS content as a string - this will be injected automatically\nconst ADMESH_STYLES = `\n/* AdMesh UI SDK - Complete Self-Contained Styles */\n\n/* CSS Reset for AdMesh components */\n.admesh-component, .admesh-component * {\n  box-sizing: border-box;\n}\n\n/* CSS Variables */\n.admesh-component {\n  --admesh-primary: #6366f1;\n  --admesh-primary-hover: #4f46e5;\n  --admesh-secondary: #8b5cf6;\n  --admesh-accent: #06b6d4;\n  --admesh-background: #ffffff;\n  --admesh-surface: #ffffff;\n  --admesh-border: #e2e8f0;\n  --admesh-text: #0f172a;\n  --admesh-text-muted: #64748b;\n  --admesh-text-light: #94a3b8;\n  --admesh-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --admesh-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --admesh-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --admesh-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --admesh-radius: 0.75rem;\n  --admesh-radius-sm: 0.375rem;\n  --admesh-radius-lg: 1rem;\n  --admesh-radius-xl: 1.5rem;\n}\n\n.admesh-component[data-admesh-theme=\"dark\"] {\n  --admesh-background: #111827;\n  --admesh-surface: #1f2937;\n  --admesh-border: #374151;\n  --admesh-text: #f9fafb;\n  --admesh-text-muted: #9ca3af;\n  --admesh-text-light: #6b7280;\n  --admesh-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.3), 0 1px 2px -1px rgb(0 0 0 / 0.3);\n  --admesh-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);\n  --admesh-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);\n  --admesh-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3);\n}\n\n/* Layout Styles */\n.admesh-layout {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif;\n  color: var(--admesh-text);\n  background-color: var(--admesh-background);\n  border-radius: var(--admesh-radius);\n  padding: 1.5rem;\n  box-shadow: var(--admesh-shadow);\n  border: 1px solid var(--admesh-border);\n}\n\n.admesh-layout__header {\n  margin-bottom: 1.5rem;\n  text-align: center;\n}\n\n.admesh-layout__title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: var(--admesh-text);\n  margin-bottom: 0.5rem;\n}\n\n.admesh-layout__subtitle {\n  font-size: 0.875rem;\n  color: var(--admesh-text-muted);\n}\n\n.admesh-layout__cards-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n}\n\n.admesh-layout__more-indicator {\n  text-align: center;\n  padding: 1rem;\n  color: var(--admesh-text-muted);\n  font-size: 0.875rem;\n}\n\n.admesh-layout__empty {\n  text-align: center;\n  padding: 3rem 1rem;\n}\n\n.admesh-layout__empty-content h3 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: var(--admesh-text-muted);\n  margin-bottom: 0.5rem;\n}\n\n.admesh-layout__empty-content p {\n  font-size: 0.875rem;\n  color: var(--admesh-text-muted);\n}\n\n/* Product Card Styles */\n.admesh-product-card {\n  background-color: var(--admesh-surface);\n  border: 1px solid var(--admesh-border);\n  border-radius: var(--admesh-radius);\n  padding: 1.5rem;\n  transition: all 0.2s ease-in-out;\n  position: relative;\n  overflow: hidden;\n}\n\n.admesh-product-card:hover {\n  box-shadow: var(--admesh-shadow-lg);\n  transform: translateY(-2px);\n  border-color: var(--admesh-primary);\n}\n\n.admesh-product-card__header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 1rem;\n}\n\n.admesh-product-card__title {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: var(--admesh-text);\n  margin-bottom: 0.5rem;\n  line-height: 1.4;\n}\n\n.admesh-product-card__reason {\n  font-size: 0.875rem;\n  color: var(--admesh-text-muted);\n  line-height: 1.5;\n  margin-bottom: 1rem;\n}\n\n.admesh-product-card__match-score {\n  margin-bottom: 1rem;\n}\n\n.admesh-product-card__match-score-label {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 0.75rem;\n  color: var(--admesh-text-muted);\n  margin-bottom: 0.25rem;\n}\n\n.admesh-product-card__match-score-bar {\n  width: 100%;\n  height: 0.375rem;\n  background-color: var(--admesh-border);\n  border-radius: var(--admesh-radius-sm);\n  overflow: hidden;\n}\n\n.admesh-product-card__match-score-fill {\n  height: 100%;\n  background: linear-gradient(90deg, var(--admesh-primary), #8b5cf6);\n  border-radius: var(--admesh-radius-sm);\n  transition: width 0.3s ease-in-out;\n}\n\n.admesh-product-card__badges {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.5rem;\n  margin-bottom: 1rem;\n}\n\n.admesh-product-card__badge {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.25rem;\n  padding: 0.25rem 0.5rem;\n  background-color: var(--admesh-primary);\n  color: white;\n  font-size: 0.75rem;\n  font-weight: 500;\n  border-radius: var(--admesh-radius-sm);\n}\n\n.admesh-product-card__badge--secondary {\n  background-color: var(--admesh-secondary);\n}\n\n.admesh-product-card__keywords {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.25rem;\n  margin-bottom: 1rem;\n}\n\n.admesh-product-card__keyword {\n  padding: 0.125rem 0.375rem;\n  background-color: var(--admesh-border);\n  color: var(--admesh-text-muted);\n  font-size: 0.75rem;\n  border-radius: var(--admesh-radius-sm);\n}\n\n/* Dark mode specific enhancements */\n.admesh-component[data-admesh-theme=\"dark\"] .admesh-product-card__keyword {\n  background-color: #4b5563;\n  color: #d1d5db;\n}\n\n.admesh-component[data-admesh-theme=\"dark\"] .admesh-product-card:hover {\n  border-color: var(--admesh-primary);\n  background-color: #374151;\n}\n\n.admesh-component[data-admesh-theme=\"dark\"] .admesh-product-card__button:hover {\n  background: linear-gradient(90deg, var(--admesh-primary-hover), var(--admesh-primary));\n}\n\n.admesh-product-card__footer {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 1.5rem;\n}\n\n/* Mobile-specific sidebar improvements */\n@media (max-width: 640px) {\n  .admesh-sidebar {\n    /* Ensure proper mobile viewport handling */\n    height: 100vh !important;\n    height: 100dvh !important; /* Dynamic viewport height for mobile browsers */\n    max-height: 100vh !important;\n    max-height: 100dvh !important;\n    width: 100vw !important;\n    max-width: 90vw !important;\n    overflow: hidden !important;\n  }\n\n  .admesh-sidebar.relative {\n    height: 100% !important;\n    width: 100% !important;\n    max-width: 100% !important;\n  }\n\n  /* Improve touch scrolling */\n  .admesh-sidebar .overflow-y-auto {\n    -webkit-overflow-scrolling: touch !important;\n    overscroll-behavior: contain !important;\n    scroll-behavior: smooth !important;\n  }\n\n  /* Prevent body scroll when sidebar is open */\n  body:has(.admesh-sidebar[data-mobile-open=\"true\"]) {\n    overflow: hidden !important;\n    position: fixed !important;\n    width: 100% !important;\n  }\n}\n\n/* Tablet improvements */\n@media (min-width: 641px) and (max-width: 1024px) {\n  .admesh-sidebar {\n    max-width: 400px !important;\n  }\n}\n\n/* Mobile responsiveness improvements for all components */\n@media (max-width: 640px) {\n  /* Product cards mobile optimization */\n  .admesh-card {\n    padding: 0.75rem !important;\n    margin-bottom: 0.75rem !important;\n  }\n\n  /* Inline recommendations mobile optimization */\n  .admesh-inline-recommendation {\n    padding: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n\n  /* Conversation summary mobile optimization */\n  .admesh-conversation-summary {\n    padding: 1rem !important;\n  }\n\n  /* Percentage text mobile improvements */\n  .admesh-component .text-xs {\n    font-size: 0.75rem !important;\n    line-height: 1rem !important;\n  }\n\n  .admesh-component .text-sm {\n    font-size: 0.875rem !important;\n    line-height: 1.25rem !important;\n  }\n\n  /* Button mobile improvements */\n  .admesh-component button {\n    padding: 0.375rem 0.75rem !important;\n    font-size: 0.75rem !important;\n    min-height: 2rem !important;\n    touch-action: manipulation !important;\n  }\n\n  /* Badge mobile improvements */\n  .admesh-component .rounded-full {\n    padding: 0.25rem 0.5rem !important;\n    font-size: 0.625rem !important;\n    line-height: 1rem !important;\n  }\n\n  /* Progress bar mobile improvements */\n  .admesh-component .bg-gray-200,\n  .admesh-component .bg-slate-600 {\n    height: 0.25rem !important;\n  }\n\n  /* Flex layout mobile improvements */\n  .admesh-component .flex {\n    flex-wrap: wrap !important;\n  }\n\n  .admesh-component .gap-2 {\n    gap: 0.375rem !important;\n  }\n\n  .admesh-component .gap-3 {\n    gap: 0.5rem !important;\n  }\n}\n\n.admesh-product-card__button {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1.5rem;\n  background: linear-gradient(90deg, var(--admesh-primary), var(--admesh-primary-hover));\n  color: white;\n  font-size: 0.875rem;\n  font-weight: 500;\n  border: none;\n  border-radius: var(--admesh-radius);\n  cursor: pointer;\n  transition: all 0.2s ease-in-out;\n  text-decoration: none;\n}\n\n.admesh-product-card__button:hover {\n  transform: translateY(-1px);\n  box-shadow: var(--admesh-shadow-lg);\n}\n\n/* Utility Classes */\n.admesh-text-xs { font-size: 0.75rem; }\n.admesh-text-sm { font-size: 0.875rem; }\n.admesh-text-base { font-size: 1rem; }\n.admesh-text-lg { font-size: 1.125rem; }\n.admesh-text-xl { font-size: 1.25rem; }\n\n.admesh-font-medium { font-weight: 500; }\n.admesh-font-semibold { font-weight: 600; }\n.admesh-font-bold { font-weight: 700; }\n\n.admesh-text-muted { color: var(--admesh-text-muted); }\n\n/* Comparison Table Styles */\n.admesh-compare-table {\n  width: 100%;\n  border-collapse: collapse;\n  background-color: var(--admesh-surface);\n  border: 1px solid var(--admesh-border);\n  border-radius: var(--admesh-radius);\n  overflow: hidden;\n}\n\n.admesh-compare-table th,\n.admesh-compare-table td {\n  padding: 0.75rem;\n  text-align: left;\n  border-bottom: 1px solid var(--admesh-border);\n}\n\n.admesh-compare-table th {\n  background-color: var(--admesh-background);\n  font-weight: 600;\n  color: var(--admesh-text);\n  font-size: 0.875rem;\n}\n\n.admesh-compare-table td {\n  color: var(--admesh-text);\n  font-size: 0.875rem;\n}\n\n.admesh-compare-table tr:hover {\n  background-color: var(--admesh-border);\n}\n\n/* Dark mode table enhancements */\n.admesh-component[data-admesh-theme=\"dark\"] .admesh-compare-table th {\n  background-color: #374151;\n}\n\n.admesh-component[data-admesh-theme=\"dark\"] .admesh-compare-table tr:hover {\n  background-color: #4b5563;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .admesh-layout {\n    padding: 1rem;\n  }\n\n  .admesh-layout__cards-grid {\n    grid-template-columns: 1fr;\n    gap: 0.75rem;\n  }\n\n  .admesh-product-card {\n    padding: 1rem;\n  }\n\n  .admesh-compare-table {\n    font-size: 0.75rem;\n  }\n\n  .admesh-compare-table th,\n  .admesh-compare-table td {\n    padding: 0.5rem;\n  }\n}\n\n/* Essential Utility Classes for Self-Contained SDK - High Specificity */\n.admesh-component .relative { position: relative !important; }\n.admesh-component .absolute { position: absolute !important; }\n.admesh-component .flex { display: flex !important; }\n.admesh-component .inline-flex { display: inline-flex !important; }\n.admesh-component .grid { display: grid !important; }\n.admesh-component .hidden { display: none !important; }\n.admesh-component .block { display: block !important; }\n.admesh-component .inline-block { display: inline-block !important; }\n\n/* Flexbox utilities */\n.admesh-component .flex-col { flex-direction: column !important; }\n.admesh-component .flex-row { flex-direction: row !important; }\n.admesh-component .flex-wrap { flex-wrap: wrap !important; }\n.admesh-component .items-center { align-items: center !important; }\n.admesh-component .items-start { align-items: flex-start !important; }\n.admesh-component .items-end { align-items: flex-end !important; }\n.admesh-component .justify-center { justify-content: center !important; }\n.admesh-component .justify-between { justify-content: space-between !important; }\n.admesh-component .justify-end { justify-content: flex-end !important; }\n.admesh-component .flex-1 { flex: 1 1 0% !important; }\n.admesh-component .flex-shrink-0 { flex-shrink: 0 !important; }\n\n/* Grid utilities */\n.admesh-component .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }\n.admesh-component .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }\n.admesh-component .grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }\n\n/* Spacing utilities */\n.admesh-component .gap-1 { gap: 0.25rem; }\n.admesh-component .gap-2 { gap: 0.5rem; }\n.admesh-component .gap-3 { gap: 0.75rem; }\n.admesh-component .gap-4 { gap: 1rem; }\n.admesh-component .gap-6 { gap: 1.5rem; }\n.admesh-component .gap-8 { gap: 2rem; }\n\n/* Padding utilities */\n.admesh-component .p-1 { padding: 0.25rem; }\n.admesh-component .p-2 { padding: 0.5rem; }\n.admesh-component .p-3 { padding: 0.75rem; }\n.admesh-component .p-4 { padding: 1rem; }\n.admesh-component .p-5 { padding: 1.25rem; }\n.admesh-component .p-6 { padding: 1.5rem; }\n.admesh-component .px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }\n.admesh-component .px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }\n.admesh-component .px-4 { padding-left: 1rem; padding-right: 1rem; }\n.admesh-component .py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }\n.admesh-component .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }\n.admesh-component .py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }\n.admesh-component .pt-2 { padding-top: 0.5rem; }\n.admesh-component .pt-3 { padding-top: 0.75rem; }\n.admesh-component .pb-2 { padding-bottom: 0.5rem; }\n.admesh-component .pb-3 { padding-bottom: 0.75rem; }\n\n/* Margin utilities */\n.admesh-component .m-0 { margin: 0; }\n.admesh-component .mb-1 { margin-bottom: 0.25rem; }\n.admesh-component .mb-2 { margin-bottom: 0.5rem; }\n.admesh-component .mb-3 { margin-bottom: 0.75rem; }\n.admesh-component .mb-4 { margin-bottom: 1rem; }\n.admesh-component .mb-6 { margin-bottom: 1.5rem; }\n.admesh-component .mt-1 { margin-top: 0.25rem; }\n.admesh-component .mt-2 { margin-top: 0.5rem; }\n.admesh-component .mt-4 { margin-top: 1rem; }\n.admesh-component .mt-6 { margin-top: 1.5rem; }\n.admesh-component .mt-auto { margin-top: auto; }\n.admesh-component .ml-1 { margin-left: 0.25rem; }\n.admesh-component .mr-1 { margin-right: 0.25rem; }\n.admesh-component .mr-2 { margin-right: 0.5rem; }\n\n/* Width and height utilities */\n.admesh-component .w-2 { width: 0.5rem; }\n.admesh-component .w-3 { width: 0.75rem; }\n.admesh-component .w-4 { width: 1rem; }\n.admesh-component .w-5 { width: 1.25rem; }\n.admesh-component .w-6 { width: 1.5rem; }\n.admesh-component .w-full { width: 100%; }\n.admesh-component .w-fit { width: fit-content; }\n.admesh-component .h-2 { height: 0.5rem; }\n.admesh-component .h-3 { height: 0.75rem; }\n.admesh-component .h-4 { height: 1rem; }\n.admesh-component .h-5 { height: 1.25rem; }\n.admesh-component .h-6 { height: 1.5rem; }\n.admesh-component .h-full { height: 100%; }\n.admesh-component .min-w-0 { min-width: 0px; }\n\n/* Border utilities */\n.admesh-component .border { border-width: 1px; }\n.admesh-component .border-t { border-top-width: 1px; }\n.admesh-component .border-gray-100 { border-color: #f3f4f6; }\n.admesh-component .border-gray-200 { border-color: #e5e7eb; }\n.admesh-component .border-gray-300 { border-color: #d1d5db; }\n.admesh-component .border-blue-200 { border-color: #bfdbfe; }\n.admesh-component .border-green-200 { border-color: #bbf7d0; }\n\n/* Border radius utilities */\n.admesh-component .rounded { border-radius: 0.25rem !important; }\n.admesh-component .rounded-md { border-radius: 0.375rem !important; }\n.admesh-component .rounded-lg { border-radius: 0.5rem !important; }\n.admesh-component .rounded-xl { border-radius: 0.75rem !important; }\n.admesh-component .rounded-full { border-radius: 9999px !important; }\n\n/* Background utilities */\n.admesh-component .bg-white { background-color: #ffffff; }\n.admesh-component .bg-gray-50 { background-color: #f9fafb; }\n.admesh-component .bg-gray-100 { background-color: #f3f4f6; }\n.admesh-component .bg-blue-50 { background-color: #eff6ff; }\n.admesh-component .bg-blue-100 { background-color: #dbeafe; }\n.admesh-component .bg-green-100 { background-color: #dcfce7; }\n.admesh-component .bg-green-500 { background-color: #22c55e; }\n.admesh-component .bg-blue-500 { background-color: #3b82f6; }\n\n/* Gradients */\n.admesh-component .bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)); }\n.admesh-component .bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }\n.admesh-component .from-white { --tw-gradient-from: #ffffff; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(255, 255, 255, 0)); }\n.admesh-component .to-gray-50 { --tw-gradient-to: #f9fafb; }\n.admesh-component .from-purple-500 { --tw-gradient-from: #a855f7; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(168, 85, 247, 0)); }\n.admesh-component .to-pink-500 { --tw-gradient-to: #ec4899; }\n.admesh-component .from-green-400 { --tw-gradient-from: #4ade80; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(74, 222, 128, 0)); }\n.admesh-component .to-blue-500 { --tw-gradient-to: #3b82f6; }\n\n/* Text utilities */\n.admesh-component .text-xs { font-size: 0.75rem; line-height: 1rem; }\n.admesh-component .text-sm { font-size: 0.875rem; line-height: 1.25rem; }\n.admesh-component .text-base { font-size: 1rem; line-height: 1.5rem; }\n.admesh-component .text-lg { font-size: 1.125rem; line-height: 1.75rem; }\n.admesh-component .text-xl { font-size: 1.25rem; line-height: 1.75rem; }\n.admesh-component .font-medium { font-weight: 500; }\n.admesh-component .font-semibold { font-weight: 600; }\n.admesh-component .font-bold { font-weight: 700; }\n.admesh-component .leading-relaxed { line-height: 1.625; }\n\n/* Text colors */\n.admesh-component .text-white { color: #ffffff; }\n.admesh-component .text-gray-400 { color: #9ca3af; }\n.admesh-component .text-gray-500 { color: #6b7280; }\n.admesh-component .text-gray-600 { color: #4b5563; }\n.admesh-component .text-gray-700 { color: #374151; }\n.admesh-component .text-gray-800 { color: #1f2937; }\n.admesh-component .text-blue-600 { color: #2563eb; }\n.admesh-component .text-blue-700 { color: #1d4ed8; }\n.admesh-component .text-green-700 { color: #15803d; }\n\n/* Shadow utilities */\n.admesh-component .shadow-sm { box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05); }\n.admesh-component .shadow { box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1); }\n.admesh-component .shadow-md { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); }\n.admesh-component .shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); }\n.admesh-component .shadow-xl { box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1); }\n\n/* Transition utilities */\n.admesh-component .transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }\n.admesh-component .transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }\n.admesh-component .duration-200 { transition-duration: 200ms; }\n.admesh-component .duration-300 { transition-duration: 300ms; }\n\n/* Transform utilities */\n.admesh-component .hover\\\\:-translate-y-1:hover { transform: translateY(-0.25rem); }\n.admesh-component .hover\\\\:scale-105:hover { transform: scale(1.05); }\n\n/* Hover utilities */\n.admesh-component .hover\\\\:shadow-xl:hover { box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1); }\n.admesh-component .hover\\\\:bg-gray-100:hover { background-color: #f3f4f6; }\n.admesh-component .hover\\\\:text-blue-800:hover { color: #1e40af; }\n\n/* Cursor utilities */\n.admesh-component .cursor-pointer { cursor: pointer; }\n\n/* Overflow utilities */\n.admesh-component .overflow-hidden { overflow: hidden; }\n.admesh-component .truncate { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }\n\n/* Text decoration */\n.admesh-component .underline { text-decoration-line: underline; }\n\n/* Whitespace */\n.admesh-component .whitespace-nowrap { white-space: nowrap; }\n\n/* Dark mode utilities */\n@media (prefers-color-scheme: dark) {\n  .admesh-component .dark\\\\:bg-slate-800 { background-color: #1e293b; }\n  .admesh-component .dark\\\\:bg-slate-900 { background-color: #0f172a; }\n  .admesh-component .dark\\\\:border-slate-700 { border-color: #334155; }\n  .admesh-component .dark\\\\:text-white { color: #ffffff; }\n  .admesh-component .dark\\\\:text-gray-200 { color: #e5e7eb; }\n  .admesh-component .dark\\\\:text-gray-300 { color: #d1d5db; }\n  .admesh-component .dark\\\\:text-gray-400 { color: #9ca3af; }\n  .admesh-component .dark\\\\:text-blue-400 { color: #60a5fa; }\n}\n\n/* Responsive utilities */\n@media (min-width: 640px) {\n  .admesh-component .sm\\\\:p-5 { padding: 1.25rem; }\n  .admesh-component .sm\\\\:text-base { font-size: 1rem; line-height: 1.5rem; }\n  .admesh-component .sm\\\\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }\n  .admesh-component .sm\\\\:flex-row { flex-direction: row; }\n  .admesh-component .sm\\\\:items-center { align-items: center; }\n  .admesh-component .sm\\\\:justify-between { justify-content: space-between; }\n}\n\n@media (min-width: 768px) {\n  .admesh-component .md\\\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }\n}\n\n@media (min-width: 1024px) {\n  .admesh-component .lg\\\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }\n  .admesh-component .lg\\\\:col-span-1 { grid-column: span 1 / span 1; }\n}\n`;\n\nlet stylesInjected = false;\n\nexport const useAdMeshStyles = () => {\n  useEffect(() => {\n    if (stylesInjected) return;\n\n    // Create and inject styles\n    const styleElement = document.createElement('style');\n    styleElement.id = 'admesh-ui-sdk-styles';\n    styleElement.textContent = ADMESH_STYLES;\n    \n    // Check if styles are already injected\n    if (!document.getElementById('admesh-ui-sdk-styles')) {\n      document.head.appendChild(styleElement);\n      stylesInjected = true;\n    }\n\n    // Cleanup function\n    return () => {\n      const existingStyle = document.getElementById('admesh-ui-sdk-styles');\n      if (existingStyle && document.head.contains(existingStyle)) {\n        document.head.removeChild(existingStyle);\n        stylesInjected = false;\n      }\n    };\n  }, []);\n};\n", "import type { AdMeshRecommendation } from '../types/index';\n\n/**\n * Utility functions for generating compliant disclosure labels and tooltips\n */\n\nexport interface DisclosureConfig {\n  showTooltips?: boolean;\n  compactMode?: boolean;\n  customLabels?: {\n    smartPick?: string;\n    partnerMatch?: string;\n    promotedOption?: string;\n    relatedOption?: string;\n  };\n}\n\n/**\n * Generate appropriate label based on match score and recommendation quality\n */\nexport const getRecommendationLabel = (\n  recommendation: AdMeshRecommendation,\n  config: DisclosureConfig = {}\n): string => {\n  const matchScore = recommendation.intent_match_score || 0;\n  const customLabels = config.customLabels || {};\n\n  // High match score (>0.8)\n  if (matchScore >= 0.8) {\n    return customLabels.smartPick || 'Smart Pick';\n  }\n  \n  // Medium match score (0.6-0.8)\n  if (matchScore >= 0.6) {\n    return customLabels.partnerMatch || 'Partner Match';\n  }\n  \n  // Lower match score (<0.6)\n  if (matchScore >= 0.3) {\n    return customLabels.promotedOption || 'Promoted Option';\n  }\n  \n  // Very low match - related option\n  return customLabels.relatedOption || 'Related Option';\n};\n\n/**\n * Generate tooltip text for recommendation labels\n */\nexport const getLabelTooltip = (\n  recommendation: AdMeshRecommendation,\n  _label: string\n): string => {\n  const matchScore = recommendation.intent_match_score || 0;\n\n  if (matchScore >= 0.8) {\n    return \"This recommendation is from a partner who compensates us when you engage. We've matched it to your needs based on your query.\";\n  }\n  \n  if (matchScore >= 0.6) {\n    return \"Top-rated partner solution matched to your specific requirements. Partner compensates us for qualified referrals.\";\n  }\n  \n  if (matchScore >= 0.3) {\n    return \"This partner solution may be relevant to your needs. The partner compensates us when you take qualifying actions.\";\n  }\n  \n  return \"This solution is somewhat related to your query. While not a perfect match, it might still be helpful. This partner compensates us for qualified referrals.\";\n};\n\n/**\n * Generate section-level disclosure text\n */\nexport const getSectionDisclosure = (\n  hasHighMatches: boolean = true,\n  isExpanded: boolean = false\n): string => {\n  if (!hasHighMatches) {\n    return \"Expanded Results: While these don't perfectly match your query, they're related solutions from our partner network. All partners compensate us for referrals.\";\n  }\n  \n  if (isExpanded) {\n    return \"These curated recommendations are from partners who compensate us for referrals.\";\n  }\n  \n  return \"Personalized Partner Recommendations: All results are from vetted partners who compensate us for qualified matches. We've ranked them based on relevance to your specific needs.\";\n};\n\n/**\n * Generate inline disclosure text for product cards\n */\nexport const getInlineDisclosure = (\n  recommendation: AdMeshRecommendation,\n  compact: boolean = false\n): string => {\n  const matchScore = recommendation.intent_match_score || 0;\n\n  if (compact) {\n    return \"Promoted Match\";\n  }\n\n  if (matchScore >= 0.8) {\n    return \"Smart Pick\";\n  }\n\n  if (matchScore >= 0.6) {\n    return \"Partner Match\";\n  }\n\n  return \"Promoted Option\";\n};\n\n/**\n * Generate detailed tooltip for inline disclosures\n */\nexport const getInlineTooltip = (): string => {\n  return \"We've partnered with trusted providers to bring you relevant solutions. These partners compensate us for qualified referrals, which helps us keep our service free.\";\n};\n\n/**\n * Generate badge text without emojis\n */\nexport const getBadgeText = (badgeType: string): string => {\n  const badgeMap: Record<string, string> = {\n    'Top Match': 'Top Match',\n    'Smart Pick': 'Smart Pick',\n    'Perfect Fit': 'Perfect Fit',\n    'Great Match': 'Great Match',\n    'Recommended': 'Recommended',\n    'Good Fit': 'Good Fit',\n    'Featured': 'Featured',\n    'Popular Choice': 'Popular Choice',\n    'Premium Pick': 'Premium Pick',\n    'Free Tier': 'Free Tier',\n    'AI Powered': 'AI Powered',\n    'Popular': 'Popular',\n    'New': 'New',\n    'Trial Available': 'Trial Available',\n    'Related Option': 'Related Option',\n    'Alternative Solution': 'Alternative Solution',\n    'Expanded Match': 'Expanded Match'\n  };\n  \n  return badgeMap[badgeType] || badgeType;\n};\n\n/**\n * Generate appropriate CTA text\n */\nexport const getCtaText = (\n  recommendation: AdMeshRecommendation,\n  context: 'button' | 'link' = 'button'\n): string => {\n  const productName = recommendation.recommendation_title || recommendation.title;\n  \n  if (context === 'link') {\n    return productName;\n  }\n  \n  // For buttons, use action-oriented text\n  if (recommendation.trial_days && recommendation.trial_days > 0) {\n    return `Try ${productName}`;\n  }\n\n  return `Learn More`;\n};\n\n/**\n * Check if recommendations have high match scores\n */\nexport const hasHighQualityMatches = (recommendations: AdMeshRecommendation[]): boolean => {\n  return recommendations.some(rec => (rec.intent_match_score || 0) >= 0.8);\n};\n\n/**\n * Generate compliant powered-by text\n */\nexport const getPoweredByText = (compact: boolean = false): string => {\n  if (compact) {\n    return \"Powered by AdMesh\";\n  }\n  \n  return \"Recommendations powered by AdMesh\";\n};\n", "import React, { useMemo, useState } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshProductCardProps, BadgeType } from '../types/index';\nimport { AdMeshLinkTracker } from './AdMeshLinkTracker';\nimport { useAdMeshStyles } from '../hooks/useAdMeshStyles';\nimport {\n  getRecommendationLabel,\n  getLabelTooltip,\n  getInlineDisclosure,\n  getInlineTooltip,\n  getBadgeText\n} from '../utils/disclosureUtils';\n\nexport const AdMeshProductCard: React.FC<AdMeshProductCardProps> = ({\n  recommendation,\n  theme,\n  showMatchScore = false,\n  showBadges = true,\n  variation = 'default',\n  className,\n  style\n}) => {\n  // Inject styles automatically\n  useAdMeshStyles();\n\n  // State for expandable variations\n  const [isExpanded, setIsExpanded] = useState(false);\n  // Generate badges based on recommendation data using compliant labels\n  const badges = useMemo((): BadgeType[] => {\n    const generatedBadges: BadgeType[] = [];\n\n    // Add primary recommendation label based on match score\n    const primaryLabel = getRecommendationLabel(recommendation);\n    if (primaryLabel === 'Smart Pick') {\n      generatedBadges.push('Top Match'); // Map to existing badge type\n    }\n\n    // Add Trial Available badge\n    if (recommendation.trial_days && recommendation.trial_days > 0) {\n      generatedBadges.push('Trial Available');\n    }\n\n    // Add AI Powered badge (check if AI-related keywords exist)\n    const aiKeywords = ['ai', 'artificial intelligence', 'machine learning', 'ml', 'automation'];\n    const hasAIKeywords = recommendation.keywords?.some(keyword =>\n      aiKeywords.some(ai => keyword.toLowerCase().includes(ai))\n    ) || recommendation.title.toLowerCase().includes('ai');\n\n    if (hasAIKeywords) {\n      generatedBadges.push('AI Powered');\n    }\n\n    // Add badges from the response if available\n    if (recommendation.badges && recommendation.badges.length > 0) {\n      recommendation.badges.forEach(badge => {\n        // Only add if it's a valid BadgeType and not already included\n        if (['Top Match', 'Free Tier', 'AI Powered', 'Popular', 'New', 'Trial Available'].includes(badge) &&\n            !generatedBadges.includes(badge as BadgeType)) {\n          generatedBadges.push(badge as BadgeType);\n        }\n      });\n    }\n\n    // Note: is_open_source field has been removed\n\n    return generatedBadges;\n  }, [recommendation]);\n\n  // Get compliant disclosure text\n  const inlineDisclosure = getInlineDisclosure(recommendation, false);\n  const inlineTooltip = getInlineTooltip();\n\n  // Format match score as percentage\n  const matchScorePercentage = Math.round(recommendation.intent_match_score * 100);\n\n  // Get content based on variation\n  const getVariationContent = () => {\n    const variations = recommendation.content_variations;\n\n    if (variation === 'simple') {\n      return {\n        title: recommendation.recommendation_title || recommendation.title,\n        description: recommendation.recommendation_description || recommendation.description || recommendation.reason,\n        ctaText: recommendation.recommendation_title || recommendation.title,\n        isSimple: true\n      };\n    } else if (variation === 'question' && variations?.question) {\n      return {\n        title: variations.question.cta || recommendation.recommendation_title || recommendation.title,\n        description: variations.question.text,\n        ctaText: variations.question.cta || recommendation.recommendation_title || recommendation.title\n      };\n    } else if (variation === 'statement' && variations?.statement) {\n      return {\n        title: recommendation.recommendation_title || recommendation.title,\n        description: variations.statement.text,\n        ctaText: variations.statement.cta || recommendation.recommendation_title || recommendation.title\n      };\n    } else {\n      // Default variation\n      return {\n        title: recommendation.recommendation_title || recommendation.title,\n        description: recommendation.recommendation_description || recommendation.description || recommendation.reason,\n        ctaText: recommendation.recommendation_title || recommendation.title\n      };\n    }\n  };\n\n  const content = getVariationContent();\n\n  const cardClasses = classNames(\n    'admesh-component',\n    'admesh-card',\n    'relative p-4 sm:p-5 rounded-xl bg-gradient-to-br from-white to-gray-50 dark:from-slate-800 dark:to-slate-900 border border-gray-200/50 dark:border-slate-700/50 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1',\n    className\n  );\n\n  const cardStyle = theme ? {\n    '--admesh-primary': theme.primaryColor || theme.accentColor || '#3b82f6',\n    '--admesh-secondary': theme.secondaryColor || '#10b981',\n    '--admesh-accent': theme.accentColor || '#3b82f6',\n    '--admesh-background': theme.backgroundColor,\n    '--admesh-surface': theme.surfaceColor,\n    '--admesh-border': theme.borderColor,\n    '--admesh-text': theme.textColor,\n    '--admesh-text-secondary': theme.textSecondaryColor,\n    '--admesh-radius': theme.borderRadius || '12px',\n    '--admesh-shadow-sm': theme.shadows?.small,\n    '--admesh-shadow-md': theme.shadows?.medium,\n    '--admesh-shadow-lg': theme.shadows?.large,\n    '--admesh-spacing-sm': theme.spacing?.small,\n    '--admesh-spacing-md': theme.spacing?.medium,\n    '--admesh-spacing-lg': theme.spacing?.large,\n    '--admesh-font-size-sm': theme.fontSize?.small,\n    '--admesh-font-size-base': theme.fontSize?.base,\n    '--admesh-font-size-lg': theme.fontSize?.large,\n    '--admesh-font-size-title': theme.fontSize?.title,\n    fontFamily: theme.fontFamily\n  } as React.CSSProperties : undefined;\n\n  // Render different layouts based on variation\n  if (variation === 'simple') {\n    // Simple inline ad format (replaces AdMeshSimpleAd)\n    return (\n      <div\n        className={classNames(\n          \"admesh-component admesh-simple-ad\",\n          \"inline-block text-sm leading-relaxed\",\n          className\n        )}\n        style={{\n          fontFamily: theme?.fontFamily || '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n          ...theme?.components?.productCard,\n          ...style\n        }}\n        data-admesh-theme={theme?.mode}\n      >\n        {/* Recommendation label */}\n        <span\n          style={{\n            fontSize: '11px',\n            fontWeight: '600',\n            color: theme?.accentColor || '#2563eb',\n            backgroundColor: theme?.mode === 'dark' ? '#374151' : '#f3f4f6',\n            padding: '2px 6px',\n            borderRadius: '4px',\n            marginRight: '8px'\n          }}\n          title={getLabelTooltip(recommendation, getRecommendationLabel(recommendation))}\n        >\n          {getRecommendationLabel(recommendation)}\n        </span>\n\n        {/* Main content */}\n        <span\n          style={{\n            color: theme?.mode === 'dark' ? '#f3f4f6' : '#374151',\n            marginRight: '4px'\n          }}\n        >\n          {content.description}{' '}\n        </span>\n\n        {/* CTA Link */}\n        <AdMeshLinkTracker\n          adId={recommendation.ad_id}\n          admeshLink={recommendation.admesh_link}\n          productId={recommendation.product_id}\n          trackingData={{\n            title: recommendation.title,\n            matchScore: recommendation.intent_match_score,\n            component: 'simple_ad_cta'\n          }}\n        >\n          <span\n            style={{\n              color: theme?.accentColor || '#2563eb',\n              textDecoration: 'underline',\n              cursor: 'pointer',\n              fontSize: 'inherit',\n              fontFamily: 'inherit'\n            }}\n          >\n            {content.ctaText}\n          </span>\n        </AdMeshLinkTracker>\n\n        {/* Disclosure */}\n        <span\n          style={{\n            fontSize: '10px',\n            color: theme?.mode === 'dark' ? '#9ca3af' : '#6b7280',\n            marginLeft: '8px'\n          }}\n          title={inlineTooltip}\n        >\n          ({inlineDisclosure})\n        </span>\n      </div>\n    );\n  }\n\n  if (variation === 'question' || variation === 'statement') {\n    // Expandable layout - starts simple, can expand to full card\n    return (\n      <div\n        className={classNames(\n          \"admesh-component admesh-expandable-variation transition-all duration-300\",\n          isExpanded\n            ? \"p-4 sm:p-5 rounded-xl bg-gradient-to-br from-white to-gray-50 dark:from-slate-800 dark:to-slate-900 border border-gray-200/50 dark:border-slate-700/50 shadow-lg\"\n            : \"p-4 rounded-lg bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 shadow-sm hover:shadow-md\",\n          className\n        )}\n        style={{\n          fontFamily: theme?.fontFamily || '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n          ...theme?.components?.productCard,\n          ...style\n        }}\n        data-admesh-theme={theme?.mode}\n      >\n        {!isExpanded ? (\n          // Simple inline layout with top label\n          <>\n            {/* Recommendation label at top */}\n            <div className=\"mb-2\">\n              <span\n                style={{\n                  fontSize: '11px',\n                  fontWeight: '600',\n                  color: theme?.accentColor || '#2563eb',\n                  backgroundColor: theme?.mode === 'dark' ? '#374151' : '#f3f4f6',\n                  padding: '2px 6px',\n                  borderRadius: '4px'\n                }}\n                title={getLabelTooltip(recommendation, getRecommendationLabel(recommendation))}\n              >\n                {getRecommendationLabel(recommendation)}\n              </span>\n            </div>\n\n            <div className=\"flex items-center justify-between gap-3\">\n              {/* Content */}\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm text-gray-700 dark:text-gray-300 leading-relaxed\">\n                  {content.description}{' '}\n                  <AdMeshLinkTracker\n                    adId={recommendation.ad_id}\n                    admeshLink={recommendation.admesh_link}\n                    productId={recommendation.product_id}\n                    trackingData={{\n                      title: recommendation.title,\n                      matchScore: recommendation.intent_match_score,\n                      component: 'simple_variation_cta'\n                    }}\n                  >\n                    <span\n                      className=\"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline cursor-pointer font-medium transition-colors\"\n                    >\n                      {content.ctaText}\n                    </span>\n                  </AdMeshLinkTracker>\n                </p>\n              </div>\n\n              {/* Expand Button */}\n              <div className=\"flex items-center gap-3 flex-shrink-0\">\n                <button\n                  onClick={() => setIsExpanded(true)}\n                  className=\"flex items-center gap-2 px-3 py-2 text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-all duration-200 border border-blue-200 dark:border-blue-700 hover:border-blue-300 dark:hover:border-blue-600\"\n                  title=\"View more details\"\n                >\n                  <span>More Details</span>\n                  <svg className=\"h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                </button>\n              </div>\n            </div>\n\n\n          </>\n        ) : (\n          // Expanded full card layout (same as default variation)\n          <div\n            className=\"h-full flex flex-col\"\n            style={cardStyle}\n            data-admesh-theme={theme?.mode}\n          >\n            {/* Header with badges, title, and collapse button */}\n            <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3 mb-4\">\n              <div className=\"flex flex-col sm:flex-row sm:items-center gap-2 flex-1 min-w-0\">\n                {showBadges && badges.includes('Top Match') && (\n                  <span\n                    className=\"text-xs font-semibold text-white px-3 py-1 rounded-full w-fit shadow-md\"\n                    style={{\n                      backgroundColor: theme?.primaryColor || theme?.accentColor || '#f59e0b',\n                      borderRadius: theme?.borderRadius || '9999px'\n                    }}\n                    title={getLabelTooltip(recommendation, 'Smart Pick')}\n                  >\n                    {getBadgeText('Top Match')}\n                  </span>\n                )}\n                <div className=\"flex items-center gap-2 min-w-0\">\n                  {recommendation.product_logo && (\n                    <img\n                      src={recommendation.product_logo.url}\n                      alt={`${recommendation.title} logo`}\n                      className=\"w-6 h-6 rounded flex-shrink-0\"\n                      onError={(e) => {\n                        // Hide image if it fails to load\n                        (e.target as HTMLImageElement).style.display = 'none';\n                      }}\n                    />\n                  )}\n                  <h4 className=\"font-semibold text-gray-800 dark:text-gray-200 text-sm sm:text-base truncate\">\n                    {content.title}\n                  </h4>\n                </div>\n              </div>\n\n              <div className=\"flex gap-3 flex-shrink-0\">\n                <button\n                  onClick={() => setIsExpanded(false)}\n                  className=\"flex items-center gap-2 px-3 py-2 text-xs font-medium text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-800/50 rounded-lg transition-all duration-200 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600\"\n                  title=\"Show less details\"\n                >\n                  <span>Less Details</span>\n                  <svg className=\"h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 12H4\" />\n                  </svg>\n                </button>\n                <AdMeshLinkTracker\n                  adId={recommendation.ad_id}\n                  admeshLink={recommendation.admesh_link}\n                  productId={recommendation.product_id}\n                  trackingData={{\n                    title: recommendation.title,\n                    matchScore: recommendation.intent_match_score,\n                    component: 'product_card_cta'\n                  }}\n                >\n                  <button className=\"text-xs sm:text-sm px-3 py-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600 flex items-center transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg\">\n                    {variation === 'question' ? 'Try' : 'Visit'} {content.ctaText}\n                    <svg className=\"ml-1 h-3 w-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\" />\n                    </svg>\n                  </button>\n                </AdMeshLinkTracker>\n              </div>\n            </div>\n\n            {/* Product Description/Reason */}\n            <div className=\"mb-6\">\n              <p className=\"text-sm text-gray-600 dark:text-gray-300 leading-relaxed\">\n                {content.description}\n              </p>\n            </div>\n\n            {/* Disclosure */}\n            <div className=\"mb-6\">\n              <p\n                className=\"text-xs text-gray-500 dark:text-gray-400 leading-relaxed\"\n                title={inlineTooltip}\n              >\n                {inlineDisclosure}\n              </p>\n            </div>\n\n            {/* Match Score */}\n            {showMatchScore && typeof recommendation.intent_match_score === \"number\" && (\n              <div className=\"mb-4\">\n                <div className=\"flex items-center justify-between text-xs sm:text-sm text-gray-600 dark:text-gray-300 mb-2\">\n                  <span className=\"font-medium\">Match Score</span>\n                  <span className=\"font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-blue-500 whitespace-nowrap\">{matchScorePercentage}% match</span>\n                </div>\n                <div className=\"w-full bg-gray-200 dark:bg-slate-600 rounded-full h-2 overflow-hidden\">\n                  <div\n                    className=\"bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full transition-all duration-500 ease-out\"\n                    style={{ width: `${matchScorePercentage}%` }}\n                  />\n                </div>\n              </div>\n            )}\n\n            <div className=\"flex flex-wrap gap-2 text-xs mb-3\">\n              {recommendation.pricing && (\n                <span className=\"flex items-center px-2 py-1 rounded-full bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-700\">\n                  <svg className=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n                  </svg>\n                  {recommendation.pricing}\n                </span>\n              )}\n\n              {recommendation.trial_days && recommendation.trial_days > 0 && (\n                <span className=\"flex items-center px-2 py-1 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900/30 dark:to-cyan-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700\">\n                  <svg className=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6 0h6\" />\n                  </svg>\n                  {recommendation.trial_days}-day trial\n                </span>\n              )}\n            </div>\n\n            {/* Features */}\n            {recommendation.features && recommendation.features.length > 0 && (\n              <div className=\"mb-3\">\n                <div className=\"text-xs text-gray-600 dark:text-gray-300 mb-2 font-medium\">\n                  ✨ Key Features\n                </div>\n                <div className=\"flex flex-wrap gap-1.5\">\n                  {recommendation.features.slice(0, 4).map((feature, j) => (\n                    <span\n                      key={j}\n                      className=\"text-xs px-2 py-1 rounded-full bg-gradient-to-r from-indigo-100 to-purple-100 dark:from-indigo-900/30 dark:to-purple-900/30 text-indigo-700 dark:text-indigo-300 border border-indigo-200 dark:border-indigo-700\"\n                    >\n                      <svg className=\"h-3 w-3 mr-0.5 inline text-indigo-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                      {feature}\n                    </span>\n                  ))}\n                  {recommendation.features.length > 4 && (\n                    <span className=\"text-xs text-gray-500 dark:text-gray-400 px-2 py-1\">\n                      +{recommendation.features.length - 4} more\n                    </span>\n                  )}\n                </div>\n              </div>\n            )}\n\n            {/* Integrations */}\n            {recommendation.integrations && recommendation.integrations.length > 0 && (\n              <div className=\"mb-3\">\n                <div className=\"text-xs text-gray-600 dark:text-gray-300 mb-2 font-medium\">\n                  🔗 Integrations\n                </div>\n                <div className=\"flex flex-wrap gap-1.5\">\n                  {recommendation.integrations.slice(0, 3).map((integration, j) => (\n                    <span\n                      key={j}\n                      className=\"text-xs px-2 py-1 rounded-full bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30 text-orange-700 dark:text-orange-300 border border-orange-200 dark:border-orange-700\"\n                    >\n                      <svg className=\"h-3 w-3 mr-0.5 inline text-orange-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n                      </svg>\n                      {integration}\n                    </span>\n                  ))}\n                  {recommendation.integrations.length > 3 && (\n                    <span className=\"text-xs text-gray-500 dark:text-gray-400 px-2 py-1\">\n                      +{recommendation.integrations.length - 3} more\n                    </span>\n                  )}\n                </div>\n              </div>\n            )}\n\n            {/* Powered by AdMesh branding */}\n            <div className=\"flex justify-end mt-auto pt-2\">\n              <span className=\"text-xs text-gray-400 dark:text-gray-500\">\n                Powered by AdMesh\n              </span>\n            </div>\n          </div>\n        )}\n      </div>\n    );\n  }\n\n  // Default full product card layout\n  return (\n    <div\n      className={cardClasses}\n      style={{\n        fontFamily: theme?.fontFamily || '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n        ...theme?.components?.productCard,\n        ...style\n      }}\n      data-admesh-theme={theme?.mode}\n    >\n      <div\n        className=\"h-full flex flex-col\"\n        style={cardStyle}\n      >\n        {/* Recommendation label at top */}\n        <div className=\"mb-3\">\n          <span\n            style={{\n              fontSize: '11px',\n              fontWeight: '600',\n              color: theme?.accentColor || '#2563eb',\n              backgroundColor: theme?.mode === 'dark' ? '#374151' : '#f3f4f6',\n              padding: '2px 6px',\n              borderRadius: '4px'\n            }}\n            title={getLabelTooltip(recommendation, getRecommendationLabel(recommendation))}\n          >\n            {getRecommendationLabel(recommendation)}\n          </span>\n        </div>\n\n        {/* Header with title */}\n        <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3 mb-4\">\n          <div className=\"flex items-center gap-2 flex-1 min-w-0\">\n            {recommendation.product_logo && (\n              <img\n                src={recommendation.product_logo.url}\n                alt={`${recommendation.title} logo`}\n                className=\"w-6 h-6 rounded flex-shrink-0\"\n                onError={(e) => {\n                  // Hide image if it fails to load\n                  (e.target as HTMLImageElement).style.display = 'none';\n                }}\n              />\n            )}\n            <h4 className=\"font-semibold text-gray-800 dark:text-gray-200 text-sm sm:text-base truncate\">\n              {content.title}\n            </h4>\n          </div>\n\n          <div className=\"flex gap-2 flex-shrink-0\">\n            <AdMeshLinkTracker\n              adId={recommendation.ad_id}\n              admeshLink={recommendation.admesh_link}\n              productId={recommendation.product_id}\n              trackingData={{\n                title: recommendation.title,\n                matchScore: recommendation.intent_match_score,\n                component: 'product_card_cta'\n              }}\n            >\n              <button className=\"text-xs sm:text-sm px-3 py-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600 flex items-center transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg\">\n                Visit {content.ctaText}\n                <svg className=\"ml-1 h-3 w-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\" />\n                </svg>\n              </button>\n            </AdMeshLinkTracker>\n          </div>\n        </div>\n\n        {/* Product Description/Reason */}\n        <div className=\"mb-6\">\n          <p className=\"text-sm text-gray-600 dark:text-gray-300 leading-relaxed\">\n            {content.description}\n          </p>\n        </div>\n\n        {/* Match Score */}\n        {showMatchScore && typeof recommendation.intent_match_score === \"number\" && (\n          <div className=\"mb-6\">\n            <div className=\"flex items-center justify-between text-xs sm:text-sm text-gray-600 dark:text-gray-300 mb-2\">\n              <span className=\"font-medium\">Match Score</span>\n              <span className=\"font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-blue-500 whitespace-nowrap\">{matchScorePercentage}% match</span>\n            </div>\n            <div className=\"w-full bg-gray-200 dark:bg-slate-600 rounded-full h-2 overflow-hidden\">\n              <div\n                className=\"bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full transition-all duration-500 ease-out\"\n                style={{ width: `${matchScorePercentage}%` }}\n              />\n            </div>\n          </div>\n        )}\n\n        <div className=\"flex flex-wrap gap-2 text-xs mb-3\">\n          {recommendation.pricing && (\n            <span className=\"flex items-center px-2 py-1 rounded-full bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-700\">\n              <svg className=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n              </svg>\n              {recommendation.pricing}\n            </span>\n          )}\n\n          {recommendation.trial_days && recommendation.trial_days > 0 && (\n            <span className=\"flex items-center px-2 py-1 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900/30 dark:to-cyan-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700\">\n              <svg className=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6 0h6\" />\n              </svg>\n              {recommendation.trial_days}-day trial\n            </span>\n          )}\n        </div>\n\n        {/* Features */}\n        {recommendation.features && recommendation.features.length > 0 && (\n          <div className=\"mb-3\">\n            <div className=\"text-xs text-gray-600 dark:text-gray-300 mb-2 font-medium\">\n              ✨ Key Features\n            </div>\n            <div className=\"flex flex-wrap gap-1.5\">\n              {recommendation.features.slice(0, 4).map((feature, j) => (\n                <span\n                  key={j}\n                  className=\"text-xs px-2 py-1 rounded-full bg-gradient-to-r from-indigo-100 to-purple-100 dark:from-indigo-900/30 dark:to-purple-900/30 text-indigo-700 dark:text-indigo-300 border border-indigo-200 dark:border-indigo-700\"\n                >\n                  <svg className=\"h-3 w-3 mr-0.5 inline text-indigo-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                  {feature}\n                </span>\n              ))}\n              {recommendation.features.length > 4 && (\n                <span className=\"text-xs text-gray-500 dark:text-gray-400 px-2 py-1\">\n                  +{recommendation.features.length - 4} more\n                </span>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Integrations */}\n        {recommendation.integrations && recommendation.integrations.length > 0 && (\n          <div className=\"mb-3\">\n            <div className=\"text-xs text-gray-600 dark:text-gray-300 mb-2 font-medium\">\n              🔗 Integrations\n            </div>\n            <div className=\"flex flex-wrap gap-1.5\">\n              {recommendation.integrations.slice(0, 3).map((integration, j) => (\n                <span\n                  key={j}\n                  className=\"text-xs px-2 py-1 rounded-full bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30 text-orange-700 dark:text-orange-300 border border-orange-200 dark:border-orange-700\"\n                >\n                  <svg className=\"h-3 w-3 mr-0.5 inline text-orange-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n                  </svg>\n                  {integration}\n                </span>\n              ))}\n              {recommendation.integrations.length > 3 && (\n                <span className=\"text-xs text-gray-500 dark:text-gray-400 px-2 py-1\">\n                  +{recommendation.integrations.length - 3} more\n                </span>\n              )}\n            </div>\n          </div>\n        )}\n\n\n\n\n\n\n\n\n\n        {/* Footer section */}\n        <div className=\"mt-auto pt-3 border-t border-gray-100 dark:border-slate-700\">\n          <div className=\"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400\">\n            <span title={inlineTooltip}>\n              {inlineDisclosure}\n            </span>\n            <span className=\"text-gray-400 dark:text-gray-500\">\n              Powered by AdMesh\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nAdMeshProductCard.displayName = 'AdMeshProductCard';\n", "import React, { useMemo } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshCompareTableProps } from '../types/index';\nimport { AdMeshLinkTracker } from './AdMeshLinkTracker';\nimport { useAdMeshStyles } from '../hooks/useAdMeshStyles';\n\nexport const AdMeshCompareTable: React.FC<AdMeshCompareTableProps> = ({\n  recommendations,\n  theme,\n  maxProducts = 3,\n  showMatchScores = true,\n  showFeatures = true,\n  className,\n  style\n}) => {\n  // Inject styles automatically\n  useAdMeshStyles();\n\n  // Limit the number of products to compare\n  const productsToCompare = useMemo(() => {\n    return recommendations.slice(0, maxProducts);\n  }, [recommendations, maxProducts]);\n\n\n\n  const containerClasses = classNames(\n    'admesh-component',\n    'admesh-compare-layout',\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  if (productsToCompare.length === 0) {\n    return (\n      <div\n        className={containerClasses}\n        style={{\n          ...containerStyle,\n          fontFamily: theme?.fontFamily || '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n          ...theme?.components?.compareTable,\n          ...style\n        }}\n        data-admesh-theme={theme?.mode}\n      >\n        <div className=\"p-8 text-center text-gray-500 dark:text-gray-400\">\n          <p>No products to compare</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div\n      className={containerClasses}\n      style={{\n        ...containerStyle,\n        fontFamily: theme?.fontFamily || '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n        ...theme?.components?.compareTable,\n        ...style\n      }}\n      data-admesh-theme={theme?.mode}\n    >\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"text-center\">\n          <div className=\"flex items-center justify-center gap-2 mb-2\">\n            <svg className=\"w-5 h-5 text-gray-600 dark:text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n            </svg>\n            <h3 className=\"text-lg font-semibold text-gray-800 dark:text-gray-200\">\n              Smart Comparison\n            </h3>\n          </div>\n          <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n            {productsToCompare.length} intelligent matches found\n          </p>\n        </div>\n\n        {/* Product Cards Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {productsToCompare.map((product, index) => (\n            <div\n              key={product.product_id || index}\n              className=\"relative p-4 rounded-lg bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 shadow-sm hover:shadow transition-shadow\"\n            >\n              {/* Product Header */}\n              <div className=\"flex justify-between items-start mb-3\">\n                <div className=\"flex items-center gap-2\">\n                  {index === 0 && (\n                    <span className=\"text-xs font-semibold text-white bg-black px-2 py-0.5 rounded-full\">\n                      Top Match\n                    </span>\n                  )}\n                  <span className=\"text-xs text-gray-400 dark:text-gray-500\">\n                    #{index + 1}\n                  </span>\n                </div>\n                {showMatchScores && (\n                  <div className=\"text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap\">\n                    {Math.round(product.intent_match_score * 100)}% match\n                  </div>\n                )}\n              </div>\n\n              {/* Product Title */}\n              <h4 className=\"font-semibold text-gray-800 dark:text-gray-200 mb-2\">\n                {product.title}\n              </h4>\n\n              {/* Match Score */}\n              {showMatchScores && (\n                <div className=\"mb-3\">\n                  <div className=\"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1\">\n                    <span>Match Score</span>\n                    <span className=\"whitespace-nowrap\">{Math.round(product.intent_match_score * 100)}% match</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 dark:bg-slate-600 rounded h-1.5 overflow-hidden\">\n                    <div\n                      className=\"bg-black h-1.5\"\n                      style={{ width: `${Math.round(product.intent_match_score * 100)}%` }}\n                    />\n                  </div>\n                </div>\n              )}\n\n              {/* Pricing and Trial Info */}\n              <div className=\"flex flex-wrap gap-2 text-xs mb-3\">\n                {product.pricing && (\n                  <span className=\"flex items-center text-gray-600 dark:text-gray-400\">\n                    <svg className=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n                    </svg>\n                    {product.pricing}\n                  </span>\n                )}\n\n\n\n                {product.trial_days && product.trial_days > 0 && (\n                  <span className=\"flex items-center text-gray-600 dark:text-gray-400\">\n                    <svg className=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6 0h6\" />\n                    </svg>\n                    {product.trial_days}-day trial\n                  </span>\n                )}\n              </div>\n\n              {/* Features */}\n              {showFeatures && product.features && product.features.length > 0 && (\n                <div className=\"mb-3\">\n                  <div className=\"text-xs text-gray-500 dark:text-gray-400 mb-1\">\n                    Key Features:\n                  </div>\n                  <div className=\"flex flex-wrap gap-1.5\">\n                    {product.features.slice(0, 4).map((feature, j) => (\n                      <span\n                        key={j}\n                        className=\"text-xs px-2 py-0.5 rounded-full bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-gray-300\"\n                      >\n                        <svg className=\"h-3 w-3 mr-0.5 inline text-gray-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                        </svg>\n                        {feature}\n                      </span>\n                    ))}\n                    {(product.features.length || 0) > 4 && (\n                      <span className=\"text-xs text-gray-500 dark:text-gray-400 italic\">\n                        +{product.features.length - 4} more\n                      </span>\n                    )}\n                  </div>\n                </div>\n              )}\n\n              {/* Visit Button */}\n              <AdMeshLinkTracker\n                adId={product.ad_id}\n                admeshLink={product.admesh_link}\n                productId={product.product_id}\n                trackingData={{\n                  title: product.title,\n                  matchScore: product.intent_match_score,\n                  component: 'compare_table_cta'\n                }}\n              >\n                <button className=\"w-full text-xs px-3 py-2 rounded-lg bg-black text-white hover:bg-gray-800 flex items-center justify-center gap-1 mt-auto transition-colors\">\n                  Visit Offer\n                  <svg className=\"h-3 w-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\" />\n                  </svg>\n                </button>\n              </AdMeshLinkTracker>\n            </div>\n          ))}\n        </div>\n\n        {/* Powered by AdMesh branding */}\n        <div className=\"flex items-center justify-center mt-6 pt-4 border-t border-gray-200/50 dark:border-gray-700/50\">\n          <span className=\"flex items-center gap-1.5 text-xs text-gray-400 dark:text-gray-500\">\n            <svg className=\"w-3 h-3 text-indigo-500\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z\" clipRule=\"evenodd\" />\n            </svg>\n            <span className=\"font-medium\">Powered by</span>\n            <span className=\"font-semibold text-indigo-600 dark:text-indigo-400\">AdMesh</span>\n          </span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nAdMeshCompareTable.displayName = 'AdMeshCompareTable';\n", "import React from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshBadgeProps, BadgeType } from '../types/index';\n\n// Badge type to variant mapping\nconst badgeTypeVariants: Record<BadgeType, string> = {\n  'Top Match': 'primary',\n  'Free Tier': 'success',\n  'AI Powered': 'secondary',\n  'Popular': 'warning',\n  'New': 'primary',\n  'Trial Available': 'success'\n};\n\n// Badge type to icon mapping (using clean Unicode symbols)\nconst badgeTypeIcons: Partial<Record<BadgeType, string>> = {\n  'Top Match': '★',\n  'Free Tier': '◆',\n  'AI Powered': '◉',\n  'Popular': '▲',\n  'New': '●',\n  'Trial Available': '◈'\n};\n\nexport const AdMeshBadge: React.FC<AdMeshBadgeProps> = ({\n  type,\n  variant,\n  size = 'md',\n  className,\n  style\n}) => {\n  const effectiveVariant = variant || badgeTypeVariants[type] || 'secondary';\n  const icon = badgeTypeIcons[type];\n\n  const badgeClasses = classNames(\n    'admesh-component',\n    'admesh-badge',\n    `admesh-badge--${effectiveVariant}`,\n    `admesh-badge--${size}`,\n    className\n  );\n\n  return (\n    <span\n      className={badgeClasses}\n      style={style}\n    >\n      {icon && <span className=\"admesh-badge__icon\">{icon}</span>}\n      <span className=\"admesh-badge__text\">{type}</span>\n    </span>\n  );\n};\n\nAdMeshBadge.displayName = 'AdMeshBadge';\n", "import React, { useState } from 'react';\nimport type { AdMeshRecommendation, AdMeshTheme } from '../types';\nimport { AdMeshLinkTracker } from './AdMeshLinkTracker';\nimport { useAdMeshStyles } from '../hooks/useAdMeshStyles';\nimport {\n  getInlineDisclosure,\n  getInlineTooltip\n} from '../utils/disclosureUtils';\n\nexport interface AdMeshExpandableUnitProps {\n  /** Product recommendation data */\n  recommendation: AdMeshRecommendation;\n  /** Theme configuration */\n  theme?: AdMeshTheme;\n  /** Custom CSS class name */\n  className?: string;\n  /** Custom inline styles */\n  style?: React.CSSProperties;\n  /** Show \"powered by AdMesh\" branding */\n  showPoweredBy?: boolean;\n  /** Initial expanded state */\n  initialExpanded?: boolean;\n  /** Custom sections to display */\n  sections?: {\n    title: string;\n    description: string;\n    icon?: string;\n  }[];\n  /** Custom call-to-action text */\n  ctaText?: string;\n  /** Show collapse/expand functionality */\n  collapsible?: boolean;\n}\n\n/**\n * AdMeshExpandableUnit - A comprehensive ad unit with expandable sections\n * \n * Similar to the Temporal ad format, this component displays:\n * - Header with product name and sponsor info\n * - Multiple expandable sections with descriptions\n * - Primary call-to-action button\n * - Optional powered by branding\n */\nexport const AdMeshExpandableUnit: React.FC<AdMeshExpandableUnitProps> = ({\n  recommendation,\n  theme = { mode: 'light' },\n  className = '',\n  style,\n  showPoweredBy = true,\n  initialExpanded = false,\n  sections,\n  ctaText,\n  collapsible = true\n}) => {\n  // Inject styles automatically\n  useAdMeshStyles();\n\n  const [isExpanded, setIsExpanded] = useState(initialExpanded);\n\n\n\n  const handleToggleExpand = () => {\n    if (collapsible) {\n      setIsExpanded(!isExpanded);\n    }\n  };\n\n  // Use feature sections from API if available, otherwise use default sections\n  const apiFeatureSections = recommendation.feature_sections || [];\n\n  const defaultSections = [\n    {\n      title: 'Documentation',\n      description: `Learn more about ${recommendation.recommendation_title || recommendation.title}. Start exploring the features and capabilities.`,\n      icon: '◆'\n    },\n    {\n      title: 'Talk To An Expert',\n      description: `Ready to learn more about ${recommendation.recommendation_title || recommendation.title}? Reach out to a platform specialist for personalized guidance.`,\n      icon: '◉'\n    },\n    {\n      title: `${recommendation.recommendation_title || recommendation.title} Features`,\n      description: recommendation.recommendation_description || recommendation.description || `${recommendation.recommendation_title || recommendation.title} offers comprehensive solutions for your needs. Discover the full potential.`,\n      icon: '▲'\n    },\n    {\n      title: 'How it Works',\n      description: `Learn how to get started with ${recommendation.recommendation_title || recommendation.title}. Begin your journey today.`,\n      icon: '●'\n    }\n  ];\n\n  // Get compliant labels and disclosures\n  const inlineDisclosure = getInlineDisclosure(recommendation, false);\n  const inlineTooltip = getInlineTooltip();\n\n  // Prioritize: custom sections > API feature sections > default sections\n  const displaySections = sections || (apiFeatureSections.length > 0 ? apiFeatureSections : defaultSections);\n  const displayCtaText = ctaText || `Try ${recommendation.recommendation_title || recommendation.title}`;\n\n  // Clean, professional color scheme with customization support\n  const colors = {\n    background: theme.backgroundColor || (theme.mode === 'dark' ? '#1f2937' : '#ffffff'),\n    surface: theme.surfaceColor || (theme.mode === 'dark' ? '#374151' : '#f9fafb'),\n    border: theme.borderColor || (theme.mode === 'dark' ? '#4b5563' : '#e5e7eb'),\n    text: theme.textColor || (theme.mode === 'dark' ? '#f9fafb' : '#111827'),\n    textSecondary: theme.textSecondaryColor || (theme.mode === 'dark' ? '#9ca3af' : '#6b7280'),\n    accent: theme.accentColor || theme.primaryColor || '#3b82f6',\n    secondary: theme.secondaryColor || '#10b981',\n    // Remove excessive gradients, use clean solid colors or subtle gradients\n    headerBg: theme.gradients?.primary || (theme.mode === 'dark' ? '#374151' : '#f8fafc'),\n    sectionBg: theme.gradients?.secondary || (theme.mode === 'dark' ? '#4b5563' : '#ffffff')\n  };\n\n  // Get custom styles if provided\n  const customStyles = theme.disableDefaultStyles ? {} : {\n    fontFamily: theme.fontFamily || '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n    borderRadius: theme.borderRadius || '12px',\n    border: `1px solid ${colors.border}`,\n    background: colors.background,\n    overflow: 'hidden',\n    maxWidth: '420px',\n    boxShadow: theme.shadows?.medium || (theme.mode === 'dark'\n      ? '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)'\n      : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'),\n    position: 'relative' as const,\n    transition: 'all 0.2s ease'\n  };\n\n  return (\n    <div\n      className={`admesh-component admesh-expandable-unit ${className}`}\n      style={{\n        ...customStyles,\n        ...theme.components?.expandableUnit,\n        ...style\n      }}\n      data-admesh-theme={theme.mode}\n    >\n      {/* Header */}\n      <div\n        style={{\n          background: colors.headerBg,\n          padding: '20px',\n          borderBottom: isExpanded || !collapsible ? `1px solid ${colors.border}` : 'none',\n          position: 'relative',\n          transition: 'all 0.2s ease'\n        }}\n      >\n        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', gap: '16px' }}>\n          <div style={{ display: 'flex', alignItems: 'center', gap: '16px', flex: 1, minWidth: 0 }}>\n            <div\n              style={{\n                width: '40px',\n                height: '40px',\n                borderRadius: theme.borderRadius || '8px',\n                background: colors.accent,\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                color: 'white',\n                fontSize: theme.fontSize?.base || '16px',\n                fontWeight: '600',\n                boxShadow: theme.shadows?.small || '0 2px 4px rgba(0, 0, 0, 0.1)',\n                border: `1px solid ${colors.border}`\n              }}\n            >\n              {(recommendation.recommendation_title || recommendation.title).charAt(0).toUpperCase()}\n            </div>\n            <div style={{ flex: 1, minWidth: 0 }}>\n              <h3\n                style={{\n                  margin: 0,\n                  fontSize: '18px',\n                  fontWeight: '600',\n                  color: colors.text,\n                  lineHeight: '1.4',\n                  overflow: 'hidden',\n                  textOverflow: 'ellipsis',\n                  whiteSpace: 'nowrap'\n                }}\n              >\n                {recommendation.recommendation_title || recommendation.title}\n              </h3>\n              <p\n                style={{\n                  margin: '8px 0 0 0',\n                  fontSize: '13px',\n                  color: colors.textSecondary,\n                  fontWeight: '400',\n                  overflow: 'hidden',\n                  textOverflow: 'ellipsis',\n                  whiteSpace: 'nowrap'\n                }}\n                title={inlineTooltip}\n              >\n                {inlineDisclosure} • {new URL(recommendation.url || recommendation.admesh_link).hostname}\n              </p>\n            </div>\n          </div>\n\n          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n            {/* CTA Button when collapsed */}\n            {!isExpanded && collapsible && (\n              <AdMeshLinkTracker\n                adId={recommendation.ad_id}\n                admeshLink={recommendation.admesh_link}\n                productId={recommendation.product_id}\n                trackingData={{\n                  title: recommendation.recommendation_title || recommendation.title,\n                  component: 'expandable_unit',\n                  expanded: false,\n                  location: 'header'\n                }}\n              >\n                <button\n                  style={{\n                    padding: theme.spacing?.small ? `${theme.spacing.small} ${theme.spacing.medium || '12px'}` : '6px 12px',\n                    backgroundColor: colors.accent,\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: theme.borderRadius || '6px',\n                    fontSize: theme.fontSize?.small || '12px',\n                    fontWeight: '500',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease',\n                    boxShadow: theme.shadows?.small || '0 1px 3px rgba(0, 0, 0, 0.1)',\n                    whiteSpace: 'nowrap',\n                    ...theme.components?.button\n                  }}\n                  onMouseOver={(e) => {\n                    if (!theme.disableDefaultStyles) {\n                      e.currentTarget.style.transform = 'translateY(-1px)';\n                      e.currentTarget.style.boxShadow = theme.shadows?.medium || '0 2px 6px rgba(0, 0, 0, 0.15)';\n                    }\n                  }}\n                  onMouseOut={(e) => {\n                    if (!theme.disableDefaultStyles) {\n                      e.currentTarget.style.transform = 'translateY(0)';\n                      e.currentTarget.style.boxShadow = theme.shadows?.small || '0 1px 3px rgba(0, 0, 0, 0.1)';\n                    }\n                  }}\n                >\n                  {displayCtaText}\n                </button>\n              </AdMeshLinkTracker>\n            )}\n\n            {/* Modern Expand/Collapse button */}\n            {collapsible && (\n              <button\n                onClick={handleToggleExpand}\n                style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '8px',\n                  padding: '8px 12px',\n                  background: theme.mode === 'dark' ? '#374151' : '#f3f4f6',\n                  border: `1px solid ${theme.mode === 'dark' ? '#4b5563' : '#d1d5db'}`,\n                  borderRadius: '8px',\n                  cursor: 'pointer',\n                  color: theme.accentColor || '#2563eb',\n                  fontSize: '12px',\n                  fontWeight: '600',\n                  transition: 'all 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.background = theme.mode === 'dark' ? '#4b5563' : '#e5e7eb';\n                  e.currentTarget.style.borderColor = theme.accentColor || '#2563eb';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.background = theme.mode === 'dark' ? '#374151' : '#f3f4f6';\n                  e.currentTarget.style.borderColor = theme.mode === 'dark' ? '#4b5563' : '#d1d5db';\n                }}\n                aria-label={isExpanded ? 'Show less details' : 'Show more details'}\n              >\n                <span>{isExpanded ? 'Less Details' : 'More Details'}</span>\n                <svg\n                  width=\"16\"\n                  height=\"16\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  strokeWidth=\"2\"\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                >\n                  {isExpanded ? (\n                    // Minus icon for collapse\n                    <path d=\"M5 12h14\" />\n                  ) : (\n                    // Info icon for expand\n                    <>\n                      <circle cx=\"12\" cy=\"12\" r=\"10\" />\n                      <path d=\"M12 16v-4\" />\n                      <path d=\"M12 8h.01\" />\n                    </>\n                  )}\n                </svg>\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Expandable Content */}\n      {(isExpanded || !collapsible) && (\n        <div style={{ padding: '0' }}>\n          {/* Sections */}\n          {displaySections.map((section, index) => (\n            <div\n              key={index}\n              style={{\n                padding: '24px',\n                backgroundColor: index % 2 === 0 ? colors.background : colors.sectionBg,\n                borderBottom: index < displaySections.length - 1 ? `1px solid ${colors.border}` : 'none'\n              }}\n            >\n              <h4\n                style={{\n                  margin: '0 0 12px 0',\n                  fontSize: '15px',\n                  fontWeight: '600',\n                  color: colors.text,\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '12px'\n                }}\n              >\n                {section.icon && <span>{section.icon}</span>}\n                {section.title}\n              </h4>\n              <p\n                style={{\n                  margin: 0,\n                  fontSize: '14px',\n                  color: colors.textSecondary,\n                  lineHeight: '1.6'\n                }}\n              >\n                {section.description}\n              </p>\n            </div>\n          ))}\n\n          {/* CTA Button - only show when expanded or when not collapsible */}\n          {(isExpanded || !collapsible) && (\n            <div style={{ padding: '24px', borderTop: `1px solid ${colors.border}`, backgroundColor: colors.background }}>\n              <AdMeshLinkTracker\n                adId={recommendation.ad_id}\n                admeshLink={recommendation.admesh_link}\n                productId={recommendation.product_id}\n                trackingData={{\n                  title: recommendation.title,\n                  component: 'expandable_unit',\n                  expanded: isExpanded,\n                  location: 'footer'\n                }}\n              >\n                <button\n                  style={{\n                    width: '100%',\n                    padding: '14px 28px',\n                    background: colors.accent,\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: '12px',\n                    fontSize: '15px',\n                    fontWeight: '600',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    boxShadow: '0 4px 12px rgba(99, 102, 241, 0.3)',\n                    position: 'relative',\n                    overflow: 'hidden'\n                  }}\n                  onMouseOver={(e) => {\n                    e.currentTarget.style.transform = 'translateY(-2px) scale(1.02)';\n                    e.currentTarget.style.boxShadow = '0 8px 20px rgba(99, 102, 241, 0.4)';\n                  }}\n                  onMouseOut={(e) => {\n                    e.currentTarget.style.transform = 'translateY(0) scale(1)';\n                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(99, 102, 241, 0.3)';\n                  }}\n                >\n                  {displayCtaText}\n                </button>\n              </AdMeshLinkTracker>\n            </div>\n          )}\n\n          {/* Powered by AdMesh */}\n          {showPoweredBy && (\n            <div\n              style={{\n                padding: '8px 16px',\n                borderTop: `1px solid ${colors.border}`,\n                backgroundColor: colors.headerBg\n              }}\n            >\n              <div\n                style={{\n                  fontSize: '11px',\n                  color: colors.textSecondary,\n                  textAlign: 'center' as const\n                }}\n              >\n                powered by <strong style={{ color: colors.text }}>AdMesh</strong>\n              </div>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AdMeshExpandableUnit;\n", "import React from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshInlineRecommendationProps } from '../types/index';\nimport { AdMeshLinkTracker } from './AdMeshLinkTracker';\nimport {\n  getInlineDisclosure,\n  getInlineTooltip\n} from '../utils/disclosureUtils';\n\nexport const AdMeshInlineRecommendation: React.FC<AdMeshInlineRecommendationProps> = ({\n  recommendation,\n  theme,\n  compact = false,\n  showReason = true,\n  className,\n  style\n}) => {\n  const matchScorePercentage = Math.round(recommendation.intent_match_score * 100);\n\n  // Get compliant labels and disclosures\n  const inlineDisclosure = getInlineDisclosure(recommendation, compact);\n  const inlineTooltip = getInlineTooltip();\n\n  const containerClasses = classNames(\n    'admesh-inline-recommendation',\n    'group cursor-pointer transition-all duration-200',\n    {\n      'p-2 sm:p-3 rounded-md bg-gray-50 dark:bg-slate-800/50 hover:bg-gray-100 dark:hover:bg-slate-800 border border-gray-200 dark:border-slate-700': !compact,\n      'p-1.5 sm:p-2 rounded hover:bg-gray-50 dark:hover:bg-slate-800/30': compact,\n    },\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <AdMeshLinkTracker\n      adId={recommendation.ad_id}\n      admeshLink={recommendation.admesh_link}\n      productId={recommendation.product_id}\n      trackingData={{\n        title: recommendation.title,\n        matchScore: recommendation.intent_match_score\n      }}\n      className={containerClasses}\n      style={{\n        fontFamily: theme?.fontFamily || '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n        ...theme?.components?.inlineRecommendation,\n        ...style\n      }}\n    >\n      <div\n        className=\"flex items-start gap-3\"\n        style={containerStyle}\n        data-admesh-theme={theme?.mode}\n      >\n        {/* Icon/Badge */}\n        <div className=\"flex-shrink-0 mt-0.5\">\n          {recommendation.offer_images && recommendation.offer_images.length > 0 ? (\n            <div className=\"w-6 h-6 rounded-full overflow-hidden border border-gray-200 dark:border-gray-600\">\n              <img\n                src={recommendation.offer_images[0].url}\n                alt={recommendation.recommendation_title || recommendation.title}\n                className=\"w-full h-full object-cover\"\n              />\n            </div>\n          ) : recommendation.product_logo ? (\n            <div className=\"w-6 h-6 rounded-full overflow-hidden border border-gray-200 dark:border-gray-600\">\n              <img\n                src={recommendation.product_logo.url}\n                alt={recommendation.recommendation_title || recommendation.title}\n                className=\"w-full h-full object-cover\"\n              />\n            </div>\n          ) : recommendation.intent_match_score >= 0.8 ? (\n            <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n          ) : (\n            <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n          )}\n        </div>\n\n        {/* Content */}\n        <div className=\"flex-1 min-w-0\">\n          <div className=\"flex items-start sm:items-center gap-2 mb-1 flex-col sm:flex-row\">\n            <h4 className={classNames(\n              'font-medium transition-colors duration-200 flex-shrink-0',\n              'text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300',\n              'cursor-pointer hover:underline',\n              compact ? 'text-sm sm:text-base' : 'text-base sm:text-lg'\n            )}>\n              {recommendation.recommendation_title || recommendation.title}\n            </h4>\n            \n            {/* Match score badge */}\n            {recommendation.intent_match_score >= 0.7 && (\n              <span className={classNames(\n                'inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium flex-shrink-0 whitespace-nowrap',\n                recommendation.intent_match_score >= 0.8\n                  ? 'bg-green-100 text-green-800 dark:bg-green-800/80 dark:text-green-100'\n                  : 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'\n              )}>\n                {matchScorePercentage}% match\n              </span>\n            )}\n          </div>\n\n          {/* Reason/Description */}\n          {showReason && (recommendation.recommendation_description || recommendation.reason) && (\n            <p className={classNames(\n              'text-gray-600 dark:text-gray-400 line-clamp-2',\n              compact ? 'text-xs' : 'text-sm'\n            )}>\n              {recommendation.recommendation_description || recommendation.reason}\n            </p>\n          )}\n\n          {/* Disclosure */}\n          <p\n            className={classNames(\n              'text-gray-500 dark:text-gray-400 mt-1',\n              compact ? 'text-xs' : 'text-xs'\n            )}\n            title={inlineTooltip}\n          >\n            {inlineDisclosure}\n          </p>\n\n          {/* Features/Keywords */}\n          {!compact && recommendation.keywords && recommendation.keywords.length > 0 && (\n            <div className=\"flex flex-wrap gap-1 mt-2\">\n              {recommendation.keywords.slice(0, 3).map((keyword, index) => (\n                <span\n                  key={index}\n                  className=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-slate-700 dark:text-gray-300\"\n                >\n                  {keyword}\n                </span>\n              ))}\n              {recommendation.keywords.length > 3 && (\n                <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                  +{recommendation.keywords.length - 3} more\n                </span>\n              )}\n            </div>\n          )}\n\n          {/* Pricing/Trial info */}\n          {!compact && recommendation.trial_days && recommendation.trial_days > 0 && (\n            <div className=\"flex items-center gap-2 mt-2\">\n              <span className=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400\">\n                {recommendation.trial_days}-day trial\n              </span>\n            </div>\n          )}\n        </div>\n\n        {/* Arrow indicator */}\n        <div className=\"flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity\">\n          <svg \n            className=\"w-4 h-4 text-gray-400 dark:text-gray-500\" \n            fill=\"none\" \n            stroke=\"currentColor\" \n            viewBox=\"0 0 24 24\"\n          >\n            <path \n              strokeLinecap=\"round\" \n              strokeLinejoin=\"round\" \n              strokeWidth={2} \n              d=\"M9 5l7 7-7 7\" \n            />\n          </svg>\n        </div>\n      </div>\n    </AdMeshLinkTracker>\n  );\n};\n", "import React from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshConversationSummaryProps } from '../types/index';\nimport { AdMeshInlineRecommendation } from './AdMeshInlineRecommendation';\n\nexport const AdMeshConversationSummary: React.FC<AdMeshConversationSummaryProps> = ({\n  recommendations,\n  conversationSummary,\n  theme,\n  showTopRecommendations = 3,\n  onRecommendationClick,\n  onStartNewConversation,\n  className\n}) => {\n  const topRecommendations = recommendations\n    .sort((a, b) => b.intent_match_score - a.intent_match_score)\n    .slice(0, showTopRecommendations);\n\n  const containerClasses = classNames(\n    'admesh-conversation-summary',\n    'bg-white dark:bg-black',\n    'rounded-lg border border-gray-200 dark:border-gray-800 p-4 sm:p-6',\n    'font-sans', // Standardize font family\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Header */}\n      <div className=\"flex items-center gap-3 mb-4\">\n        <div className=\"flex-shrink-0\">\n          <div className=\"w-8 h-8 sm:w-10 sm:h-10 bg-black dark:bg-white rounded-full flex items-center justify-center\">\n            <svg className=\"w-4 h-4 sm:w-5 sm:h-5 text-white dark:text-black\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n          </div>\n        </div>\n        <div className=\"min-w-0 flex-1\">\n          <h3 className=\"text-base sm:text-lg font-semibold text-black dark:text-white\">\n            Conversation Summary\n          </h3>\n          <p className=\"text-xs sm:text-sm text-gray-600 dark:text-gray-300\">\n            Here's what we discussed and found for you\n          </p>\n        </div>\n      </div>\n\n      {/* Summary Text */}\n      <div className=\"mb-6\">\n        <div className=\"bg-gray-50 dark:bg-gray-900 rounded-lg p-4 border border-gray-200 dark:border-gray-700\">\n          <p className=\"text-gray-800 dark:text-gray-200 leading-relaxed\">\n            {conversationSummary}\n          </p>\n        </div>\n      </div>\n\n      {/* Top Recommendations */}\n      {topRecommendations.length > 0 && (\n        <div className=\"mb-6\">\n          <div className=\"flex items-center gap-2 mb-3\">\n            <svg className=\"w-5 h-5 text-black dark:text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n            </svg>\n            <h4 className=\"font-medium text-black dark:text-white\">\n              Top Recommendations\n            </h4>\n          </div>\n          \n          <div className=\"space-y-2\">\n            {topRecommendations.map((recommendation, index) => (\n              <div key={recommendation.ad_id || index} className=\"relative\">\n                {/* Ranking badge */}\n                <div className=\"absolute -left-2 top-2 z-10\">\n                  <div className={classNames(\n                    'w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold',\n                    index === 0 ? 'bg-black dark:bg-white text-white dark:text-black' :\n                    index === 1 ? 'bg-gray-600 dark:bg-gray-400 text-white dark:text-black' :\n                    'bg-gray-800 dark:bg-gray-200 text-white dark:text-black'\n                  )}>\n                    {index + 1}\n                  </div>\n                </div>\n                \n                <div className=\"ml-4\">\n                  <AdMeshInlineRecommendation\n                    recommendation={recommendation}\n                    theme={theme}\n                    compact={true}\n                    showReason={true}\n                    onClick={onRecommendationClick}\n                  />\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Additional Insights */}\n      {recommendations.length > showTopRecommendations && (\n        <div className=\"mb-6\">\n          <div className=\"bg-gray-100 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700\">\n            <div className=\"flex items-center gap-2\">\n              <svg className=\"w-4 h-4 text-black dark:text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n              <span className=\"text-sm font-medium text-gray-800 dark:text-gray-200\">\n                {recommendations.length - showTopRecommendations} additional recommendation{recommendations.length - showTopRecommendations > 1 ? 's' : ''} available\n              </span>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Action Buttons */}\n      <div className=\"flex flex-col sm:flex-row gap-3\">\n        {onStartNewConversation && (\n          <button\n            onClick={onStartNewConversation}\n            className=\"flex-1 bg-black dark:bg-white hover:bg-gray-800 dark:hover:bg-gray-100 text-white dark:text-black font-medium py-2.5 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n            </svg>\n            Start New Conversation\n          </button>\n        )}\n        \n        <button\n          onClick={() => {\n            if (topRecommendations.length > 0) {\n              onRecommendationClick?.(topRecommendations[0].ad_id, topRecommendations[0].admesh_link);\n            }\n          }}\n          className=\"flex-1 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 text-black dark:text-white font-medium py-2.5 px-4 rounded-lg border border-gray-300 dark:border-gray-600 transition-all duration-200 flex items-center justify-center gap-2\"\n        >\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\" />\n          </svg>\n          View Top Pick\n        </button>\n      </div>\n\n      {/* Powered by AdMesh */}\n      <div className=\"flex justify-center mt-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n          Powered by AdMesh\n        </span>\n      </div>\n    </div>\n  );\n};\n", "import React, { useState } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshCitationReferenceProps } from '../types/index';\nimport { AdMeshLinkTracker } from './AdMeshLinkTracker';\n\nexport const AdMeshCitationReference: React.FC<AdMeshCitationReferenceProps> = ({\n  recommendation,\n  citationNumber,\n  citationStyle = 'numbered',\n  theme,\n  showTooltip = true,\n  onHover,\n  className,\n  style\n}) => {\n  const [isHovered, setIsHovered] = useState(false);\n\n  const handleMouseEnter = () => {\n    setIsHovered(true);\n    onHover?.(recommendation);\n  };\n\n  const handleMouseLeave = () => {\n    setIsHovered(false);\n  };\n\n\n\n  // Generate citation display based on style\n  const getCitationDisplay = () => {\n    switch (citationStyle) {\n      case 'bracketed':\n        return `[${citationNumber}]`;\n      case 'superscript':\n        return citationNumber.toString();\n      case 'numbered':\n      default:\n        return citationNumber.toString();\n    }\n  };\n\n  const citationClasses = classNames(\n    'admesh-citation-reference',\n    'inline-flex items-center justify-center',\n    'cursor-pointer transition-all duration-200',\n    'text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300',\n    'font-medium',\n    {\n      // Numbered style (default)\n      'w-5 h-5 bg-blue-100 dark:bg-blue-900/30 rounded-full text-xs border border-blue-300 dark:border-blue-700 hover:bg-blue-200 dark:hover:bg-blue-900/50': citationStyle === 'numbered',\n      \n      // Bracketed style\n      'px-1 text-sm hover:underline': citationStyle === 'bracketed',\n      \n      // Superscript style\n      'text-xs align-super hover:underline': citationStyle === 'superscript',\n    },\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <span className=\"relative inline-block\">\n      <AdMeshLinkTracker\n        adId={recommendation.ad_id}\n        admeshLink={recommendation.admesh_link}\n        productId={recommendation.product_id}\n        trackingData={{\n          title: recommendation.title,\n          matchScore: recommendation.intent_match_score,\n          citationNumber,\n          citationStyle\n        }}\n        className={citationClasses}\n        style={style}\n      >\n        <span\n          style={containerStyle}\n          data-admesh-theme={theme?.mode}\n          onMouseEnter={handleMouseEnter}\n          onMouseLeave={handleMouseLeave}\n        >\n          {getCitationDisplay()}\n        </span>\n      </AdMeshLinkTracker>\n\n      {/* Tooltip */}\n      {showTooltip && isHovered && (\n        <div className=\"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-50\">\n          <div className=\"bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs rounded-lg px-3 py-2 shadow-lg max-w-xs\">\n            <div className=\"font-semibold mb-1\">{recommendation.title}</div>\n            {recommendation.reason && (\n              <div className=\"text-gray-300 dark:text-gray-600 text-xs\">\n                {recommendation.reason.length > 100 \n                  ? `${recommendation.reason.substring(0, 100)}...` \n                  : recommendation.reason\n                }\n              </div>\n            )}\n            {recommendation.intent_match_score >= 0.7 && (\n              <div className=\"text-green-400 dark:text-green-600 text-xs mt-1\">\n                {Math.round(recommendation.intent_match_score * 100)}% match\n              </div>\n            )}\n            <div className=\"text-gray-400 dark:text-gray-500 text-xs mt-1 italic\">\n              Click to visit product page\n            </div>\n            {/* Tooltip arrow */}\n            <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-100\"></div>\n          </div>\n        </div>\n      )}\n    </span>\n  );\n};\n", "import React, { useState, useMemo } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshCitationUnitProps, AdMeshRecommendation } from '../types/index';\nimport { AdMeshCitationReference } from './AdMeshCitationReference';\nimport { AdMeshInlineRecommendation } from './AdMeshInlineRecommendation';\n\nexport const AdMeshCitationUnit: React.FC<AdMeshCitationUnitProps> = ({\n  recommendations,\n  conversationText,\n  theme,\n  showCitationList = true,\n  citationStyle = 'numbered',\n  onCitationHover,\n  className,\n  style\n}) => {\n  const [hoveredRecommendation, setHoveredRecommendation] = useState<AdMeshRecommendation | null>(null);\n\n  // Process conversation text to insert citations\n  const processedContent = useMemo(() => {\n    if (!conversationText || recommendations.length === 0) {\n      return { text: conversationText, citationMap: new Map() };\n    }\n\n    let processedText = conversationText;\n    const citationMap = new Map();\n    \n    // Sort recommendations by intent match score (highest first)\n    const sortedRecommendations = [...recommendations]\n      .sort((a, b) => b.intent_match_score - a.intent_match_score);\n\n    // Find mentions of product titles in the text and replace with citations\n    sortedRecommendations.forEach((recommendation, index) => {\n      const citationNumber = index + 1;\n      const title = recommendation.title;\n      \n      // Create citation reference\n      citationMap.set(citationNumber, recommendation);\n      \n      // Look for exact title matches (case insensitive)\n      const titleRegex = new RegExp(`\\\\b${title.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&')}\\\\b`, 'gi');\n      \n      // Replace first occurrence with citation\n      if (titleRegex.test(processedText)) {\n        processedText = processedText.replace(titleRegex, (match) => {\n          return `${match}{{CITATION_${citationNumber}}}`;\n        });\n      } else {\n        // If no exact match, try to find a good insertion point\n        // Look for related keywords or add at the end of relevant sentences\n        const keywords = recommendation.keywords || [];\n        let inserted = false;\n        \n        for (const keyword of keywords) {\n          const keywordRegex = new RegExp(`\\\\b${keyword.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&')}\\\\b`, 'gi');\n          if (keywordRegex.test(processedText) && !inserted) {\n            processedText = processedText.replace(keywordRegex, (match) => {\n              inserted = true;\n              return `${match}{{CITATION_${citationNumber}}}`;\n            });\n            break;\n          }\n        }\n        \n        // If still no insertion point found, add citation at the end\n        if (!inserted) {\n          processedText += `{{CITATION_${citationNumber}}}`;\n        }\n      }\n    });\n\n    return { text: processedText, citationMap };\n  }, [conversationText, recommendations]);\n\n  // Render text with embedded citations\n  const renderTextWithCitations = () => {\n    const { text, citationMap } = processedContent;\n    const parts = text.split(/(\\{\\{CITATION_\\d+\\}\\})/);\n\n    return parts.map((part, index) => {\n      const citationMatch = part.match(/\\{\\{CITATION_(\\d+)\\}\\}/);\n\n      if (citationMatch) {\n        const citationNumber = parseInt(citationMatch[1]);\n        const recommendation = citationMap.get(citationNumber);\n\n        if (recommendation) {\n          return (\n            <AdMeshCitationReference\n              key={`citation-${citationNumber}-${index}`}\n              recommendation={recommendation}\n              citationNumber={citationNumber}\n              citationStyle={citationStyle}\n              theme={theme}\n              showTooltip={true}\n              onHover={(rec) => {\n                setHoveredRecommendation(rec);\n                onCitationHover?.(rec);\n              }}\n            />\n          );\n        }\n      }\n\n      return <span key={index}>{part}</span>;\n    });\n  };\n\n  const containerClasses = classNames(\n    'admesh-citation-unit',\n    'space-y-4',\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={containerClasses}\n      style={{\n        ...containerStyle,\n        fontFamily: theme?.fontFamily || '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n        ...theme?.components?.citationUnit,\n        ...style\n      }}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Main conversation text with embedded citations */}\n      <div className=\"admesh-citation-text text-gray-800 dark:text-gray-200 leading-relaxed\">\n        {renderTextWithCitations()}\n      </div>\n\n      {/* Citation list/references */}\n      {showCitationList && recommendations.length > 0 && (\n        <div className=\"admesh-citation-list\">\n          <div className=\"border-t border-gray-200 dark:border-slate-700 pt-4\">\n            <h4 className=\"text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2\">\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1\" />\n              </svg>\n              References\n            </h4>\n            \n            <div className=\"space-y-2\">\n              {recommendations\n                .sort((a, b) => b.intent_match_score - a.intent_match_score)\n                .map((recommendation, index) => (\n                  <div \n                    key={recommendation.ad_id || index}\n                    className={classNames(\n                      'flex items-start gap-3 p-2 rounded-lg transition-colors duration-200',\n                      {\n                        'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800': \n                          hoveredRecommendation?.ad_id === recommendation.ad_id,\n                        'hover:bg-gray-50 dark:hover:bg-slate-800/50': \n                          hoveredRecommendation?.ad_id !== recommendation.ad_id\n                      }\n                    )}\n                  >\n                    {/* Citation number */}\n                    <div className=\"flex-shrink-0 mt-1\">\n                      <AdMeshCitationReference\n                        recommendation={recommendation}\n                        citationNumber={index + 1}\n                        citationStyle={citationStyle}\n                        theme={theme}\n                        showTooltip={false}\n                      />\n                    </div>\n                    \n                    {/* Recommendation details */}\n                    <div className=\"flex-1 min-w-0\">\n                      <AdMeshInlineRecommendation\n                        recommendation={recommendation}\n                        theme={theme}\n                        compact={true}\n                        showReason={false}\n                      />\n                    </div>\n                  </div>\n                ))}\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n", "import React, { useState, useEffect } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshConversationalUnitProps } from '../types/index';\nimport { AdMeshInlineRecommendation } from './AdMeshInlineRecommendation';\nimport { AdMeshConversationSummary } from './AdMeshConversationSummary';\nimport { AdMeshProductCard } from './AdMeshProductCard';\nimport { AdMeshCitationUnit } from './AdMeshCitationUnit';\n\nexport const AdMeshConversationalUnit: React.FC<AdMeshConversationalUnitProps> = ({\n  recommendations,\n  config,\n  theme,\n  conversationSummary,\n  sessionId,\n  onRecommendationClick,\n  onDismiss,\n  className\n}) => {\n  const [isVisible, setIsVisible] = useState(config.autoShow !== false);\n  const [hasAnimated, setHasAnimated] = useState(false);\n\n  useEffect(() => {\n    if (config.delayMs && config.delayMs > 0) {\n      const timer = setTimeout(() => {\n        setIsVisible(true);\n        setHasAnimated(true);\n      }, config.delayMs);\n      return () => clearTimeout(timer);\n    } else {\n      setHasAnimated(true);\n    }\n  }, [config.delayMs]);\n\n  if (!isVisible || recommendations.length === 0) {\n    return null;\n  }\n\n  const maxRecommendations = config.maxRecommendations || 3;\n  const displayRecommendations = recommendations.slice(0, maxRecommendations);\n\n  const handleRecommendationClick = (adId: string, admeshLink: string) => {\n    onRecommendationClick?.(adId, admeshLink);\n  };\n\n  const handleDismiss = () => {\n    setIsVisible(false);\n    onDismiss?.();\n  };\n\n  // Render based on display mode\n  const renderContent = () => {\n    switch (config.displayMode) {\n      case 'summary':\n        return conversationSummary ? (\n          <AdMeshConversationSummary\n            recommendations={displayRecommendations}\n            conversationSummary={conversationSummary}\n            theme={theme}\n            showTopRecommendations={maxRecommendations}\n            onRecommendationClick={handleRecommendationClick}\n            onStartNewConversation={onDismiss}\n          />\n        ) : null;\n\n      case 'inline':\n        return (\n          <div className=\"space-y-2\">\n            {displayRecommendations.map((recommendation, index) => (\n              <AdMeshInlineRecommendation\n                key={recommendation.ad_id || index}\n                recommendation={recommendation}\n                theme={theme}\n                compact={true}\n                showReason={true}\n                onClick={handleRecommendationClick}\n              />\n            ))}\n          </div>\n        );\n\n      case 'minimal':\n        return displayRecommendations.length > 0 ? (\n          <div className=\"admesh-minimal-unit\">\n            <div className=\"flex items-center gap-2 mb-2\">\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n              <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                {displayRecommendations.length} intelligent match{displayRecommendations.length > 1 ? 'es' : ''} found\n              </span>\n            </div>\n            <AdMeshInlineRecommendation\n              recommendation={displayRecommendations[0]}\n              theme={theme}\n              compact={true}\n              showReason={false}\n              onClick={handleRecommendationClick}\n            />\n            {displayRecommendations.length > 1 && (\n              <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                +{displayRecommendations.length - 1} more recommendation{displayRecommendations.length > 2 ? 's' : ''}\n              </div>\n            )}\n          </div>\n        ) : null;\n\n      case 'citation':\n        return conversationSummary ? (\n          <AdMeshCitationUnit\n            recommendations={displayRecommendations}\n            conversationText={conversationSummary}\n            theme={theme}\n            showCitationList={true}\n            citationStyle=\"numbered\"\n            onRecommendationClick={handleRecommendationClick}\n          />\n        ) : null;\n\n      case 'floating':\n        return (\n          <div className=\"admesh-floating-unit bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700 p-4\">\n            <div className=\"flex justify-between items-start mb-3\">\n              <div className=\"flex items-center gap-2\">\n                <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                <span className=\"text-sm font-semibold text-gray-800 dark:text-gray-200\">\n                  Recommended for you\n                </span>\n              </div>\n              {onDismiss && (\n                <button\n                  onClick={handleDismiss}\n                  className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors\"\n                  aria-label=\"Dismiss recommendations\"\n                >\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              )}\n            </div>\n            <div className=\"space-y-2\">\n              {displayRecommendations.map((recommendation, index) => (\n                <AdMeshInlineRecommendation\n                  key={recommendation.ad_id || index}\n                  recommendation={recommendation}\n                  theme={theme}\n                  compact={true}\n                  showReason={false}\n                  onClick={handleRecommendationClick}\n                />\n              ))}\n            </div>\n          </div>\n        );\n\n      default:\n        return (\n          <div className=\"space-y-3\">\n            {displayRecommendations.map((recommendation, index) => (\n              <AdMeshProductCard\n                key={recommendation.ad_id || index}\n                recommendation={recommendation}\n                theme={theme}\n                showMatchScore={false}\n                showBadges={true}\n                onClick={handleRecommendationClick}\n              />\n            ))}\n          </div>\n        );\n    }\n  };\n\n  const containerClasses = classNames(\n    'admesh-conversational-unit',\n    'transition-all duration-300 ease-in-out',\n    {\n      'opacity-0 translate-y-2': !hasAnimated,\n      'opacity-100 translate-y-0': hasAnimated,\n      'fixed bottom-4 right-4 max-w-sm z-50': config.displayMode === 'floating',\n      'my-3': config.displayMode === 'inline',\n      'mt-4 pt-4 border-t border-gray-200 dark:border-slate-700': config.displayMode === 'summary',\n    },\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n      data-admesh-context={config.context}\n      data-session-id={sessionId}\n    >\n      {renderContent()}\n      \n      {/* Powered by AdMesh branding */}\n      {config.showPoweredBy !== false && (\n        <div className=\"flex justify-end mt-2\">\n          <span className=\"text-xs text-gray-400 dark:text-gray-500\">\n            Powered by AdMesh\n          </span>\n        </div>\n      )}\n    </div>\n  );\n};\n", "import React from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshChatMessageProps } from '../types/index';\nimport { AdMeshConversationalUnit } from './AdMeshConversationalUnit';\n\nexport const AdMeshChatMessage: React.FC<AdMeshChatMessageProps> = ({\n  message,\n  theme,\n  onRecommendationClick,\n  className\n}) => {\n  const isUser = message.role === 'user';\n  const isAssistant = message.role === 'assistant';\n\n  const messageClasses = classNames(\n    'admesh-chat-message',\n    'flex items-start gap-3',\n    {\n      'flex-row-reverse': isUser,\n    },\n    className\n  );\n\n  const bubbleClasses = classNames(\n    'max-w-xs lg:max-w-sm px-4 py-3 rounded-lg text-sm',\n    {\n      'bg-gradient-to-r from-blue-600 to-indigo-600 text-white': isUser,\n      'bg-gray-100 dark:bg-slate-800 text-gray-900 dark:text-gray-100': isAssistant,\n      'bg-yellow-100 dark:bg-yellow-900 text-yellow-900 dark:text-yellow-100': message.role === 'system',\n    }\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  const formatTime = (timestamp: Date) => {\n    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  };\n\n  return (\n    <div\n      className={messageClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Avatar */}\n      {!isUser && (\n        <div className=\"w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center flex-shrink-0\">\n          <svg className=\"w-4 h-4 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n          </svg>\n        </div>\n      )}\n\n      {isUser && (\n        <div className=\"w-8 h-8 bg-gray-300 dark:bg-slate-600 rounded-full flex items-center justify-center flex-shrink-0\">\n          <svg className=\"w-4 h-4 text-gray-600 dark:text-gray-300\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n          </svg>\n        </div>\n      )}\n\n      {/* Message Content */}\n      <div className={`flex flex-col ${isUser ? 'items-end' : 'items-start'} flex-1`}>\n        {/* Message Bubble */}\n        <div className={bubbleClasses}>\n          <div className=\"whitespace-pre-wrap break-words\">\n            {message.content}\n          </div>\n        </div>\n\n        {/* Timestamp */}\n        <div className={classNames(\n          'text-xs text-gray-500 dark:text-gray-400 mt-1',\n          { 'text-right': isUser }\n        )}>\n          {formatTime(message.timestamp)}\n        </div>\n\n        {/* Recommendations */}\n        {message.recommendations && message.recommendations.length > 0 && (\n          <div className=\"mt-3 w-full max-w-lg\">\n            {/* Recommendations Header */}\n            <div className=\"flex items-center gap-2 mb-3\">\n              <svg className=\"w-4 h-4 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n              </svg>\n              <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                {message.recommendations.length} recommendation{message.recommendations.length > 1 ? 's' : ''} found\n              </span>\n            </div>\n\n            {/* Recommendations Display */}\n            <AdMeshConversationalUnit\n              recommendations={message.recommendations}\n              config={{\n                displayMode: 'inline',\n                context: 'chat',\n                maxRecommendations: 3,\n                showPoweredBy: false,\n                autoShow: true,\n                delayMs: 300\n              }}\n              theme={theme}\n              onRecommendationClick={onRecommendationClick}\n              className=\"bg-gray-50 dark:bg-slate-800/50 rounded-lg p-3 border border-gray-200 dark:border-slate-700\"\n            />\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n", "import React, { useState, useRef, KeyboardEvent } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshChatInputProps } from '../types/index';\n\nexport const AdMeshChatInput: React.FC<AdMeshChatInputProps> = ({\n  placeholder = \"Type your message...\",\n  disabled = false,\n  suggestions = [],\n  theme,\n  onSendMessage,\n  className\n}) => {\n  const [message, setMessage] = useState('');\n  const [showSuggestions, setShowSuggestions] = useState(false);\n  const [filteredSuggestions, setFilteredSuggestions] = useState<string[]>([]);\n  const inputRef = useRef<HTMLTextAreaElement>(null);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\n    const value = e.target.value;\n    setMessage(value);\n\n    // Filter suggestions based on input\n    if (value.trim() && suggestions.length > 0) {\n      const filtered = suggestions.filter(suggestion =>\n        suggestion.toLowerCase().includes(value.toLowerCase())\n      );\n      setFilteredSuggestions(filtered);\n      setShowSuggestions(filtered.length > 0);\n    } else {\n      setShowSuggestions(false);\n    }\n\n    // Auto-resize textarea\n    if (inputRef.current) {\n      inputRef.current.style.height = 'auto';\n      inputRef.current.style.height = `${Math.min(inputRef.current.scrollHeight, 120)}px`;\n    }\n  };\n\n  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSend();\n    }\n  };\n\n  const handleSend = () => {\n    const trimmedMessage = message.trim();\n    if (trimmedMessage && !disabled && onSendMessage) {\n      onSendMessage(trimmedMessage);\n      setMessage('');\n      setShowSuggestions(false);\n      \n      // Reset textarea height\n      if (inputRef.current) {\n        inputRef.current.style.height = 'auto';\n      }\n    }\n  };\n\n  const handleSuggestionClick = (suggestion: string) => {\n    setMessage(suggestion);\n    setShowSuggestions(false);\n    if (inputRef.current) {\n      inputRef.current.focus();\n    }\n  };\n\n  const containerClasses = classNames(\n    'admesh-chat-input',\n    'relative',\n    className\n  );\n\n  const inputClasses = classNames(\n    'w-full resize-none rounded-lg border border-gray-300 dark:border-slate-600',\n    'bg-white dark:bg-slate-800 text-gray-900 dark:text-gray-100',\n    'placeholder-gray-500 dark:placeholder-gray-400',\n    'focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent',\n    'transition-all duration-200 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600',\n    'pr-12 pl-4 py-3 text-sm leading-5',\n    {\n      'opacity-50 cursor-not-allowed': disabled,\n    }\n  );\n\n  const sendButtonClasses = classNames(\n    'absolute right-2 bottom-2 p-2 rounded-lg transition-all duration-200',\n    'flex items-center justify-center',\n    {\n      'bg-blue-600 hover:bg-blue-700 text-white': message.trim() && !disabled,\n      'bg-gray-200 dark:bg-slate-700 text-gray-400 dark:text-gray-500 cursor-not-allowed': !message.trim() || disabled,\n    }\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Suggestions Dropdown */}\n      {showSuggestions && filteredSuggestions.length > 0 && (\n        <div className=\"absolute bottom-full left-0 right-0 mb-2 bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-lg shadow-lg max-h-40 overflow-y-auto z-10\">\n          {filteredSuggestions.slice(0, 5).map((suggestion, index) => (\n            <button\n              key={index}\n              onClick={() => handleSuggestionClick(suggestion)}\n              className=\"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors first:rounded-t-lg last:rounded-b-lg\"\n            >\n              {suggestion}\n            </button>\n          ))}\n        </div>\n      )}\n\n      {/* Input Container */}\n      <div className=\"relative\">\n        <textarea\n          ref={inputRef}\n          value={message}\n          onChange={handleInputChange}\n          onKeyDown={handleKeyDown}\n          placeholder={placeholder}\n          disabled={disabled}\n          rows={1}\n          className={inputClasses}\n          style={{ minHeight: '44px', maxHeight: '120px' }}\n        />\n\n        {/* Send Button */}\n        <button\n          onClick={handleSend}\n          disabled={!message.trim() || disabled}\n          className={sendButtonClasses}\n          aria-label=\"Send message\"\n        >\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n          </svg>\n        </button>\n      </div>\n\n      {/* Helper Text */}\n      <div className=\"flex items-center justify-between mt-2 text-xs text-gray-500 dark:text-gray-400\">\n        <span>Press Enter to send, Shift+Enter for new line</span>\n        <span className={classNames(\n          'transition-opacity duration-200',\n          { 'opacity-0': message.length < 100 }\n        )}>\n          {message.length}/500\n        </span>\n      </div>\n    </div>\n  );\n};\n", "import React, { useEffect, useRef } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshChatInterfaceProps } from '../types/index';\nimport { AdMeshChatMessage } from './AdMeshChatMessage';\nimport { AdMeshChatInput } from './AdMeshChatInput';\n\nexport const AdMeshChatInterface: React.FC<AdMeshChatInterfaceProps> = ({\n  messages,\n  config,\n  theme,\n  isLoading = false,\n  onSendMessage,\n  onRecommendationClick,\n  className\n}) => {\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n  const messagesContainerRef = useRef<HTMLDivElement>(null);\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\n    }\n  }, [messages]);\n\n  const containerClasses = classNames(\n    'admesh-chat-interface',\n    'flex flex-col h-full bg-white dark:bg-slate-900',\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  // Limit messages if maxMessages is set\n  const displayMessages = config.maxMessages \n    ? messages.slice(-config.maxMessages)\n    : messages;\n\n  return (\n    <div\n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Messages Area */}\n      <div \n        ref={messagesContainerRef}\n        className=\"flex-1 overflow-y-auto p-4 space-y-4 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600\"\n      >\n        {displayMessages.length === 0 ? (\n          <div className=\"flex flex-col items-center justify-center h-full text-center\">\n            <div className=\"w-16 h-16 bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900 dark:to-indigo-900 rounded-full flex items-center justify-center mb-4\">\n              <svg className=\"w-8 h-8 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n              </svg>\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2\">\n              Welcome to AdMesh AI\n            </h3>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 max-w-xs\">\n              Ask me anything about products, tools, or services. I'll provide personalized recommendations just for you!\n            </p>\n          </div>\n        ) : (\n          <>\n            {displayMessages.map((message) => (\n              <AdMeshChatMessage\n                key={message.id}\n                message={message}\n                theme={theme}\n                onRecommendationClick={onRecommendationClick}\n              />\n            ))}\n\n            {/* Typing Indicator */}\n            {isLoading && config.enableTypingIndicator !== false && (\n              <div className=\"flex items-start gap-3\">\n                <div className=\"w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center flex-shrink-0\">\n                  <svg className=\"w-4 h-4 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                  </svg>\n                </div>\n                <div className=\"bg-gray-100 dark:bg-slate-800 rounded-lg px-4 py-3 max-w-xs\">\n                  <div className=\"flex space-x-1\">\n                    <div className=\"w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce\"></div>\n                    <div className=\"w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                    <div className=\"w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            <div ref={messagesEndRef} />\n          </>\n        )}\n      </div>\n\n      {/* Quick Suggestions */}\n      {config.enableSuggestions && config.suggestions && config.suggestions.length > 0 && messages.length === 0 && (\n        <div className=\"px-4 pb-2\">\n          <div className=\"text-xs text-gray-500 dark:text-gray-400 mb-2\">Quick suggestions:</div>\n          <div className=\"flex flex-wrap gap-2\">\n            {config.suggestions.slice(0, 3).map((suggestion, index) => (\n              <button\n                key={index}\n                onClick={() => onSendMessage?.(suggestion)}\n                className=\"px-3 py-1.5 text-xs bg-gray-100 dark:bg-slate-800 hover:bg-gray-200 dark:hover:bg-slate-700 text-gray-700 dark:text-gray-300 rounded-full transition-colors\"\n              >\n                {suggestion}\n              </button>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Input Area */}\n      {config.showInputField !== false && onSendMessage && (\n        <div className=\"border-t border-gray-200 dark:border-slate-700 p-4\">\n          <AdMeshChatInput\n            placeholder={config.placeholder || \"Ask me about products, tools, or services...\"}\n            disabled={isLoading}\n            suggestions={config.suggestions}\n            theme={theme}\n            onSendMessage={onSendMessage}\n          />\n        </div>\n      )}\n    </div>\n  );\n};\n", "import React, { useState, useEffect } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshFloatingChatProps, ChatMessage } from '../types/index';\nimport { AdMeshChatInterface } from './AdMeshChatInterface';\n\nexport const AdMeshFloatingChat: React.FC<AdMeshFloatingChatProps> = ({\n  config,\n  theme,\n  title = 'AI Assistant',\n  subtitle = 'Get personalized recommendations',\n  isOpen: controlledIsOpen,\n  onToggle,\n  onSendMessage,\n  onRecommendationClick,\n  autoRecommendations,\n  autoRecommendationTrigger,\n  showInputField = true,\n  autoShowRecommendations = false,\n  onAutoRecommendationDismiss,\n  className\n}) => {\n  const [internalIsOpen, setInternalIsOpen] = useState(config.autoOpen || false);\n  const [messages, setMessages] = useState<ChatMessage[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [hasInteracted, setHasInteracted] = useState(false);\n\n  const isOpen = controlledIsOpen !== undefined ? controlledIsOpen : internalIsOpen;\n\n  // Initialize with welcome message\n  useEffect(() => {\n    if (config.showWelcomeMessage && config.welcomeMessage && messages.length === 0) {\n      const welcomeMessage: ChatMessage = {\n        id: 'welcome',\n        role: 'assistant',\n        content: config.welcomeMessage,\n        timestamp: new Date(),\n      };\n      setMessages([welcomeMessage]);\n    }\n  }, [config.showWelcomeMessage, config.welcomeMessage, messages.length]);\n\n  // Handle auto-recommendations\n  useEffect(() => {\n    if (autoRecommendations && autoRecommendations.length > 0 && autoShowRecommendations) {\n      const autoMessage: ChatMessage = {\n        id: `auto-${Date.now()}`,\n        role: 'assistant',\n        content: autoRecommendationTrigger\n          ? `Based on \"${autoRecommendationTrigger}\", here are some relevant recommendations:`\n          : 'I found some relevant recommendations for you:',\n        timestamp: new Date(),\n        recommendations: autoRecommendations,\n      };\n\n      // Auto-open the chat and show recommendations\n      if (controlledIsOpen === undefined) {\n        setInternalIsOpen(true);\n      }\n\n      // Add the auto-recommendation message\n      setMessages(prev => {\n        // Avoid duplicating auto-recommendations\n        const hasAutoMessage = prev.some(msg => msg.id.startsWith('auto-'));\n        if (hasAutoMessage) {\n          return prev.map(msg =>\n            msg.id.startsWith('auto-') ? autoMessage : msg\n          );\n        }\n        return [...prev, autoMessage];\n      });\n    }\n  }, [autoRecommendations, autoShowRecommendations, autoRecommendationTrigger, controlledIsOpen]);\n\n  const handleToggle = () => {\n    if (onToggle) {\n      onToggle();\n    } else {\n      setInternalIsOpen(!internalIsOpen);\n    }\n    setHasInteracted(true);\n  };\n\n  const handleSendMessage = async (messageContent: string) => {\n    if (!onSendMessage) return;\n\n    // Add user message\n    const userMessage: ChatMessage = {\n      id: `user-${Date.now()}`,\n      role: 'user',\n      content: messageContent,\n      timestamp: new Date(),\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setIsLoading(true);\n\n    try {\n      // Send message\n      await onSendMessage(messageContent);\n      // Note: The response should be handled by the parent component\n    } catch (error) {\n      console.error('Error sending message:', error);\n      const errorMessage: ChatMessage = {\n        id: `error-${Date.now()}`,\n        role: 'assistant',\n        content: 'Sorry, I encountered an error. Please try again.',\n        timestamp: new Date(),\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Get chat dimensions based on size\n  const getChatDimensions = () => {\n    switch (config.size) {\n      case 'sm': return 'w-80 h-96';\n      case 'md': return 'w-96 h-[32rem]';\n      case 'lg': return 'w-[28rem] h-[36rem]';\n      case 'xl': return 'w-[32rem] h-[40rem]';\n      default: return 'w-96 h-[32rem]';\n    }\n  };\n\n  // Get position classes\n  const getPositionClasses = () => {\n    switch (config.position) {\n      case 'bottom-right': return 'bottom-4 right-4';\n      case 'bottom-left': return 'bottom-4 left-4';\n      case 'top-right': return 'top-4 right-4';\n      case 'top-left': return 'top-4 left-4';\n      default: return 'bottom-4 right-4';\n    }\n  };\n\n  const containerClasses = classNames(\n    'admesh-floating-chat',\n    'fixed z-50 transition-all duration-300 ease-in-out',\n    getPositionClasses(),\n    className\n  );\n\n  const chatClasses = classNames(\n    'bg-white dark:bg-slate-900 rounded-lg shadow-2xl border border-gray-200 dark:border-slate-700 overflow-hidden',\n    getChatDimensions(),\n    {\n      'opacity-0 scale-95 pointer-events-none': !isOpen,\n      'opacity-100 scale-100': isOpen,\n    }\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Chat Interface */}\n      <div className={chatClasses}>\n        {isOpen && (\n          <>\n            {/* Header */}\n            <div className=\"flex items-center justify-between p-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center\">\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                  </svg>\n                </div>\n                <div>\n                  <h3 className=\"font-semibold text-sm\">{title}</h3>\n                  <p className=\"text-xs text-blue-100\">{subtitle}</p>\n                </div>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                {/* Dismiss auto-recommendations button */}\n                {autoRecommendations && autoRecommendations.length > 0 && onAutoRecommendationDismiss && (\n                  <button\n                    onClick={() => {\n                      onAutoRecommendationDismiss();\n                      setMessages(prev => prev.filter(msg => !msg.id.startsWith('auto-')));\n                    }}\n                    className=\"p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors\"\n                    aria-label=\"Dismiss recommendations\"\n                    title=\"Dismiss recommendations\"\n                  >\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\" />\n                    </svg>\n                  </button>\n                )}\n\n                <button\n                  onClick={handleToggle}\n                  className=\"p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors\"\n                  aria-label=\"Close chat\"\n                >\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n            </div>\n\n            {/* Chat Interface */}\n            <AdMeshChatInterface\n              messages={messages}\n              config={{\n                ...config,\n                showInputField: showInputField\n              }}\n              theme={theme}\n              isLoading={isLoading}\n              onSendMessage={showInputField ? handleSendMessage : () => {}}\n              onRecommendationClick={onRecommendationClick}\n              className=\"h-full\"\n            />\n          </>\n        )}\n      </div>\n\n      {/* Chat Toggle Button */}\n      {!isOpen && (\n        <button\n          onClick={handleToggle}\n          className={classNames(\n            'w-14 h-14 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700',\n            'text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200',\n            'flex items-center justify-center relative'\n          )}\n          aria-label=\"Open chat\"\n        >\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n          </svg>\n          \n          {/* Notification dot for new users */}\n          {!hasInteracted && (\n            <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse\"></div>\n          )}\n        </button>\n      )}\n\n      {/* Powered by AdMesh */}\n      {isOpen && (\n        <div className=\"absolute bottom-2 right-2 text-xs text-gray-400 dark:text-gray-500 bg-white dark:bg-slate-900 px-2 py-1 rounded shadow-sm\">\n          Powered by AdMesh\n        </div>\n      )}\n    </div>\n  );\n};\n", "import React, { useState, useEffect } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshSidebarHeaderProps } from '../types/index';\n\nexport const AdMeshSidebarHeader: React.FC<AdMeshSidebarHeaderProps> = ({\n  title,\n  theme,\n  collapsible = false,\n  isCollapsed = false,\n  onToggle,\n  onSearch,\n  showSearch = false,\n  className\n}) => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [isSearchFocused, setIsSearchFocused] = useState(false);\n  const [isMobile, setIsMobile] = useState(false);\n\n  // Mobile detection\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth < 640);\n    };\n\n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    return () => window.removeEventListener('resize', checkMobile);\n  }, []);\n\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const value = e.target.value;\n    setSearchQuery(value);\n    onSearch?.(value);\n  };\n\n  const handleSearchClear = () => {\n    setSearchQuery('');\n    onSearch?.('');\n  };\n\n  const headerClasses = classNames(\n    'admesh-sidebar-header',\n    'flex flex-col p-4 border-b border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-800',\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={headerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Title and Toggle */}\n      <div className=\"flex items-center justify-between mb-3\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 truncate\">\n          {title}\n        </h3>\n\n        <div className=\"flex items-center gap-2\">\n          {/* Mobile close button */}\n          {isMobile && onToggle && (\n            <button\n              onClick={onToggle}\n              className=\"p-1.5 rounded-lg hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors flex-shrink-0 sm:hidden\"\n              title=\"Close sidebar\"\n            >\n              <svg\n                className=\"w-4 h-4 text-gray-600 dark:text-gray-400\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          )}\n\n          {/* Desktop collapse button */}\n          {collapsible && (\n            <button\n              onClick={onToggle}\n              className=\"p-1.5 rounded-lg hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors flex-shrink-0 hidden sm:block\"\n              title={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}\n            >\n              <svg\n                className={classNames(\n                  'w-4 h-4 text-gray-600 dark:text-gray-400 transition-transform duration-200',\n                  { 'rotate-180': isCollapsed }\n                )}\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n              </svg>\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* Search Bar */}\n      {showSearch && !isCollapsed && (\n        <div className=\"relative\">\n          <div className={classNames(\n            'relative flex items-center transition-all duration-200',\n            {\n              'ring-2 ring-blue-500 dark:ring-blue-400': isSearchFocused,\n            }\n          )}>\n            {/* Search Icon */}\n            <div className=\"absolute left-3 pointer-events-none\">\n              <svg className=\"w-4 h-4 text-gray-400 dark:text-gray-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n              </svg>\n            </div>\n\n            {/* Search Input */}\n            <input\n              type=\"text\"\n              value={searchQuery}\n              onChange={handleSearchChange}\n              onFocus={() => setIsSearchFocused(true)}\n              onBlur={() => setIsSearchFocused(false)}\n              placeholder=\"Search recommendations...\"\n              className={classNames(\n                'w-full pl-10 pr-10 py-2 text-sm bg-white dark:bg-slate-900 border border-gray-300 dark:border-slate-600 rounded-lg',\n                'placeholder-gray-400 dark:placeholder-gray-500 text-gray-900 dark:text-gray-100',\n                'focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent',\n                'transition-all duration-200'\n              )}\n            />\n\n            {/* Clear Button */}\n            {searchQuery && (\n              <button\n                onClick={handleSearchClear}\n                className=\"absolute right-3 p-0.5 rounded-full hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors\"\n                title=\"Clear search\"\n              >\n                <svg className=\"w-3 h-3 text-gray-400 dark:text-gray-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                </svg>\n              </button>\n            )}\n          </div>\n\n          {/* Search Results Count */}\n          {searchQuery && (\n            <div className=\"mt-2 text-xs text-gray-500 dark:text-gray-400\">\n              Search results will be filtered in real-time\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Quick Stats */}\n      {!isCollapsed && (\n        <div className=\"mt-3 flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400\">\n          <div className=\"flex items-center gap-1\">\n            <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n            <span>Live recommendations</span>\n          </div>\n          <div className=\"flex items-center gap-1\">\n            <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n            </svg>\n            <span>AI-powered</span>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n", "import React, { useState } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshSidebarContentProps } from '../types/index';\nimport { AdMeshInlineRecommendation } from './AdMeshInlineRecommendation';\nimport { AdMeshProductCard } from './AdMeshProductCard';\n\nexport const AdMeshSidebarContent: React.FC<AdMeshSidebarContentProps> = ({\n  recommendations,\n  displayMode,\n  theme,\n  maxRecommendations,\n  onRecommendationClick,\n  className\n}) => {\n  const [showFilters, setShowFilters] = useState(false);\n  const [activeTab, setActiveTab] = useState<'all' | 'top' | 'recent'>('all');\n\n  const displayRecommendations = maxRecommendations \n    ? recommendations.slice(0, maxRecommendations)\n    : recommendations;\n\n  const getTabRecommendations = () => {\n    switch (activeTab) {\n      case 'top':\n        return displayRecommendations\n          .filter(rec => rec.intent_match_score >= 0.8)\n          .slice(0, 5);\n      case 'recent':\n        return displayRecommendations.slice(0, 3);\n      default:\n        return displayRecommendations;\n    }\n  };\n\n  const tabRecommendations = getTabRecommendations();\n\n  const contentClasses = classNames(\n    'admesh-sidebar-content',\n    'flex flex-col h-full',\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  const renderRecommendations = () => {\n    if (tabRecommendations.length === 0) {\n      return (\n        <div className=\"flex-1 flex flex-col items-center justify-center p-6 text-center\">\n          <div className=\"w-16 h-16 bg-gray-100 dark:bg-slate-800 rounded-full flex items-center justify-center mb-4\">\n            <svg className=\"w-8 h-8 text-gray-400 dark:text-gray-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n            </svg>\n          </div>\n          <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n            No recommendations found\n          </h4>\n          <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n            Try adjusting your search or filters\n          </p>\n        </div>\n      );\n    }\n\n    switch (displayMode) {\n      case 'recommendations':\n        return (\n          <div className=\"space-y-3\">\n            {tabRecommendations.map((recommendation, index) => (\n              <AdMeshInlineRecommendation\n                key={recommendation.ad_id || index}\n                recommendation={recommendation}\n                theme={theme}\n                compact={true}\n                showReason={true}\n                onClick={onRecommendationClick}\n              />\n            ))}\n          </div>\n        );\n\n      case 'history':\n        return (\n          <div className=\"space-y-2\">\n            {tabRecommendations.map((recommendation, index) => (\n              <div key={recommendation.ad_id || index} className=\"flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-800 transition-colors\">\n                <div className=\"w-2 h-2 bg-gray-400 rounded-full flex-shrink-0\"></div>\n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\">\n                    {recommendation.title}\n                  </div>\n                  <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    {Math.round(recommendation.intent_match_score * 100)}% match\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        );\n\n      case 'favorites':\n        return (\n          <div className=\"space-y-3\">\n            {tabRecommendations.slice(0, 3).map((recommendation, index) => (\n              <div key={recommendation.ad_id || index} className=\"relative\">\n                <AdMeshInlineRecommendation\n                  recommendation={recommendation}\n                  theme={theme}\n                  compact={true}\n                  showReason={false}\n                  onClick={onRecommendationClick}\n                />\n                <button className=\"absolute top-2 right-2 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors\">\n                  <svg className=\"w-3 h-3 text-yellow-500\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\" />\n                  </svg>\n                </button>\n              </div>\n            ))}\n          </div>\n        );\n\n      case 'mixed':\n        return (\n          <div className=\"space-y-4\">\n            {/* Top recommendation as card */}\n            {tabRecommendations[0] && (\n              <div>\n                <h4 className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2\">\n                  Top Pick\n                </h4>\n                <AdMeshProductCard\n                  recommendation={tabRecommendations[0]}\n                  theme={theme}\n                  showMatchScore={true}\n                  showBadges={true}\n                  onClick={onRecommendationClick}\n                  className=\"text-xs\"\n                />\n              </div>\n            )}\n\n            {/* Other recommendations as inline */}\n            {tabRecommendations.slice(1).length > 0 && (\n              <div>\n                <h4 className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2\">\n                  More Options\n                </h4>\n                <div className=\"space-y-2\">\n                  {tabRecommendations.slice(1, 4).map((recommendation, index) => (\n                    <AdMeshInlineRecommendation\n                      key={recommendation.ad_id || index}\n                      recommendation={recommendation}\n                      theme={theme}\n                      compact={true}\n                      showReason={false}\n                      onClick={onRecommendationClick}\n                    />\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n        );\n\n      default:\n        return (\n          <div className=\"space-y-3\">\n            {tabRecommendations.map((recommendation, index) => (\n              <AdMeshInlineRecommendation\n                key={recommendation.ad_id || index}\n                recommendation={recommendation}\n                theme={theme}\n                compact={true}\n                showReason={true}\n                onClick={onRecommendationClick}\n              />\n            ))}\n          </div>\n        );\n    }\n  };\n\n  return (\n    <div\n      className={contentClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Tabs */}\n      <div className=\"flex border-b border-gray-200 dark:border-slate-700 bg-white dark:bg-slate-900\">\n        <button\n          onClick={() => setActiveTab('all')}\n          className={classNames(\n            'flex-1 px-3 py-2 text-xs font-medium transition-colors',\n            activeTab === 'all'\n              ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'\n              : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\n          )}\n        >\n          All ({recommendations.length})\n        </button>\n        <button\n          onClick={() => setActiveTab('top')}\n          className={classNames(\n            'flex-1 px-3 py-2 text-xs font-medium transition-colors',\n            activeTab === 'top'\n              ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'\n              : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\n          )}\n        >\n          Top\n        </button>\n        <button\n          onClick={() => setActiveTab('recent')}\n          className={classNames(\n            'flex-1 px-3 py-2 text-xs font-medium transition-colors',\n            activeTab === 'recent'\n              ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'\n              : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\n          )}\n        >\n          Recent\n        </button>\n      </div>\n\n      {/* Content */}\n      <div className=\"flex-1 overflow-y-auto p-4 min-h-0\" style={{\n        WebkitOverflowScrolling: 'touch', // Smooth scrolling on iOS\n        overscrollBehavior: 'contain' // Prevent scroll chaining on mobile\n      }}>\n        {renderRecommendations()}\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"p-3 border-t border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-800\">\n        <div className=\"flex items-center justify-between text-xs\">\n          <span className=\"text-gray-500 dark:text-gray-400\">\n            {tabRecommendations.length} recommendation{tabRecommendations.length !== 1 ? 's' : ''}\n          </span>\n          <button\n            onClick={() => setShowFilters(!showFilters)}\n            className=\"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors\"\n          >\n            Filters\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n", "import React, { useState, useEffect, useMemo } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshSidebarProps, SidebarFilters } from '../types/index';\nimport { AdMeshSidebarHeader } from './AdMeshSidebarHeader';\nimport { AdMeshSidebarContent } from './AdMeshSidebarContent';\n\nexport const AdMeshSidebar: React.FC<AdMeshSidebarProps> = ({\n  recommendations,\n  config,\n  theme,\n  title = 'Recommendations',\n  isOpen = true,\n  onToggle,\n  onRecommendationClick,\n  onSearch,\n  // onFilter,\n  className,\n  containerMode = false // New prop for demo/container integration\n}) => {\n  const [isCollapsed, setIsCollapsed] = useState(config.defaultCollapsed || false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [filters] = useState<SidebarFilters>({});\n  const [isMobile, setIsMobile] = useState(false);\n\n  // Mobile detection\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth < 640);\n    };\n\n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    return () => window.removeEventListener('resize', checkMobile);\n  }, []);\n\n  // Prevent body scroll on mobile when sidebar is open\n  useEffect(() => {\n    if (isMobile && isOpen && !isCollapsed && !containerMode) {\n      const originalStyle = window.getComputedStyle(document.body).overflow;\n      document.body.style.overflow = 'hidden';\n      document.body.style.position = 'fixed';\n      document.body.style.width = '100%';\n\n      return () => {\n        document.body.style.overflow = originalStyle;\n        document.body.style.position = '';\n        document.body.style.width = '';\n      };\n    }\n  }, [isMobile, isOpen, isCollapsed, containerMode]);\n\n  // Handle auto-refresh if enabled\n  useEffect(() => {\n    if (config.autoRefresh && config.refreshInterval) {\n      const interval = setInterval(() => {\n        // Trigger a refresh - in a real app this would refetch recommendations\n        console.log('Auto-refreshing recommendations...');\n      }, config.refreshInterval);\n\n      return () => clearInterval(interval);\n    }\n  }, [config.autoRefresh, config.refreshInterval]);\n\n  // Filter recommendations based on search and filters\n  const filteredRecommendations = useMemo(() => {\n    let filtered = [...recommendations];\n\n    // Apply search filter\n    if (searchQuery.trim()) {\n      const query = searchQuery.toLowerCase();\n      filtered = filtered.filter(rec => \n        rec.title.toLowerCase().includes(query) ||\n        rec.reason.toLowerCase().includes(query) ||\n        rec.keywords?.some(keyword => keyword.toLowerCase().includes(query))\n      );\n    }\n\n    // Apply category filter\n    if (filters.categories && filters.categories.length > 0) {\n      filtered = filtered.filter(rec => \n        rec.categories?.some(cat => filters.categories?.includes(cat))\n      );\n    }\n\n    // Apply free tier filter\n    if (filters.hasFreeTier) {\n      // Note: has_free_tier property not available in current type definition\n      // This filter is disabled until the property is added to AdMeshRecommendation\n      // filtered = filtered.filter(rec => rec.has_free_tier);\n    }\n\n    // Apply trial filter\n    if (filters.hasTrial) {\n      filtered = filtered.filter(rec => rec.trial_days && rec.trial_days > 0);\n    }\n\n    // Apply minimum match score filter\n    if (filters.minMatchScore !== undefined) {\n      filtered = filtered.filter(rec => rec.intent_match_score >= filters.minMatchScore!);\n    }\n\n    // Sort by match score (highest first)\n    filtered.sort((a, b) => b.intent_match_score - a.intent_match_score);\n\n    // Limit results\n    if (config.maxRecommendations) {\n      filtered = filtered.slice(0, config.maxRecommendations);\n    }\n\n    return filtered;\n  }, [recommendations, searchQuery, filters, config.maxRecommendations]);\n\n  const handleToggle = () => {\n    if (config.collapsible) {\n      setIsCollapsed(!isCollapsed);\n      onToggle?.();\n    }\n  };\n\n  const handleSearch = (query: string) => {\n    setSearchQuery(query);\n    onSearch?.(query);\n  };\n\n  // const handleFilter = (newFilters: SidebarFilters) => {\n  //   setFilters(newFilters);\n  //   onFilter?.(newFilters);\n  // };\n\n  // Get sidebar width based on size with mobile responsiveness\n  const getSidebarWidth = () => {\n    if (isCollapsed) return 'w-12';\n\n    // On mobile, always use full width with proper constraints\n    switch (config.size) {\n      case 'sm': return 'w-full sm:w-64 max-w-[90vw] sm:max-w-sm';\n      case 'md': return 'w-full sm:w-80 max-w-[90vw] sm:max-w-md';\n      case 'lg': return 'w-full sm:w-96 max-w-[90vw] sm:max-w-lg';\n      case 'xl': return 'w-full sm:w-[28rem] max-w-[90vw] sm:max-w-xl';\n      default: return 'w-full sm:w-80 max-w-[90vw] sm:max-w-md';\n    }\n  };\n\n  const sidebarClasses = classNames(\n    'admesh-sidebar',\n    'flex flex-col bg-white dark:bg-slate-900 border-gray-200 dark:border-slate-700 shadow-lg transition-all duration-300 ease-in-out',\n    getSidebarWidth(),\n    {\n      'border-r': config.position === 'left',\n      'border-l': config.position === 'right',\n      // Use fixed positioning for full-screen mode, relative for container mode\n      // Improved mobile positioning with proper viewport handling\n      'fixed top-0 bottom-0 z-[9999]': !containerMode,\n      'relative h-full': containerMode,\n      'left-0': config.position === 'left' && !containerMode,\n      'right-0': config.position === 'right' && !containerMode,\n      // Better mobile transform handling\n      'transform -translate-x-full': config.position === 'left' && !isOpen && !containerMode,\n      'transform translate-x-full': config.position === 'right' && !isOpen && !containerMode,\n      // Mobile-specific improvements\n      'min-h-0': true, // Prevent height issues on mobile\n      'overflow-hidden': !containerMode, // Prevent scroll issues\n    },\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  if (!isOpen && !config.collapsible) {\n    return null;\n  }\n\n  return (\n    <>\n      {/* Overlay for mobile - show in both modes on small screens */}\n      {isOpen && !isCollapsed && (\n        <div\n          className={classNames(\n            \"bg-black bg-opacity-50 z-[9998] sm:hidden transition-opacity duration-300\",\n            containerMode ? \"absolute inset-0\" : \"fixed inset-0\"\n          )}\n          onClick={() => onToggle?.()}\n          style={{\n            // Ensure overlay covers the entire viewport on mobile\n            position: containerMode ? 'absolute' : 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            touchAction: 'none', // Prevent scrolling behind overlay\n          }}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div\n        className={sidebarClasses}\n        style={containerStyle}\n        data-admesh-theme={theme?.mode}\n        data-sidebar-position={config.position}\n        data-sidebar-size={config.size}\n        data-mobile-open={isMobile && isOpen && !isCollapsed ? 'true' : 'false'}\n        data-container-mode={containerMode ? 'true' : 'false'}\n      >\n        {/* Header */}\n        {config.showHeader !== false && (\n          <AdMeshSidebarHeader\n            title={title}\n            theme={theme}\n            collapsible={config.collapsible}\n            isCollapsed={isCollapsed}\n            onToggle={handleToggle}\n            onSearch={config.showSearch ? handleSearch : undefined}\n            showSearch={config.showSearch && !isCollapsed}\n          />\n        )}\n\n        {/* Content */}\n        {!isCollapsed && (\n          <AdMeshSidebarContent\n            recommendations={filteredRecommendations}\n            displayMode={config.displayMode}\n            theme={theme}\n            maxRecommendations={config.maxRecommendations}\n            onRecommendationClick={onRecommendationClick}\n            className=\"flex-1 overflow-hidden min-h-0\"\n          />\n        )}\n\n        {/* Collapsed state indicator */}\n        {isCollapsed && config.collapsible && (\n          <div className=\"flex-1 flex flex-col items-center justify-center p-2\">\n            <button\n              onClick={handleToggle}\n              className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-800 transition-colors\"\n              title=\"Expand sidebar\"\n            >\n              <svg className=\"w-5 h-5 text-gray-600 dark:text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n              </svg>\n            </button>\n            <div className=\"mt-4 text-xs text-gray-500 dark:text-gray-400 transform -rotate-90 whitespace-nowrap\">\n              {filteredRecommendations.length}\n            </div>\n          </div>\n        )}\n\n        {/* Powered by AdMesh */}\n        {!isCollapsed && (\n          <div className=\"p-3 border-t border-gray-200 dark:border-slate-700\">\n            <div className=\"text-xs text-gray-400 dark:text-gray-500 text-center\">\n              Powered by AdMesh\n            </div>\n          </div>\n        )}\n      </div>\n    </>\n  );\n};\n", "import React, { useState, useEffect } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshRecommendation, AdMeshTheme } from '../types/index';\nimport { AdMeshConversationalUnit } from './AdMeshConversationalUnit';\n\nexport interface AdMeshAutoRecommendationWidgetProps {\n  recommendations: AdMeshRecommendation[];\n  trigger?: string; // The query/context that triggered recommendations\n  theme?: AdMeshTheme;\n  title?: string;\n  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';\n  size?: 'sm' | 'md' | 'lg';\n  autoShow?: boolean;\n  showDelay?: number; // Delay before showing (ms)\n  onRecommendationClick?: (adId: string, admeshLink: string) => void;\n  onDismiss?: () => void;\n  className?: string;\n}\n\nexport const AdMeshAutoRecommendationWidget: React.FC<AdMeshAutoRecommendationWidgetProps> = ({\n  recommendations,\n  trigger,\n  theme,\n  title = 'AI Recommendations',\n  position = 'bottom-right',\n  size = 'md',\n  autoShow = true,\n  showDelay = 1000,\n  onRecommendationClick,\n  onDismiss,\n  className\n}) => {\n  const [isVisible, setIsVisible] = useState(false);\n  const [hasAnimated, setHasAnimated] = useState(false);\n\n  // Auto-show with delay\n  useEffect(() => {\n    if (autoShow && recommendations.length > 0) {\n      const timer = setTimeout(() => {\n        setIsVisible(true);\n        setHasAnimated(true);\n      }, showDelay);\n\n      return () => clearTimeout(timer);\n    }\n  }, [autoShow, recommendations.length, showDelay]);\n\n  const handleDismiss = () => {\n    setIsVisible(false);\n    onDismiss?.();\n  };\n\n  // Get widget dimensions based on size\n  const getWidgetDimensions = () => {\n    switch (size) {\n      case 'sm': return 'w-72 max-h-80';\n      case 'md': return 'w-80 max-h-96';\n      case 'lg': return 'w-96 max-h-[28rem]';\n      default: return 'w-80 max-h-96';\n    }\n  };\n\n  // Get position classes\n  const getPositionClasses = () => {\n    switch (position) {\n      case 'bottom-right': return 'bottom-4 right-4';\n      case 'bottom-left': return 'bottom-4 left-4';\n      case 'top-right': return 'top-4 right-4';\n      case 'top-left': return 'top-4 left-4';\n      default: return 'bottom-4 right-4';\n    }\n  };\n\n  if (!isVisible || recommendations.length === 0) {\n    return null;\n  }\n\n  const containerClasses = classNames(\n    'admesh-auto-recommendation-widget',\n    'fixed z-50 transition-all duration-500 ease-out',\n    getPositionClasses(),\n    getWidgetDimensions(),\n    {\n      'opacity-0 scale-95 translate-y-2': !hasAnimated,\n      'opacity-100 scale-100 translate-y-0': hasAnimated,\n    },\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      <div className=\"bg-white dark:bg-slate-900 rounded-lg shadow-2xl border border-gray-200 dark:border-slate-700 overflow-hidden\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center\">\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n              </svg>\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-sm\">{title}</h3>\n              {trigger && (\n                <p className=\"text-xs text-blue-100 truncate max-w-48\">\n                  Based on: \"{trigger}\"\n                </p>\n              )}\n            </div>\n          </div>\n          <button\n            onClick={handleDismiss}\n            className=\"p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors\"\n            aria-label=\"Dismiss recommendations\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-4 max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600\">\n          {/* Recommendations count */}\n          <div className=\"flex items-center gap-2 mb-3\">\n            <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n            <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n              {recommendations.length} intelligent match{recommendations.length > 1 ? 'es' : ''} found\n            </span>\n          </div>\n\n          {/* Recommendations */}\n          <AdMeshConversationalUnit\n            recommendations={recommendations}\n            config={{\n              displayMode: 'inline',\n              context: 'assistant',\n              maxRecommendations: 3,\n              showPoweredBy: false,\n              autoShow: true,\n              delayMs: 200\n            }}\n            theme={theme}\n            onRecommendationClick={onRecommendationClick}\n          />\n        </div>\n\n        {/* Footer */}\n        <div className=\"px-4 py-3 bg-gray-50 dark:bg-slate-800 border-t border-gray-200 dark:border-slate-700\">\n          <div className=\"flex items-center justify-between\">\n            <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n              Powered by AdMesh\n            </span>\n            <button\n              onClick={handleDismiss}\n              className=\"text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors\"\n            >\n              Dismiss\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n", "import type { AdMeshTheme } from '../types';\n\n/**\n * Utility functions for theme customization in AdMesh UI SDK\n */\n\n/**\n * Creates a theme with sensible defaults and custom overrides\n */\nexport const createAdMeshTheme = (customTheme: Partial<AdMeshTheme> = {}): AdMeshTheme => {\n  const baseTheme: AdMeshTheme = {\n    mode: 'light',\n    primaryColor: '#3b82f6',\n    secondaryColor: '#10b981',\n    accentColor: '#3b82f6',\n    backgroundColor: '#ffffff',\n    surfaceColor: '#f9fafb',\n    borderColor: '#e5e7eb',\n    textColor: '#111827',\n    textSecondaryColor: '#6b7280',\n    fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',\n    fontSize: {\n      small: '12px',\n      base: '14px',\n      large: '16px',\n      title: '18px'\n    },\n    borderRadius: '8px',\n    spacing: {\n      small: '4px',\n      medium: '8px',\n      large: '16px'\n    },\n    shadows: {\n      small: '0 1px 3px rgba(0, 0, 0, 0.1)',\n      medium: '0 4px 6px rgba(0, 0, 0, 0.1)',\n      large: '0 10px 15px rgba(0, 0, 0, 0.1)'\n    },\n    icons: {\n      expandIcon: '▼',\n      collapseIcon: '▲',\n      starIcon: '★',\n      checkIcon: '✓',\n      arrowIcon: '→'\n    }\n  };\n\n  return {\n    ...baseTheme,\n    ...customTheme,\n    fontSize: {\n      ...baseTheme.fontSize,\n      ...customTheme.fontSize\n    },\n    spacing: {\n      ...baseTheme.spacing,\n      ...customTheme.spacing\n    },\n    shadows: {\n      ...baseTheme.shadows,\n      ...customTheme.shadows\n    },\n    icons: {\n      ...baseTheme.icons,\n      ...customTheme.icons\n    },\n    components: {\n      ...baseTheme.components,\n      ...customTheme.components\n    }\n  };\n};\n\n/**\n * Creates a dark theme variant\n */\nexport const createDarkTheme = (customTheme: Partial<AdMeshTheme> = {}): AdMeshTheme => {\n  const darkDefaults: Partial<AdMeshTheme> = {\n    mode: 'dark',\n    backgroundColor: '#1f2937',\n    surfaceColor: '#374151',\n    borderColor: '#4b5563',\n    textColor: '#f9fafb',\n    textSecondaryColor: '#9ca3af',\n    shadows: {\n      small: '0 1px 3px rgba(0, 0, 0, 0.3)',\n      medium: '0 4px 6px rgba(0, 0, 0, 0.3)',\n      large: '0 10px 15px rgba(0, 0, 0, 0.3)'\n    }\n  };\n\n  return createAdMeshTheme({\n    ...darkDefaults,\n    ...customTheme\n  });\n};\n\n/**\n * Predefined theme presets for common AI platforms\n */\nexport const themePresets = {\n  // Clean, minimal theme\n  minimal: createAdMeshTheme({\n    primaryColor: '#000000',\n    secondaryColor: '#666666',\n    borderRadius: '4px',\n    shadows: {\n      small: 'none',\n      medium: '0 1px 3px rgba(0, 0, 0, 0.1)',\n      large: '0 2px 6px rgba(0, 0, 0, 0.1)'\n    }\n  }),\n\n  // Modern, colorful theme\n  vibrant: createAdMeshTheme({\n    primaryColor: '#8b5cf6',\n    secondaryColor: '#06b6d4',\n    accentColor: '#f59e0b',\n    borderRadius: '12px',\n    gradients: {\n      primary: 'linear-gradient(135deg, #8b5cf6 0%, #06b6d4 100%)',\n      secondary: 'linear-gradient(135deg, #06b6d4 0%, #10b981 100%)',\n      accent: 'linear-gradient(135deg, #f59e0b 0%, #ef4444 100%)'\n    }\n  }),\n\n  // Professional, corporate theme\n  corporate: createAdMeshTheme({\n    primaryColor: '#1e40af',\n    secondaryColor: '#059669',\n    backgroundColor: '#f8fafc',\n    surfaceColor: '#ffffff',\n    borderColor: '#cbd5e1',\n    borderRadius: '6px',\n    fontFamily: '\"Inter\", -apple-system, BlinkMacSystemFont, sans-serif'\n  }),\n\n  // High contrast theme for accessibility\n  highContrast: createAdMeshTheme({\n    primaryColor: '#000000',\n    secondaryColor: '#ffffff',\n    backgroundColor: '#ffffff',\n    surfaceColor: '#f5f5f5',\n    borderColor: '#000000',\n    textColor: '#000000',\n    textSecondaryColor: '#333333',\n    borderRadius: '0px',\n    shadows: {\n      small: 'none',\n      medium: '0 0 0 2px #000000',\n      large: '0 0 0 3px #000000'\n    }\n  })\n};\n\n/**\n * Utility to merge multiple theme objects\n */\nexport const mergeThemes = (...themes: Partial<AdMeshTheme>[]): AdMeshTheme => {\n  const baseTheme = createAdMeshTheme();\n  return themes.reduce((merged, theme) => {\n    if (!theme) return merged;\n    return createAdMeshTheme({\n      ...merged,\n      ...theme\n    });\n  }, baseTheme);\n};\n\n/**\n * Utility to create a theme from CSS custom properties\n */\nexport const themeFromCSSProperties = (element: HTMLElement): Partial<AdMeshTheme> => {\n  const computedStyle = getComputedStyle(element);\n  \n  return {\n    primaryColor: computedStyle.getPropertyValue('--admesh-primary-color')?.trim() || undefined,\n    secondaryColor: computedStyle.getPropertyValue('--admesh-secondary-color')?.trim() || undefined,\n    backgroundColor: computedStyle.getPropertyValue('--admesh-background-color')?.trim() || undefined,\n    textColor: computedStyle.getPropertyValue('--admesh-text-color')?.trim() || undefined,\n    borderRadius: computedStyle.getPropertyValue('--admesh-border-radius')?.trim() || undefined,\n    fontFamily: computedStyle.getPropertyValue('--admesh-font-family')?.trim() || undefined\n  };\n};\n", "// AdMesh UI SDK - Main Entry Point\n\n// Export all components\nexport {\n  AdMeshProductCard,\n  AdMeshCompareTable,\n  AdMeshBadge,\n\n  AdMeshLinkTracker,\n\n  AdMeshExpandableUnit,\n  AdMeshConversationSummary,\n  AdMeshCitationUnit,\n  AdMeshInlineRecommendation,\n  AdMeshConversationalUnit,\n  AdMeshCitationReference,\n  AdMeshFloatingChat,\n  AdMeshChatInterface,\n  AdMeshChatMessage,\n  AdMeshChatInput,\n  AdMeshSidebar,\n  AdMeshSidebarHeader,\n  AdMeshSidebarContent,\n  AdMeshAutoRecommendationWidget\n} from './components';\n\n// Export hooks\nexport {\n  useAdMeshTracker,\n  setAdMeshTrackerConfig,\n  buildAdMeshLink,\n  extractTrackingData\n} from './hooks/useAdMeshTracker';\n\nexport {\n  useAdMeshStyles\n} from './hooks/useAdMeshStyles';\n\n// Export theme utilities\nexport {\n  createAdMeshTheme,\n  createDarkTheme,\n  themePresets,\n  mergeThemes,\n  themeFromCSSProperties\n} from './utils/themeUtils';\n\n// Export disclosure utilities\nexport {\n  getRecommendationLabel,\n  getLabelTooltip,\n  getSectionDisclosure,\n  getInlineDisclosure,\n  getInlineTooltip,\n  getBadgeText,\n  getCtaText,\n  hasHighQualityMatches,\n  getPoweredByText\n} from './utils/disclosureUtils';\n\nexport type {\n  DisclosureConfig\n} from './utils/disclosureUtils';\n\n// Export types\nexport type {\n  AdMeshRecommendation,\n  AdMeshTheme,\n  IntentType,\n  BadgeType,\n  BadgeVariant,\n  BadgeSize,\n  TrackingData,\n  AdMeshProductCardProps,\n  AdMeshCompareTableProps,\n  AdMeshBadgeProps,\n\n  AdMeshLinkTrackerProps,\n\n  UseAdMeshTrackerReturn,\n  AgentRecommendationResponse,\n  AdMeshConfig,\n  ConversationalDisplayMode,\n  ConversationContext,\n  ConversationalAdConfig,\n  AdMeshConversationSummaryProps,\n  AdMeshCitationUnitProps,\n  AdMeshInlineRecommendationProps,\n  AdMeshChatInputProps,\n  AdMeshChatMessageProps,\n  AdMeshChatInterfaceProps,\n  AdMeshFloatingChatProps,\n  AdMeshCitationReferenceProps,\n  AdMeshConversationalUnitProps,\n  ChatMessage,\n  SidebarPosition,\n  SidebarSize,\n  SidebarDisplayMode,\n  AdMeshSidebarConfig,\n  AdMeshSidebarProps,\n  SidebarFilters,\n  AdMeshSidebarHeaderProps,\n  AdMeshSidebarContentProps,\n\n} from './types/index';\n\n// Version info\nexport const VERSION = '0.2.1';\n\n// Default configuration\nexport const DEFAULT_CONFIG = {\n  trackingEnabled: true,\n  debug: false,\n  theme: {\n    mode: 'light' as const,\n    accentColor: '#2563eb'\n  }\n};\n"], "names": ["REACT_ELEMENT_TYPE", "REACT_FRAGMENT_TYPE", "jsxProd", "type", "config", "<PERSON><PERSON><PERSON>", "key", "propName", "reactJsxRuntime_production", "getComponentNameFromType", "REACT_CLIENT_REFERENCE", "REACT_PROFILER_TYPE", "REACT_STRICT_MODE_TYPE", "REACT_SUSPENSE_TYPE", "REACT_SUSPENSE_LIST_TYPE", "REACT_ACTIVITY_TYPE", "REACT_PORTAL_TYPE", "REACT_CONTEXT_TYPE", "REACT_CONSUMER_TYPE", "REACT_FORWARD_REF_TYPE", "innerType", "REACT_MEMO_TYPE", "REACT_LAZY_TYPE", "testStringCoercion", "value", "checkKeyStringCoercion", "JSCompiler_inline_result", "JSCompiler_temp_const", "JSCompiler_inline_result$jscomp$0", "getTaskName", "name", "get<PERSON>wner", "dispatcher", "ReactSharedInternals", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasOwnProperty", "getter", "defineKeyPropWarningGetter", "props", "displayName", "warnAboutAccessingKey", "specialPropKeyWarningShown", "elementRefGetterWithDeprecationWarning", "componentName", "didWarnAboutElementRef", "ReactElement", "self", "source", "owner", "debugStack", "debugTask", "jsxDEVImpl", "isStaticChildren", "children", "isArrayImpl", "validate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keys", "k", "didWarnAboutKeySpread", "node", "React", "require$$0", "createTask", "callStackForError", "unknownOwnerDebugStack", "unknownOwnerDebugTask", "reactJsxRuntime_development", "trackActualOwner", "jsxRuntimeModule", "require$$1", "hasOwn", "classNames", "classes", "i", "arg", "appendClass", "parseValue", "newClass", "module", "DEFAULT_TRACKING_URL", "globalConfig", "setAdMeshTrackerConfig", "useAdMeshTracker", "isTracking", "setIsTracking", "useState", "error", "setError", "mergedConfig", "useMemo", "log", "useCallback", "message", "data", "sendTrackingEvent", "eventType", "errorMsg", "payload", "lastError", "attempt", "response", "result", "err", "resolve", "trackClick", "trackView", "trackConversion", "buildAdMeshLink", "baseLink", "adId", "additionalParams", "url", "extractTrackingData", "recommendation", "additionalData", "AdMeshLinkTracker", "admeshLink", "productId", "trackingData", "className", "style", "elementRef", "useRef", "hasTrackedView", "useEffect", "observer", "entries", "entry", "handleClick", "event", "jsx", "ADMESH_STYLES", "stylesInjected", "useAdMeshStyles", "styleElement", "existingStyle", "getRecommendationLabel", "matchScore", "customLabels", "getLabelTooltip", "_label", "getSectionDisclosure", "hasHighMatches", "isExpanded", "getInlineDisclosure", "compact", "getInlineTooltip", "getBadgeText", "badgeType", "getCtaText", "context", "productName", "hasHighQualityMatches", "recommendations", "rec", "getPoweredByText", "AdMeshProductCard", "theme", "showMatchScore", "showBadges", "variation", "setIsExpanded", "badges", "generatedBadges", "aiKeywords", "_a", "keyword", "ai", "badge", "inlineDisclosure", "inlineTooltip", "matchScorePercentage", "content", "variations", "cardClasses", "cardStyle", "_b", "_c", "_d", "_e", "_f", "_g", "_h", "_i", "_j", "jsxs", "_k", "_l", "e", "feature", "j", "integration", "Fragment", "_m", "AdMeshCompareTable", "maxProducts", "showMatchScores", "showFeatures", "productsToCompare", "containerClasses", "containerStyle", "product", "index", "badgeTypeVariants", "badgeTypeIcons", "AdMeshBadge", "variant", "size", "effectiveVariant", "icon", "badgeClasses", "AdMeshExpandableUnit", "showPoweredBy", "initialExpanded", "sections", "ctaText", "collapsible", "handleToggleExpand", "apiFeatureSections", "defaultSections", "displaySections", "displayCtaText", "colors", "customStyles", "section", "AdMeshInlineRecommendation", "showReason", "AdMeshConversationSummary", "conversation<PERSON><PERSON><PERSON><PERSON>", "showTopRecommendations", "onRecommendationClick", "onStartNewConversation", "topRecommendations", "a", "b", "AdMeshCitationReference", "citationNumber", "citationStyle", "showTooltip", "onHover", "isHovered", "setIsHovered", "handleMouseEnter", "handleMouseLeave", "getCitationDisplay", "citationClasses", "AdMeshCitationUnit", "conversationText", "showCitationList", "onCitationHover", "hoveredRecommendation", "setHoveredRecommendation", "processedContent", "processedText", "citationMap", "title", "titleRegex", "match", "keywords", "inserted", "keywordRegex", "renderTextWithCitations", "text", "part", "citationMatch", "AdMeshConversationalUnit", "sessionId", "on<PERSON><PERSON><PERSON>", "isVisible", "setIsVisible", "hasAnimated", "setHasAnimated", "timer", "maxRecommendations", "displayRecommendations", "handleRecommendationClick", "handle<PERSON><PERSON><PERSON>", "renderContent", "AdMeshChatMessage", "isUser", "isAssistant", "messageClasses", "bubbleClasses", "formatTime", "timestamp", "AdMeshChatInput", "placeholder", "disabled", "suggestions", "onSendMessage", "setMessage", "showSuggestions", "setShowSuggestions", "filteredSuggestions", "setFilteredSuggestions", "inputRef", "handleInputChange", "filtered", "suggestion", "handleKeyDown", "handleSend", "trimmedMessage", "handleSuggestionClick", "inputClasses", "sendButtonClasses", "AdMeshChatInterface", "messages", "isLoading", "messagesEndRef", "messagesContainerRef", "displayMessages", "AdMeshFloatingChat", "subtitle", "controlledIsOpen", "onToggle", "autoRecommendations", "autoRecommendationTrigger", "showInputField", "autoShowRecommendations", "onAutoRecommendationDismiss", "internalIsOpen", "setInternalIsOpen", "setMessages", "setIsLoading", "hasInteracted", "setHasInteracted", "isOpen", "welcomeMessage", "autoMessage", "prev", "msg", "handleToggle", "handleSendMessage", "messageContent", "userMessage", "errorMessage", "getChatDimensions", "chatClasses", "AdMeshSidebarHeader", "isCollapsed", "onSearch", "showSearch", "searchQuery", "setSearch<PERSON>uery", "isSearchFocused", "setIsSearchFocused", "isMobile", "setIsMobile", "checkMobile", "handleSearchChange", "handleSearchClear", "headerClasses", "AdMeshSidebarContent", "displayMode", "showFilters", "setShowFilters", "activeTab", "setActiveTab", "tabRecommendations", "contentClasses", "renderRecommendations", "AdMeshSidebar", "containerMode", "setIsCollapsed", "filters", "originalStyle", "interval", "filteredRecommendations", "query", "cat", "handleSearch", "sidebarClasses", "AdMeshAutoRecommendationWidget", "trigger", "position", "autoShow", "showDelay", "getWidgetDimensions", "getPositionClasses", "createAdMeshTheme", "customTheme", "baseTheme", "createDarkTheme", "themePresets", "mergeThemes", "themes", "merged", "themeFromCSSProperties", "element", "computedStyle", "VERSION", "DEFAULT_CONFIG"], "mappings": ";;;;;;;;4CAWA,IAAIA,EAAqB,OAAO,IAAI,4BAA4B,EAC9DC,EAAsB,OAAO,IAAI,gBAAgB,EACnD,SAASC,EAAQC,EAAMC,EAAQC,EAAU,CACvC,IAAIC,EAAM,KAGV,GAFWD,IAAX,SAAwBC,EAAM,GAAKD,GACxBD,EAAO,MAAlB,SAA0BE,EAAM,GAAKF,EAAO,KACxC,QAASA,EAAQ,CACnBC,EAAW,CAAA,EACX,QAASE,KAAYH,EACTG,IAAV,QAAuBF,EAASE,CAAQ,EAAIH,EAAOG,CAAQ,QACxDF,EAAWD,EAClB,OAAAA,EAASC,EAAS,IACX,CACL,SAAUL,EACV,KAAMG,EACN,IAAKG,EACL,IAAgBF,IAAX,OAAoBA,EAAS,KAClC,MAAOC,EAEX,CACA,OAAAG,EAAA,SAAmBP,EACnBO,EAAA,IAAcN,EACdM,EAAA,KAAeN;;;;;;;;yCCtBE,QAAQ,IAAI,WAA7B,cACG,UAAY,CACX,SAASO,EAAyBN,EAAM,CACtC,GAAYA,GAAR,KAAc,OAAO,KACzB,GAAmB,OAAOA,GAAtB,WACF,OAAOA,EAAK,WAAaO,EACrB,KACAP,EAAK,aAAeA,EAAK,MAAQ,KACvC,GAAiB,OAAOA,GAApB,SAA0B,OAAOA,EACrC,OAAQA,EAAI,CACV,KAAKF,EACH,MAAO,WACT,KAAKU,EACH,MAAO,WACT,KAAKC,EACH,MAAO,aACT,KAAKC,EACH,MAAO,WACT,KAAKC,EACH,MAAO,eACT,KAAKC,EACH,MAAO,UACjB,CACM,GAAiB,OAAOZ,GAApB,SACF,OACgB,OAAOA,EAAK,KAAzB,UACC,QAAQ,MACN,qHAEJA,EAAK,SACf,CACU,KAAKa,EACH,MAAO,SACT,KAAKC,EACH,OAAQd,EAAK,aAAe,WAAa,YAC3C,KAAKe,EACH,OAAQf,EAAK,SAAS,aAAe,WAAa,YACpD,KAAKgB,EACH,IAAIC,EAAYjB,EAAK,OACrB,OAAAA,EAAOA,EAAK,YACZA,IACIA,EAAOiB,EAAU,aAAeA,EAAU,MAAQ,GACnDjB,EAAcA,IAAP,GAAc,cAAgBA,EAAO,IAAM,cAC9CA,EACT,KAAKkB,EACH,OACGD,EAAYjB,EAAK,aAAe,KACxBiB,IAAT,KACIA,EACAX,EAAyBN,EAAK,IAAI,GAAK,OAE/C,KAAKmB,EACHF,EAAYjB,EAAK,SACjBA,EAAOA,EAAK,MACZ,GAAI,CACF,OAAOM,EAAyBN,EAAKiB,CAAS,CAAC,OACrC,CAAA,CACxB,CACM,OAAO,IACb,CACI,SAASG,EAAmBC,EAAO,CACjC,MAAO,GAAKA,CAClB,CACI,SAASC,EAAuBD,EAAO,CACrC,GAAI,CACFD,EAAmBC,CAAK,EACxB,IAAIE,EAA2B,QACrB,CACVA,EAA2B,EACnC,CACM,GAAIA,EAA0B,CAC5BA,EAA2B,QAC3B,IAAIC,EAAwBD,EAAyB,MACjDE,EACc,OAAO,QAAtB,YACC,OAAO,aACPJ,EAAM,OAAO,WAAW,GAC1BA,EAAM,YAAY,MAClB,SACF,OAAAG,EAAsB,KACpBD,EACA,2GACAE,GAEKL,EAAmBC,CAAK,CACvC,CACA,CACI,SAASK,EAAY1B,EAAM,CACzB,GAAIA,IAASF,EAAqB,MAAO,KACzC,GACe,OAAOE,GAApB,UACSA,IAAT,MACAA,EAAK,WAAamB,EAElB,MAAO,QACT,GAAI,CACF,IAAIQ,EAAOrB,EAAyBN,CAAI,EACxC,OAAO2B,EAAO,IAAMA,EAAO,IAAM,aACvB,CACV,MAAO,OACf,CACA,CACI,SAASC,GAAW,CAClB,IAAIC,EAAaC,EAAqB,EACtC,OAAgBD,IAAT,KAAsB,KAAOA,EAAW,SAAQ,CAC7D,CACI,SAASE,GAAe,CACtB,OAAO,MAAM,uBAAuB,CAC1C,CACI,SAASC,EAAY/B,EAAQ,CAC3B,GAAIgC,EAAe,KAAKhC,EAAQ,KAAK,EAAG,CACtC,IAAIiC,EAAS,OAAO,yBAAyBjC,EAAQ,KAAK,EAAE,IAC5D,GAAIiC,GAAUA,EAAO,eAAgB,MAAO,EACpD,CACM,OAAkBjC,EAAO,MAAlB,MACb,CACI,SAASkC,EAA2BC,EAAOC,EAAa,CACtD,SAASC,GAAwB,CAC/BC,IACIA,EAA6B,GAC/B,QAAQ,MACN,0OACAF,CACZ,EACA,CACMC,EAAsB,eAAiB,GACvC,OAAO,eAAeF,EAAO,MAAO,CAClC,IAAKE,EACL,aAAc,EACtB,CAAO,CACP,CACI,SAASE,GAAyC,CAChD,IAAIC,EAAgBnC,EAAyB,KAAK,IAAI,EACtD,OAAAoC,EAAuBD,CAAa,IAChCC,EAAuBD,CAAa,EAAI,GAC1C,QAAQ,MACN,6IACV,GACMA,EAAgB,KAAK,MAAM,IACTA,IAAX,OAA2BA,EAAgB,IACxD,CACI,SAASE,EACP3C,EACAG,EACAyC,EACAC,EACAC,EACAV,EACAW,GACAC,GACA,CACA,OAAAJ,EAAOR,EAAM,IACbpC,EAAO,CACL,SAAUH,EACV,KAAMG,EACN,IAAKG,EACL,MAAOiC,EACP,OAAQU,IAEWF,IAAX,OAAkBA,EAAO,QAAnC,KACI,OAAO,eAAe5C,EAAM,MAAO,CACjC,WAAY,GACZ,IAAKwC,EACN,EACD,OAAO,eAAexC,EAAM,MAAO,CAAE,WAAY,GAAI,MAAO,KAAM,EACtEA,EAAK,OAAS,CAAA,EACd,OAAO,eAAeA,EAAK,OAAQ,YAAa,CAC9C,aAAc,GACd,WAAY,GACZ,SAAU,GACV,MAAO,CACf,CAAO,EACD,OAAO,eAAeA,EAAM,aAAc,CACxC,aAAc,GACd,WAAY,GACZ,SAAU,GACV,MAAO,IACf,CAAO,EACD,OAAO,eAAeA,EAAM,cAAe,CACzC,aAAc,GACd,WAAY,GACZ,SAAU,GACV,MAAO+C,EACf,CAAO,EACD,OAAO,eAAe/C,EAAM,aAAc,CACxC,aAAc,GACd,WAAY,GACZ,SAAU,GACV,MAAOgD,EACf,CAAO,EACD,OAAO,SAAW,OAAO,OAAOhD,EAAK,KAAK,EAAG,OAAO,OAAOA,CAAI,GACxDA,CACb,CACI,SAASiD,EACPjD,EACAC,EACAC,EACAgD,EACAL,EACAD,EACAG,GACAC,GACA,CACA,IAAIG,EAAWlD,EAAO,SACtB,GAAekD,IAAX,OACF,GAAID,EACF,GAAIE,EAAYD,CAAQ,EAAG,CACzB,IACED,EAAmB,EACnBA,EAAmBC,EAAS,OAC5BD,IAEAG,EAAkBF,EAASD,CAAgB,CAAC,EAC9C,OAAO,QAAU,OAAO,OAAOC,CAAQ,OAEvC,QAAQ,MACN,6JAEDE,EAAkBF,CAAQ,EACjC,GAAIlB,EAAe,KAAKhC,EAAQ,KAAK,EAAG,CACtCkD,EAAW7C,EAAyBN,CAAI,EACxC,IAAIsD,EAAO,OAAO,KAAKrD,CAAM,EAAE,OAAO,SAAUsD,GAAG,CACjD,OAAiBA,KAAV,KACjB,CAAS,EACDL,EACE,EAAII,EAAK,OACL,kBAAoBA,EAAK,KAAK,SAAS,EAAI,SAC3C,iBACNE,EAAsBL,EAAWD,CAAgB,IAC7CI,EACA,EAAIA,EAAK,OAAS,IAAMA,EAAK,KAAK,SAAS,EAAI,SAAW,KAC5D,QAAQ,MACN;AAAA;AAAA;AAAA;AAAA;AAAA,mCACAJ,EACAC,EACAG,EACAH,GAEDK,EAAsBL,EAAWD,CAAgB,EAAI,GAChE,CAMM,GALAC,EAAW,KACAjD,IAAX,SACGoB,EAAuBpB,CAAQ,EAAIiD,EAAW,GAAKjD,GACtD8B,EAAY/B,CAAM,IACfqB,EAAuBrB,EAAO,GAAG,EAAIkD,EAAW,GAAKlD,EAAO,KAC3D,QAASA,EAAQ,CACnBC,EAAW,CAAA,EACX,QAASE,MAAYH,EACTG,KAAV,QAAuBF,EAASE,EAAQ,EAAIH,EAAOG,EAAQ,QACxDF,EAAWD,EAClB,OAAAkD,GACEhB,EACEjC,EACe,OAAOF,GAAtB,WACIA,EAAK,aAAeA,EAAK,MAAQ,UACjCA,GAED2C,EACL3C,EACAmD,EACAP,EACAC,EACAjB,EAAQ,EACR1B,EACA6C,GACAC,GAER,CACI,SAASK,EAAkBI,EAAM,CAClB,OAAOA,GAApB,UACWA,IAAT,MACAA,EAAK,WAAa5D,GAClB4D,EAAK,SACJA,EAAK,OAAO,UAAY,EACjC,CACI,IAAIC,EAAQC,EACV9D,EAAqB,OAAO,IAAI,4BAA4B,EAC5DgB,EAAoB,OAAO,IAAI,cAAc,EAC7Cf,EAAsB,OAAO,IAAI,gBAAgB,EACjDW,EAAyB,OAAO,IAAI,mBAAmB,EACvDD,EAAsB,OAAO,IAAI,gBAAgB,EAE/CO,EAAsB,OAAO,IAAI,gBAAgB,EACnDD,EAAqB,OAAO,IAAI,eAAe,EAC/CE,EAAyB,OAAO,IAAI,mBAAmB,EACvDN,EAAsB,OAAO,IAAI,gBAAgB,EACjDC,EAA2B,OAAO,IAAI,qBAAqB,EAC3DO,EAAkB,OAAO,IAAI,YAAY,EACzCC,EAAkB,OAAO,IAAI,YAAY,EACzCP,EAAsB,OAAO,IAAI,gBAAgB,EACjDL,EAAyB,OAAO,IAAI,wBAAwB,EAC5DuB,EACE4B,EAAM,gEACRzB,EAAiB,OAAO,UAAU,eAClCmB,EAAc,MAAM,QACpBQ,EAAa,QAAQ,WACjB,QAAQ,WACR,UAAY,CACV,OAAO,MAEfF,EAAQ,CACN,2BAA4B,SAAUG,EAAmB,CACvD,OAAOA,EAAiB,CAChC,GAEI,IAAItB,EACAG,EAAyB,CAAA,EACzBoB,EAAyBJ,EAAM,0BAA0B,EAAE,KAC7DA,EACA3B,CACN,EAAK,EACGgC,EAAwBH,EAAWlC,EAAYK,CAAY,CAAC,EAC5DyB,EAAwB,CAAA,EAC5BQ,EAAA,SAAmBlE,EACnBkE,EAAA,IAAc,SAAUhE,EAAMC,EAAQC,EAAU2C,EAAQD,EAAM,CAC5D,IAAIqB,EACF,IAAMnC,EAAqB,6BAC7B,OAAOmB,EACLjD,EACAC,EACAC,EACA,GACA2C,EACAD,EACAqB,EACI,MAAM,uBAAuB,EAC7BH,EACJG,EAAmBL,EAAWlC,EAAY1B,CAAI,CAAC,EAAI+D,IAGvDC,EAAA,KAAe,SAAUhE,EAAMC,EAAQC,EAAU2C,EAAQD,EAAM,CAC7D,IAAIqB,EACF,IAAMnC,EAAqB,6BAC7B,OAAOmB,EACLjD,EACAC,EACAC,EACA,GACA2C,EACAD,EACAqB,EACI,MAAM,uBAAuB,EAC7BH,EACJG,EAAmBL,EAAWlC,EAAY1B,CAAI,CAAC,EAAI+D,GAG3D,EAAG,2CCnWC,QAAQ,IAAI,WAAa,aAC3BG,GAAA,QAAiBP,GAAA,EAEjBO,GAAA,QAAiBC,GAAA;;;;qDCElB,UAAY,CAGZ,IAAIC,EAAS,CAAA,EAAG,eAEhB,SAASC,GAAc,CAGtB,QAFIC,EAAU,GAELC,EAAI,EAAGA,EAAI,UAAU,OAAQA,IAAK,CAC1C,IAAIC,EAAM,UAAUD,CAAC,EACjBC,IACHF,EAAUG,EAAYH,EAASI,EAAWF,CAAG,CAAC,EAElD,CAEE,OAAOF,CACT,CAEC,SAASI,EAAYF,EAAK,CACzB,GAAI,OAAOA,GAAQ,UAAY,OAAOA,GAAQ,SAC7C,OAAOA,EAGR,GAAI,OAAOA,GAAQ,SAClB,MAAO,GAGR,GAAI,MAAM,QAAQA,CAAG,EACpB,OAAOH,EAAW,MAAM,KAAMG,CAAG,EAGlC,GAAIA,EAAI,WAAa,OAAO,UAAU,UAAY,CAACA,EAAI,SAAS,SAAQ,EAAG,SAAS,eAAe,EAClG,OAAOA,EAAI,SAAQ,EAGpB,IAAIF,EAAU,GAEd,QAASnE,KAAOqE,EACXJ,EAAO,KAAKI,EAAKrE,CAAG,GAAKqE,EAAIrE,CAAG,IACnCmE,EAAUG,EAAYH,EAASnE,CAAG,GAIpC,OAAOmE,CACT,CAEC,SAASG,EAAapD,EAAOsD,EAAU,CACtC,OAAKA,EAIDtD,EACIA,EAAQ,IAAMsD,EAGftD,EAAQsD,EAPPtD,CAQV,CAEsCuD,EAAO,SAC3CP,EAAW,QAAUA,EACrBO,UAAiBP,GAOjB,OAAO,WAAaA,CAEtB,iDCxEMQ,GAAuB,kCAW7B,IAAIC,GAA+B,CACjC,WAAYD,GACZ,QAAS,GACT,MAAO,GACP,cAAe,EACf,WAAY,GACd,EAEO,MAAME,GAA0B9E,GAAoC,CACzE6E,GAAe,CAAE,GAAGA,GAAc,GAAG7E,CAAA,CACvC,EAEa+E,GAAoB/E,GAA6D,CAC5F,KAAM,CAACgF,EAAYC,CAAa,EAAIC,EAAAA,SAAS,EAAK,EAC5C,CAACC,EAAOC,CAAQ,EAAIF,EAAAA,SAAwB,IAAI,EAEhDG,EAAeC,UAAQ,KAAO,CAAE,GAAGT,GAAc,GAAG7E,CAAA,GAAW,CAACA,CAAM,CAAC,EAEvEuF,EAAMC,EAAAA,YAAY,CAACC,EAAiBC,IAAmB,CACvDL,EAAa,OACf,QAAQ,IAAI,oBAAoBI,CAAO,GAAIC,CAAI,CACjD,EACC,CAACL,EAAa,KAAK,CAAC,EAEjBM,EAAoBH,EAAAA,YAAY,MACpCI,EACAF,IACkB,CAClB,GAAI,CAACL,EAAa,QAAS,CACzBE,EAAI,oCAAqC,CAAE,UAAAK,EAAW,KAAAF,CAAA,CAAM,EAC5D,MAAA,CAGF,GAAI,CAACA,EAAK,MAAQ,CAACA,EAAK,WAAY,CAClC,MAAMG,EAAW,mEACjBN,EAAIM,EAAUH,CAAI,EAClBN,EAASS,CAAQ,EACjB,MAAA,CAGFZ,EAAc,EAAI,EAClBG,EAAS,IAAI,EAEb,MAAMU,EAAU,CACd,WAAYF,EACZ,MAAOF,EAAK,KACZ,YAAaA,EAAK,WAClB,WAAYA,EAAK,UACjB,QAASA,EAAK,OACd,WAAYA,EAAK,UACjB,QAASA,EAAK,QACd,gBAAiBA,EAAK,eACtB,SAAUA,EAAK,SACf,UAAW,IAAI,KAAA,EAAO,YAAA,EACtB,WAAY,UAAU,UACtB,SAAU,SAAS,SACnB,SAAU,OAAO,SAAS,IAAA,EAG5BH,EAAI,WAAWK,CAAS,SAAUE,CAAO,EAEzC,IAAIC,EAA0B,KAE9B,QAASC,EAAU,EAAGA,IAAYX,EAAa,eAAiB,GAAIW,IAClE,GAAI,CACF,MAAMC,EAAW,MAAM,MAAM,GAAGZ,EAAa,UAAU,UAAW,CAChE,OAAQ,OACR,QAAS,CACP,eAAgB,kBAAA,EAElB,KAAM,KAAK,UAAUS,CAAO,CAAA,CAC7B,EAED,GAAI,CAACG,EAAS,GACZ,MAAM,IAAI,MAAM,QAAQA,EAAS,MAAM,KAAKA,EAAS,UAAU,EAAE,EAGnE,MAAMC,EAAS,MAAMD,EAAS,KAAA,EAC9BV,EAAI,GAAGK,CAAS,8BAA+BM,CAAM,EACrDjB,EAAc,EAAK,EACnB,MAAA,OAEOkB,EAAK,CACZJ,EAAYI,EACZZ,EAAI,WAAWS,CAAO,eAAeJ,CAAS,SAAUO,CAAG,EAEvDH,GAAWX,EAAa,eAAiB,IAC3C,MAAM,IAAI,WACR,WAAWe,GAAUf,EAAa,YAAc,KAAQW,CAAO,CAAA,CAEnE,CAKJ,MAAMH,EAAW,mBAAmBD,CAAS,gBAAgBP,EAAa,aAAa,cAAcU,GAAA,YAAAA,EAAW,OAAO,GACvHR,EAAIM,EAAUE,CAAS,EACvBX,EAASS,CAAQ,EACjBZ,EAAc,EAAK,CAAA,EAClB,CAACI,EAAcE,CAAG,CAAC,EAEhBc,EAAab,cAAY,MAAOE,GAC7BC,EAAkB,QAASD,CAAI,EACrC,CAACC,CAAiB,CAAC,EAEhBW,EAAYd,cAAY,MAAOE,GAC5BC,EAAkB,OAAQD,CAAI,EACpC,CAACC,CAAiB,CAAC,EAEhBY,EAAkBf,cAAY,MAAOE,IACrC,CAACA,EAAK,SAAW,CAACA,EAAK,gBACzBH,EAAI,kEAAmEG,CAAI,EAEtEC,EAAkB,aAAcD,CAAI,GAC1C,CAACC,EAAmBJ,CAAG,CAAC,EAE3B,MAAO,CACL,WAAAc,EACA,UAAAC,EACA,gBAAAC,EACA,WAAAvB,EACA,MAAAG,CAAA,CAEJ,EAGaqB,GAAkB,CAC7BC,EACAC,EACAC,IACW,CACX,GAAI,CACF,MAAMC,EAAM,IAAI,IAAIH,CAAQ,EAC5B,OAAAG,EAAI,aAAa,IAAI,QAASF,CAAI,EAClCE,EAAI,aAAa,IAAI,aAAc,QAAQ,EAC3CA,EAAI,aAAa,IAAI,aAAc,gBAAgB,EAE/CD,GACF,OAAO,QAAQA,CAAgB,EAAE,QAAQ,CAAC,CAACzG,EAAKkB,CAAK,IAAM,CACzDwF,EAAI,aAAa,IAAI1G,EAAKkB,CAAK,CAAA,CAChC,EAGIwF,EAAI,SAAA,CAAS,OACbT,EAAK,CACZ,eAAQ,KAAK,oDAAqDM,EAAUN,CAAG,EACxEM,CAAA,CAEX,EAGaI,GAAsB,CACjCC,EACAC,KAEO,CACL,KAAMD,EAAe,MACrB,WAAYA,EAAe,YAC3B,UAAWA,EAAe,WAC1B,GAAGC,CAAA,GC1KMC,EAAsD,CAAC,CAClE,KAAAN,EACA,WAAAO,EACA,UAAAC,EACA,SAAAhE,EACA,aAAAiE,EACA,UAAAC,EACA,MAAAC,CACF,IAAM,CACJ,KAAM,CAAE,WAAAhB,EAAY,UAAAC,CAAA,EAAcvB,GAAA,EAC5BuC,EAAaC,EAAAA,OAAuB,IAAI,EACxCC,EAAiBD,EAAAA,OAAO,EAAK,EAGnCE,EAAAA,UAAU,IAAM,CACd,GAAI,CAACH,EAAW,SAAWE,EAAe,QAAS,OAEnD,MAAME,EAAW,IAAI,qBAClBC,GAAY,CACXA,EAAQ,QAASC,GAAU,CACrBA,EAAM,gBAAkB,CAACJ,EAAe,UAC1CA,EAAe,QAAU,GACzBlB,EAAU,CACR,KAAAI,EACA,WAAAO,EACA,UAAAC,EACA,GAAGC,CAAA,CACJ,EAAE,MAAM,QAAQ,KAAK,EACxB,CACD,CAAA,EAEH,CACE,UAAW,GACX,WAAY,KAAA,CACd,EAGF,OAAAO,EAAS,QAAQJ,EAAW,OAAO,EAE5B,IAAM,CACXI,EAAS,WAAA,CAAW,CACtB,EACC,CAAChB,EAAMO,EAAYC,EAAWC,EAAcb,CAAS,CAAC,EAEzD,MAAMuB,EAAcrC,cAAY,MAAOsC,GAA4B,CAEjE,GAAI,CACF,MAAMzB,EAAW,CACf,KAAAK,EACA,WAAAO,EACA,UAAAC,EACA,GAAGC,CAAA,CACJ,CAAA,OACMhC,EAAO,CACd,QAAQ,MAAM,yBAA0BA,CAAK,CAAA,CAOhC2C,EAAM,OACD,QAAQ,GAAG,GAI7B,OAAO,KAAKb,EAAY,SAAU,qBAAqB,CACzD,EAEC,CAACP,EAAMO,EAAYC,EAAWC,EAAcd,CAAU,CAAC,EAE1D,OACE0B,EAAAA,IAAC,MAAA,CACC,IAAKT,EACL,UAAAF,EACA,QAASS,EACT,MAAO,CACL,OAAQ,UACR,GAAGR,CAAA,EAGJ,SAAAnE,CAAA,CAAA,CAGP,EAEA8D,EAAkB,YAAc,oBCvFhC,MAAMgB,GAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsoBtB,IAAIC,GAAiB,GAEd,MAAMC,GAAkB,IAAM,CACnCT,EAAAA,UAAU,IAAM,CACd,GAAIQ,GAAgB,OAGpB,MAAME,EAAe,SAAS,cAAc,OAAO,EACnD,OAAAA,EAAa,GAAK,uBAClBA,EAAa,YAAcH,GAGtB,SAAS,eAAe,sBAAsB,IACjD,SAAS,KAAK,YAAYG,CAAY,EACtCF,GAAiB,IAIZ,IAAM,CACX,MAAMG,EAAgB,SAAS,eAAe,sBAAsB,EAChEA,GAAiB,SAAS,KAAK,SAASA,CAAa,IACvD,SAAS,KAAK,YAAYA,CAAa,EACvCH,GAAiB,GACnB,CACF,EACC,EAAE,CACP,EC/oBaI,EAAyB,CACpCvB,EACA9G,EAA2B,KAChB,CACX,MAAMsI,EAAaxB,EAAe,oBAAsB,EAClDyB,EAAevI,EAAO,cAAgB,CAAA,EAG5C,OAAIsI,GAAc,GACTC,EAAa,WAAa,aAI/BD,GAAc,GACTC,EAAa,cAAgB,gBAIlCD,GAAc,GACTC,EAAa,gBAAkB,kBAIjCA,EAAa,eAAiB,gBACvC,EAKaC,EAAkB,CAC7B1B,EACA2B,IACW,CACX,MAAMH,EAAaxB,EAAe,oBAAsB,EAExD,OAAIwB,GAAc,GACT,gIAGLA,GAAc,GACT,oHAGLA,GAAc,GACT,oHAGF,6JACT,EAKaI,GAAuB,CAClCC,EAA0B,GAC1BC,EAAsB,KAEjBD,EAIDC,EACK,mFAGF,mLAPE,gKAaEC,GAAsB,CACjC/B,EACAgC,EAAmB,KACR,CACX,MAAMR,EAAaxB,EAAe,oBAAsB,EAExD,OAAIgC,EACK,iBAGLR,GAAc,GACT,aAGLA,GAAc,GACT,gBAGF,iBACT,EAKaS,GAAmB,IACvB,sKAMIC,GAAgBC,IACc,CACvC,YAAa,YACb,aAAc,aACd,cAAe,cACf,cAAe,cACf,YAAe,cACf,WAAY,WACZ,SAAY,WACZ,iBAAkB,iBAClB,eAAgB,eAChB,YAAa,YACb,aAAc,aACd,QAAW,UACX,IAAO,MACP,kBAAmB,kBACnB,iBAAkB,iBAClB,uBAAwB,uBACxB,iBAAkB,gBAAA,GAGJA,CAAS,GAAKA,EAMnBC,GAAa,CACxBpC,EACAqC,EAA6B,WAClB,CACX,MAAMC,EAActC,EAAe,sBAAwBA,EAAe,MAE1E,OAAIqC,IAAY,OACPC,EAILtC,EAAe,YAAcA,EAAe,WAAa,EACpD,OAAOsC,CAAW,GAGpB,YACT,EAKaC,GAAyBC,GAC7BA,EAAgB,KAAKC,IAAQA,EAAI,oBAAsB,IAAM,EAAG,EAM5DC,GAAmB,CAACV,EAAmB,KAC9CA,EACK,oBAGF,oCCzKIW,GAAsD,CAAC,CAClE,eAAA3C,EACA,MAAA4C,EACA,eAAAC,EAAiB,GACjB,WAAAC,EAAa,GACb,UAAAC,EAAY,UACZ,UAAAzC,EACA,MAAAC,CACF,IAAM,+BAEJa,GAAA,EAGA,KAAM,CAACU,EAAYkB,CAAa,EAAI5E,EAAAA,SAAS,EAAK,EAE5C6E,EAASzE,EAAAA,QAAQ,IAAmB,OACxC,MAAM0E,EAA+B,CAAA,EAGhB3B,EAAuBvB,CAAc,IACrC,cACnBkD,EAAgB,KAAK,WAAW,EAI9BlD,EAAe,YAAcA,EAAe,WAAa,GAC3DkD,EAAgB,KAAK,iBAAiB,EAIxC,MAAMC,EAAa,CAAC,KAAM,0BAA2B,mBAAoB,KAAM,YAAY,EAK3F,SAJsBC,EAAApD,EAAe,WAAf,YAAAoD,EAAyB,KAAKC,GAClDF,EAAW,KAAKG,GAAMD,EAAQ,YAAA,EAAc,SAASC,CAAE,CAAC,KACrDtD,EAAe,MAAM,YAAA,EAAc,SAAS,IAAI,IAGnDkD,EAAgB,KAAK,YAAY,EAI/BlD,EAAe,QAAUA,EAAe,OAAO,OAAS,GAC1DA,EAAe,OAAO,QAAQuD,GAAS,CAEjC,CAAC,YAAa,YAAa,aAAc,UAAW,MAAO,iBAAiB,EAAE,SAASA,CAAK,GAC5F,CAACL,EAAgB,SAASK,CAAkB,GAC9CL,EAAgB,KAAKK,CAAkB,CACzC,CACD,EAKIL,CAAA,EACN,CAAClD,CAAc,CAAC,EAGbwD,EAAmBzB,GAAoB/B,EAAgB,EAAK,EAC5DyD,EAAgBxB,GAAA,EAGhByB,EAAuB,KAAK,MAAM1D,EAAe,mBAAqB,GAAG,EAmCzE2D,GAhCsB,IAAM,CAChC,MAAMC,EAAa5D,EAAe,mBAElC,OAAI+C,IAAc,SACT,CACL,MAAO/C,EAAe,sBAAwBA,EAAe,MAC7D,YAAaA,EAAe,4BAA8BA,EAAe,aAAeA,EAAe,OACvG,QAASA,EAAe,sBAAwBA,EAAe,MAC/D,SAAU,EAAA,EAEH+C,IAAc,aAAca,GAAA,MAAAA,EAAY,UAC1C,CACL,MAAOA,EAAW,SAAS,KAAO5D,EAAe,sBAAwBA,EAAe,MACxF,YAAa4D,EAAW,SAAS,KACjC,QAASA,EAAW,SAAS,KAAO5D,EAAe,sBAAwBA,EAAe,KAAA,EAEnF+C,IAAc,cAAea,GAAA,MAAAA,EAAY,WAC3C,CACL,MAAO5D,EAAe,sBAAwBA,EAAe,MAC7D,YAAa4D,EAAW,UAAU,KAClC,QAASA,EAAW,UAAU,KAAO5D,EAAe,sBAAwBA,EAAe,KAAA,EAItF,CACL,MAAOA,EAAe,sBAAwBA,EAAe,MAC7D,YAAaA,EAAe,4BAA8BA,EAAe,aAAeA,EAAe,OACvG,QAASA,EAAe,sBAAwBA,EAAe,KAAA,CAEnE,GAGc,EAEV6D,EAAcvG,EAClB,mBACA,cACA,6OACAgD,CAAA,EAGIwD,EAAYlB,EAAQ,CACxB,mBAAoBA,EAAM,cAAgBA,EAAM,aAAe,UAC/D,qBAAsBA,EAAM,gBAAkB,UAC9C,kBAAmBA,EAAM,aAAe,UACxC,sBAAuBA,EAAM,gBAC7B,mBAAoBA,EAAM,aAC1B,kBAAmBA,EAAM,YACzB,gBAAiBA,EAAM,UACvB,0BAA2BA,EAAM,mBACjC,kBAAmBA,EAAM,cAAgB,OACzC,sBAAsBQ,EAAAR,EAAM,UAAN,YAAAQ,EAAe,MACrC,sBAAsBW,EAAAnB,EAAM,UAAN,YAAAmB,EAAe,OACrC,sBAAsBC,EAAApB,EAAM,UAAN,YAAAoB,EAAe,MACrC,uBAAuBC,EAAArB,EAAM,UAAN,YAAAqB,EAAe,MACtC,uBAAuBC,EAAAtB,EAAM,UAAN,YAAAsB,EAAe,OACtC,uBAAuBC,EAAAvB,EAAM,UAAN,YAAAuB,EAAe,MACtC,yBAAyBC,EAAAxB,EAAM,WAAN,YAAAwB,EAAgB,MACzC,2BAA2BC,EAAAzB,EAAM,WAAN,YAAAyB,EAAgB,KAC3C,yBAAyBC,EAAA1B,EAAM,WAAN,YAAA0B,EAAgB,MACzC,4BAA4BC,EAAA3B,EAAM,WAAN,YAAA2B,EAAgB,MAC5C,WAAY3B,EAAM,UAAA,EACO,OAG3B,OAAIG,IAAc,SAGdyB,EAAAA,KAAC,MAAA,CACC,UAAWlH,EACT,oCACA,uCACAgD,CAAA,EAEF,MAAO,CACL,YAAYsC,GAAA,YAAAA,EAAO,aAAc,oEACjC,IAAG6B,EAAA7B,GAAA,YAAAA,EAAO,aAAP,YAAA6B,EAAmB,YACtB,GAAGlE,CAAA,EAEL,oBAAmBqC,GAAA,YAAAA,EAAO,KAG1B,SAAA,CAAA3B,EAAAA,IAAC,OAAA,CACC,MAAO,CACL,SAAU,OACV,WAAY,MACZ,OAAO2B,GAAA,YAAAA,EAAO,cAAe,UAC7B,iBAAiBA,GAAA,YAAAA,EAAO,QAAS,OAAS,UAAY,UACtD,QAAS,UACT,aAAc,MACd,YAAa,KAAA,EAEf,MAAOlB,EAAgB1B,EAAgBuB,EAAuBvB,CAAc,CAAC,EAE5E,WAAuBA,CAAc,CAAA,CAAA,EAIxCwE,EAAAA,KAAC,OAAA,CACC,MAAO,CACL,OAAO5B,GAAA,YAAAA,EAAO,QAAS,OAAS,UAAY,UAC5C,YAAa,KAAA,EAGd,SAAA,CAAAe,EAAQ,YAAa,GAAA,CAAA,CAAA,EAIxB1C,EAAAA,IAACf,EAAA,CACC,KAAMF,EAAe,MACrB,WAAYA,EAAe,YAC3B,UAAWA,EAAe,WAC1B,aAAc,CACZ,MAAOA,EAAe,MACtB,WAAYA,EAAe,mBAC3B,UAAW,eAAA,EAGb,SAAAiB,EAAAA,IAAC,OAAA,CACC,MAAO,CACL,OAAO2B,GAAA,YAAAA,EAAO,cAAe,UAC7B,eAAgB,YAChB,OAAQ,UACR,SAAU,UACV,WAAY,SAAA,EAGb,SAAAe,EAAQ,OAAA,CAAA,CACX,CAAA,EAIFa,EAAAA,KAAC,OAAA,CACC,MAAO,CACL,SAAU,OACV,OAAO5B,GAAA,YAAAA,EAAO,QAAS,OAAS,UAAY,UAC5C,WAAY,KAAA,EAEd,MAAOa,EACR,SAAA,CAAA,IACGD,EAAiB,GAAA,CAAA,CAAA,CACrB,CAAA,CAAA,EAKFT,IAAc,YAAcA,IAAc,YAG1C9B,EAAAA,IAAC,MAAA,CACC,UAAW3D,EACT,2EACAwE,EACI,mKACA,mHACJxB,CAAA,EAEF,MAAO,CACL,YAAYsC,GAAA,YAAAA,EAAO,aAAc,oEACjC,IAAG8B,EAAA9B,GAAA,YAAAA,EAAO,aAAP,YAAA8B,EAAmB,YACtB,GAAGnE,CAAA,EAEL,oBAAmBqC,GAAA,YAAAA,EAAO,KAEzB,SAACd,EA+DA0C,EAAAA,KAAC,MAAA,CACC,UAAU,uBACV,MAAOV,EACP,oBAAmBlB,GAAA,YAAAA,EAAO,KAG1B,SAAA,CAAA4B,EAAAA,KAAC,MAAA,CAAI,UAAU,yEACb,SAAA,CAAAA,EAAAA,KAAC,MAAA,CAAI,UAAU,iEACZ,SAAA,CAAA1B,GAAcG,EAAO,SAAS,WAAW,GACxChC,EAAAA,IAAC,OAAA,CACC,UAAU,0EACV,MAAO,CACL,iBAAiB2B,GAAA,YAAAA,EAAO,gBAAgBA,GAAA,YAAAA,EAAO,cAAe,UAC9D,cAAcA,GAAA,YAAAA,EAAO,eAAgB,QAAA,EAEvC,MAAOlB,EAAgB1B,CAA4B,EAElD,YAAa,WAAW,CAAA,CAAA,EAG7BwE,EAAAA,KAAC,MAAA,CAAI,UAAU,kCACZ,SAAA,CAAAxE,EAAe,cACdiB,EAAAA,IAAC,MAAA,CACC,IAAKjB,EAAe,aAAa,IACjC,IAAK,GAAGA,EAAe,KAAK,QAC5B,UAAU,gCACV,QAAU2E,GAAM,CAEbA,EAAE,OAA4B,MAAM,QAAU,MAAA,CACjD,CAAA,EAGJ1D,EAAAA,IAAC,KAAA,CAAG,UAAU,+EACX,WAAQ,KAAA,CACX,CAAA,CAAA,CACF,CAAA,EACF,EAEAuD,EAAAA,KAAC,MAAA,CAAI,UAAU,2BACb,SAAA,CAAAA,EAAAA,KAAC,SAAA,CACC,QAAS,IAAMxB,EAAc,EAAK,EAClC,UAAU,qTACV,MAAM,oBAEN,SAAA,CAAA/B,EAAAA,IAAC,QAAK,SAAA,cAAA,CAAY,EAClBA,EAAAA,IAAC,OAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,YACjE,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,WAAW,CAAA,CAClF,CAAA,CAAA,CAAA,EAEFA,EAAAA,IAACf,EAAA,CACC,KAAMF,EAAe,MACrB,WAAYA,EAAe,YAC3B,UAAWA,EAAe,WAC1B,aAAc,CACZ,MAAOA,EAAe,MACtB,WAAYA,EAAe,mBAC3B,UAAW,kBAAA,EAGb,SAAAwE,EAAAA,KAAC,SAAA,CAAO,UAAU,8OACf,SAAA,CAAAzB,IAAc,WAAa,MAAQ,QAAQ,IAAEY,EAAQ,QACtD1C,EAAAA,IAAC,OAAI,UAAU,eAAe,KAAK,OAAO,OAAO,eAAe,QAAQ,YACtE,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,+EAA+E,CAAA,CACtJ,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAGAA,EAAAA,IAAC,MAAA,CAAI,UAAU,OACb,SAAAA,EAAAA,IAAC,KAAE,UAAU,2DACV,SAAA0C,EAAQ,WAAA,CACX,CAAA,CACF,EAGA1C,EAAAA,IAAC,MAAA,CAAI,UAAU,OACb,SAAAA,EAAAA,IAAC,IAAA,CACC,UAAU,2DACV,MAAOwC,EAEN,SAAAD,CAAA,CAAA,EAEL,EAGCX,GAAkB,OAAO7C,EAAe,oBAAuB,UAC9DwE,OAAC,MAAA,CAAI,UAAU,OACb,SAAA,CAAAA,EAAAA,KAAC,MAAA,CAAI,UAAU,6FACb,SAAA,CAAAvD,EAAAA,IAAC,OAAA,CAAK,UAAU,cAAc,SAAA,cAAW,EACzCuD,EAAAA,KAAC,OAAA,CAAK,UAAU,wGAAyG,SAAA,CAAAd,EAAqB,SAAA,CAAA,CAAO,CAAA,EACvJ,EACAzC,EAAAA,IAAC,MAAA,CAAI,UAAU,wEACb,SAAAA,EAAAA,IAAC,MAAA,CACC,UAAU,oGACV,MAAO,CAAE,MAAO,GAAGyC,CAAoB,GAAA,CAAI,CAAA,CAC7C,CACF,CAAA,EACF,EAGFc,EAAAA,KAAC,MAAA,CAAI,UAAU,oCACZ,SAAA,CAAAxE,EAAe,SACdwE,OAAC,OAAA,CAAK,UAAU,yNACd,SAAA,CAAAvD,EAAAA,IAAC,OAAI,UAAU,eAAe,KAAK,OAAO,OAAO,eAAe,QAAQ,YACtE,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,4IAA4I,CAAA,CACnN,EACCjB,EAAe,OAAA,EAClB,EAGDA,EAAe,YAAcA,EAAe,WAAa,GACxDwE,OAAC,OAAA,CAAK,UAAU,6MACd,SAAA,CAAAvD,EAAAA,IAAC,OAAI,UAAU,eAAe,KAAK,OAAO,OAAO,eAAe,QAAQ,YACtE,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,4DAA4D,CAAA,CACnI,EACCjB,EAAe,WAAW,YAAA,CAAA,CAC7B,CAAA,EAEJ,EAGCA,EAAe,UAAYA,EAAe,SAAS,OAAS,GAC3DwE,EAAAA,KAAC,MAAA,CAAI,UAAU,OACb,SAAA,CAAAvD,EAAAA,IAAC,MAAA,CAAI,UAAU,4DAA4D,SAAA,iBAE3E,EACAuD,EAAAA,KAAC,MAAA,CAAI,UAAU,yBACZ,SAAA,CAAAxE,EAAe,SAAS,MAAM,EAAG,CAAC,EAAE,IAAI,CAAC4E,EAASC,IACjDL,EAAAA,KAAC,OAAA,CAEC,UAAU,mNAEV,SAAA,CAAAvD,EAAAA,IAAC,OAAI,UAAU,wCAAwC,KAAK,OAAO,OAAO,eAAe,QAAQ,YAC/F,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,gDAAgD,CAAA,CACvH,EACC2D,CAAA,CAAA,EANIC,CAAA,CAQR,EACA7E,EAAe,SAAS,OAAS,GAChCwE,EAAAA,KAAC,OAAA,CAAK,UAAU,qDAAqD,SAAA,CAAA,IACjExE,EAAe,SAAS,OAAS,EAAE,OAAA,CAAA,CACvC,CAAA,CAAA,CAEJ,CAAA,EACF,EAIDA,EAAe,cAAgBA,EAAe,aAAa,OAAS,GACnEwE,EAAAA,KAAC,MAAA,CAAI,UAAU,OACb,SAAA,CAAAvD,EAAAA,IAAC,MAAA,CAAI,UAAU,4DAA4D,SAAA,kBAE3E,EACAuD,EAAAA,KAAC,MAAA,CAAI,UAAU,yBACZ,SAAA,CAAAxE,EAAe,aAAa,MAAM,EAAG,CAAC,EAAE,IAAI,CAAC8E,EAAaD,IACzDL,EAAAA,KAAC,OAAA,CAEC,UAAU,6MAEV,SAAA,CAAAvD,EAAAA,IAAC,OAAI,UAAU,wCAAwC,KAAK,OAAO,OAAO,eAAe,QAAQ,YAC/F,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,yJAAyJ,CAAA,CAChO,EACC6D,CAAA,CAAA,EANID,CAAA,CAQR,EACA7E,EAAe,aAAa,OAAS,GACpCwE,EAAAA,KAAC,OAAA,CAAK,UAAU,qDAAqD,SAAA,CAAA,IACjExE,EAAe,aAAa,OAAS,EAAE,OAAA,CAAA,CAC3C,CAAA,CAAA,CAEJ,CAAA,EACF,EAIFiB,EAAAA,IAAC,OAAI,UAAU,gCACb,eAAC,OAAA,CAAK,UAAU,2CAA2C,SAAA,mBAAA,CAE3D,CAAA,CACF,CAAA,CAAA,CAAA,EAlPFuD,OAAAO,EAAAA,SAAA,CAEE,SAAA,CAAA9D,EAAAA,IAAC,MAAA,CAAI,UAAU,OACb,SAAAA,EAAAA,IAAC,OAAA,CACC,MAAO,CACL,SAAU,OACV,WAAY,MACZ,OAAO2B,GAAA,YAAAA,EAAO,cAAe,UAC7B,iBAAiBA,GAAA,YAAAA,EAAO,QAAS,OAAS,UAAY,UACtD,QAAS,UACT,aAAc,KAAA,EAEhB,MAAOlB,EAAgB1B,EAAgBuB,EAAuBvB,CAAc,CAAC,EAE5E,WAAuBA,CAAc,CAAA,CAAA,EAE1C,EAEAwE,EAAAA,KAAC,MAAA,CAAI,UAAU,0CAEb,SAAA,CAAAvD,EAAAA,IAAC,OAAI,UAAU,iBACb,SAAAuD,EAAAA,KAAC,IAAA,CAAE,UAAU,2DACV,SAAA,CAAAb,EAAQ,YAAa,IACtB1C,EAAAA,IAACf,EAAA,CACC,KAAMF,EAAe,MACrB,WAAYA,EAAe,YAC3B,UAAWA,EAAe,WAC1B,aAAc,CACZ,MAAOA,EAAe,MACtB,WAAYA,EAAe,mBAC3B,UAAW,sBAAA,EAGb,SAAAiB,EAAAA,IAAC,OAAA,CACC,UAAU,uIAET,SAAA0C,EAAQ,OAAA,CAAA,CACX,CAAA,CACF,CAAA,CACF,CAAA,CACF,EAGA1C,EAAAA,IAAC,MAAA,CAAI,UAAU,wCACb,SAAAuD,EAAAA,KAAC,SAAA,CACC,QAAS,IAAMxB,EAAc,EAAI,EACjC,UAAU,qTACV,MAAM,oBAEN,SAAA,CAAA/B,EAAAA,IAAC,QAAK,SAAA,cAAA,CAAY,EAClBA,EAAAA,IAAC,OAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,YACjE,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,4DAA4D,CAAA,CACnI,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAGF,CAyLA,CAAA,EAQNA,EAAAA,IAAC,MAAA,CACC,UAAW4C,EACX,MAAO,CACL,YAAYjB,GAAA,YAAAA,EAAO,aAAc,oEACjC,IAAGoC,EAAApC,GAAA,YAAAA,EAAO,aAAP,YAAAoC,EAAmB,YACtB,GAAGzE,CAAA,EAEL,oBAAmBqC,GAAA,YAAAA,EAAO,KAE1B,SAAA4B,EAAAA,KAAC,MAAA,CACC,UAAU,uBACV,MAAOV,EAGP,SAAA,CAAA7C,EAAAA,IAAC,MAAA,CAAI,UAAU,OACb,SAAAA,EAAAA,IAAC,OAAA,CACC,MAAO,CACL,SAAU,OACV,WAAY,MACZ,OAAO2B,GAAA,YAAAA,EAAO,cAAe,UAC7B,iBAAiBA,GAAA,YAAAA,EAAO,QAAS,OAAS,UAAY,UACtD,QAAS,UACT,aAAc,KAAA,EAEhB,MAAOlB,EAAgB1B,EAAgBuB,EAAuBvB,CAAc,CAAC,EAE5E,WAAuBA,CAAc,CAAA,CAAA,EAE1C,EAGAwE,EAAAA,KAAC,MAAA,CAAI,UAAU,yEACb,SAAA,CAAAA,EAAAA,KAAC,MAAA,CAAI,UAAU,yCACZ,SAAA,CAAAxE,EAAe,cACdiB,EAAAA,IAAC,MAAA,CACC,IAAKjB,EAAe,aAAa,IACjC,IAAK,GAAGA,EAAe,KAAK,QAC5B,UAAU,gCACV,QAAU2E,GAAM,CAEbA,EAAE,OAA4B,MAAM,QAAU,MAAA,CACjD,CAAA,EAGJ1D,EAAAA,IAAC,KAAA,CAAG,UAAU,+EACX,WAAQ,KAAA,CACX,CAAA,EACF,EAEAA,EAAAA,IAAC,MAAA,CAAI,UAAU,2BACb,SAAAA,EAAAA,IAACf,EAAA,CACC,KAAMF,EAAe,MACrB,WAAYA,EAAe,YAC3B,UAAWA,EAAe,WAC1B,aAAc,CACZ,MAAOA,EAAe,MACtB,WAAYA,EAAe,mBAC3B,UAAW,kBAAA,EAGb,SAAAwE,EAAAA,KAAC,SAAA,CAAO,UAAU,8OAA8O,SAAA,CAAA,SACvPb,EAAQ,QACf1C,EAAAA,IAAC,OAAI,UAAU,eAAe,KAAK,OAAO,OAAO,eAAe,QAAQ,YACtE,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,+EAA+E,CAAA,CACtJ,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CACF,CAAA,EACF,EAGAA,EAAAA,IAAC,MAAA,CAAI,UAAU,OACb,SAAAA,EAAAA,IAAC,KAAE,UAAU,2DACV,SAAA0C,EAAQ,WAAA,CACX,CAAA,CACF,EAGCd,GAAkB,OAAO7C,EAAe,oBAAuB,UAC9DwE,OAAC,MAAA,CAAI,UAAU,OACb,SAAA,CAAAA,EAAAA,KAAC,MAAA,CAAI,UAAU,6FACb,SAAA,CAAAvD,EAAAA,IAAC,OAAA,CAAK,UAAU,cAAc,SAAA,cAAW,EACzCuD,EAAAA,KAAC,OAAA,CAAK,UAAU,wGAAyG,SAAA,CAAAd,EAAqB,SAAA,CAAA,CAAO,CAAA,EACvJ,EACAzC,EAAAA,IAAC,MAAA,CAAI,UAAU,wEACb,SAAAA,EAAAA,IAAC,MAAA,CACC,UAAU,oGACV,MAAO,CAAE,MAAO,GAAGyC,CAAoB,GAAA,CAAI,CAAA,CAC7C,CACF,CAAA,EACF,EAGFc,EAAAA,KAAC,MAAA,CAAI,UAAU,oCACZ,SAAA,CAAAxE,EAAe,SACdwE,OAAC,OAAA,CAAK,UAAU,yNACd,SAAA,CAAAvD,EAAAA,IAAC,OAAI,UAAU,eAAe,KAAK,OAAO,OAAO,eAAe,QAAQ,YACtE,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,4IAA4I,CAAA,CACnN,EACCjB,EAAe,OAAA,EAClB,EAGDA,EAAe,YAAcA,EAAe,WAAa,GACxDwE,OAAC,OAAA,CAAK,UAAU,6MACd,SAAA,CAAAvD,EAAAA,IAAC,OAAI,UAAU,eAAe,KAAK,OAAO,OAAO,eAAe,QAAQ,YACtE,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,4DAA4D,CAAA,CACnI,EACCjB,EAAe,WAAW,YAAA,CAAA,CAC7B,CAAA,EAEJ,EAGCA,EAAe,UAAYA,EAAe,SAAS,OAAS,GAC3DwE,EAAAA,KAAC,MAAA,CAAI,UAAU,OACb,SAAA,CAAAvD,EAAAA,IAAC,MAAA,CAAI,UAAU,4DAA4D,SAAA,iBAE3E,EACAuD,EAAAA,KAAC,MAAA,CAAI,UAAU,yBACZ,SAAA,CAAAxE,EAAe,SAAS,MAAM,EAAG,CAAC,EAAE,IAAI,CAAC4E,EAASC,IACjDL,EAAAA,KAAC,OAAA,CAEC,UAAU,mNAEV,SAAA,CAAAvD,EAAAA,IAAC,OAAI,UAAU,wCAAwC,KAAK,OAAO,OAAO,eAAe,QAAQ,YAC/F,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,gDAAgD,CAAA,CACvH,EACC2D,CAAA,CAAA,EANIC,CAAA,CAQR,EACA7E,EAAe,SAAS,OAAS,GAChCwE,EAAAA,KAAC,OAAA,CAAK,UAAU,qDAAqD,SAAA,CAAA,IACjExE,EAAe,SAAS,OAAS,EAAE,OAAA,CAAA,CACvC,CAAA,CAAA,CAEJ,CAAA,EACF,EAIDA,EAAe,cAAgBA,EAAe,aAAa,OAAS,GACnEwE,EAAAA,KAAC,MAAA,CAAI,UAAU,OACb,SAAA,CAAAvD,EAAAA,IAAC,MAAA,CAAI,UAAU,4DAA4D,SAAA,kBAE3E,EACAuD,EAAAA,KAAC,MAAA,CAAI,UAAU,yBACZ,SAAA,CAAAxE,EAAe,aAAa,MAAM,EAAG,CAAC,EAAE,IAAI,CAAC8E,EAAaD,IACzDL,EAAAA,KAAC,OAAA,CAEC,UAAU,6MAEV,SAAA,CAAAvD,EAAAA,IAAC,OAAI,UAAU,wCAAwC,KAAK,OAAO,OAAO,eAAe,QAAQ,YAC/F,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,yJAAyJ,CAAA,CAChO,EACC6D,CAAA,CAAA,EANID,CAAA,CAQR,EACA7E,EAAe,aAAa,OAAS,GACpCwE,EAAAA,KAAC,OAAA,CAAK,UAAU,qDAAqD,SAAA,CAAA,IACjExE,EAAe,aAAa,OAAS,EAAE,OAAA,CAAA,CAC3C,CAAA,CAAA,CAEJ,CAAA,EACF,QAYD,MAAA,CAAI,UAAU,8DACb,SAAAwE,EAAAA,KAAC,MAAA,CAAI,UAAU,6EACb,SAAA,CAAAvD,EAAAA,IAAC,OAAA,CAAK,MAAOwC,EACV,SAAAD,EACH,EACAvC,EAAAA,IAAC,OAAA,CAAK,UAAU,mCAAmC,SAAA,mBAAA,CAEnD,CAAA,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CAAA,CAGN,EAEA0B,GAAkB,YAAc,oBCtqBzB,MAAMsC,GAAwD,CAAC,CACpE,gBAAAzC,EACA,MAAAI,EACA,YAAAsC,EAAc,EACd,gBAAAC,EAAkB,GAClB,aAAAC,EAAe,GACf,UAAA9E,EACA,MAAAC,CACF,IAAM,SAEJa,GAAA,EAGA,MAAMiE,EAAoB7G,EAAAA,QAAQ,IACzBgE,EAAgB,MAAM,EAAG0C,CAAW,EAC1C,CAAC1C,EAAiB0C,CAAW,CAAC,EAI3BI,EAAmBhI,EACvB,mBACA,wBACAgD,CAAA,EAGIiF,EAAiB3C,GAAA,MAAAA,EAAO,YAAc,CAC1C,mBAAoBA,EAAM,WAAA,EACD,OAE3B,OAAIyC,EAAkB,SAAW,EAE7BpE,EAAAA,IAAC,MAAA,CACC,UAAWqE,EACX,MAAO,CACL,GAAGC,EACH,YAAY3C,GAAA,YAAAA,EAAO,aAAc,oEACjC,IAAGQ,EAAAR,GAAA,YAAAA,EAAO,aAAP,YAAAQ,EAAmB,aACtB,GAAG7C,CAAA,EAEL,oBAAmBqC,GAAA,YAAAA,EAAO,KAE1B,eAAC,MAAA,CAAI,UAAU,mDACb,SAAA3B,EAAAA,IAAC,IAAA,CAAE,kCAAsB,CAAA,CAC3B,CAAA,CAAA,EAMJA,EAAAA,IAAC,MAAA,CACC,UAAWqE,EACX,MAAO,CACL,GAAGC,EACH,YAAY3C,GAAA,YAAAA,EAAO,aAAc,oEACjC,IAAGmB,EAAAnB,GAAA,YAAAA,EAAO,aAAP,YAAAmB,EAAmB,aACtB,GAAGxD,CAAA,EAEL,oBAAmBqC,GAAA,YAAAA,EAAO,KAE1B,SAAA4B,EAAAA,KAAC,MAAA,CAAI,UAAU,YAEb,SAAA,CAAAA,EAAAA,KAAC,MAAA,CAAI,UAAU,cACb,SAAA,CAAAA,EAAAA,KAAC,MAAA,CAAI,UAAU,8CACb,SAAA,CAAAvD,EAAAA,IAAC,OAAI,UAAU,2CAA2C,KAAK,OAAO,OAAO,eAAe,QAAQ,YAClG,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,uMAAuM,CAAA,CAC9Q,EACAA,EAAAA,IAAC,KAAA,CAAG,UAAU,yDAAyD,SAAA,kBAAA,CAEvE,CAAA,EACF,EACAuD,EAAAA,KAAC,IAAA,CAAE,UAAU,2CACV,SAAA,CAAAa,EAAkB,OAAO,4BAAA,CAAA,CAC5B,CAAA,EACF,EAGApE,EAAAA,IAAC,OAAI,UAAU,uDACZ,WAAkB,IAAI,CAACuE,EAASC,IAC/BjB,EAAAA,KAAC,MAAA,CAEC,UAAU,2IAGV,SAAA,CAAAA,EAAAA,KAAC,MAAA,CAAI,UAAU,wCACb,SAAA,CAAAA,EAAAA,KAAC,MAAA,CAAI,UAAU,0BACZ,SAAA,CAAAiB,IAAU,GACTxE,EAAAA,IAAC,OAAA,CAAK,UAAU,qEAAqE,SAAA,YAErF,EAEFuD,EAAAA,KAAC,OAAA,CAAK,UAAU,2CAA2C,SAAA,CAAA,IACvDiB,EAAQ,CAAA,CAAA,CACZ,CAAA,EACF,EACCN,GACCX,EAAAA,KAAC,MAAA,CAAI,UAAU,6DACZ,SAAA,CAAA,KAAK,MAAMgB,EAAQ,mBAAqB,GAAG,EAAE,SAAA,CAAA,CAChD,CAAA,EAEJ,EAGAvE,EAAAA,IAAC,KAAA,CAAG,UAAU,sDACX,WAAQ,MACX,EAGCkE,GACCX,EAAAA,KAAC,MAAA,CAAI,UAAU,OACb,SAAA,CAAAA,EAAAA,KAAC,MAAA,CAAI,UAAU,kFACb,SAAA,CAAAvD,EAAAA,IAAC,QAAK,SAAA,aAAA,CAAW,EACjBuD,EAAAA,KAAC,OAAA,CAAK,UAAU,oBAAqB,SAAA,CAAA,KAAK,MAAMgB,EAAQ,mBAAqB,GAAG,EAAE,SAAA,CAAA,CAAO,CAAA,EAC3F,EACAvE,EAAAA,IAAC,MAAA,CAAI,UAAU,qEACb,SAAAA,EAAAA,IAAC,MAAA,CACC,UAAU,iBACV,MAAO,CAAE,MAAO,GAAG,KAAK,MAAMuE,EAAQ,mBAAqB,GAAG,CAAC,GAAA,CAAI,CAAA,CACrE,CACF,CAAA,EACF,EAIFhB,EAAAA,KAAC,MAAA,CAAI,UAAU,oCACZ,SAAA,CAAAgB,EAAQ,SACPhB,OAAC,OAAA,CAAK,UAAU,qDACd,SAAA,CAAAvD,EAAAA,IAAC,OAAI,UAAU,eAAe,KAAK,OAAO,OAAO,eAAe,QAAQ,YACtE,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,4IAA4I,CAAA,CACnN,EACCuE,EAAQ,OAAA,EACX,EAKDA,EAAQ,YAAcA,EAAQ,WAAa,GAC1ChB,OAAC,OAAA,CAAK,UAAU,qDACd,SAAA,CAAAvD,EAAAA,IAAC,OAAI,UAAU,eAAe,KAAK,OAAO,OAAO,eAAe,QAAQ,YACtE,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,4DAA4D,CAAA,CACnI,EACCuE,EAAQ,WAAW,YAAA,CAAA,CACtB,CAAA,EAEJ,EAGCJ,GAAgBI,EAAQ,UAAYA,EAAQ,SAAS,OAAS,GAC7DhB,EAAAA,KAAC,MAAA,CAAI,UAAU,OACb,SAAA,CAAAvD,EAAAA,IAAC,MAAA,CAAI,UAAU,gDAAgD,SAAA,gBAE/D,EACAuD,EAAAA,KAAC,MAAA,CAAI,UAAU,yBACZ,SAAA,CAAAgB,EAAQ,SAAS,MAAM,EAAG,CAAC,EAAE,IAAI,CAACZ,EAASC,IAC1CL,EAAAA,KAAC,OAAA,CAEC,UAAU,kGAEV,SAAA,CAAAvD,EAAAA,IAAC,OAAI,UAAU,sCAAsC,KAAK,OAAO,OAAO,eAAe,QAAQ,YAC7F,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,gDAAgD,CAAA,CACvH,EACC2D,CAAA,CAAA,EANIC,CAAA,CAQR,GACCW,EAAQ,SAAS,QAAU,GAAK,GAChChB,EAAAA,KAAC,OAAA,CAAK,UAAU,kDAAkD,SAAA,CAAA,IAC9DgB,EAAQ,SAAS,OAAS,EAAE,OAAA,CAAA,CAChC,CAAA,CAAA,CAEJ,CAAA,EACF,EAIFvE,EAAAA,IAACf,EAAA,CACC,KAAMsF,EAAQ,MACd,WAAYA,EAAQ,YACpB,UAAWA,EAAQ,WACnB,aAAc,CACZ,MAAOA,EAAQ,MACf,WAAYA,EAAQ,mBACpB,UAAW,mBAAA,EAGb,SAAAhB,EAAAA,KAAC,SAAA,CAAO,UAAU,6IAA6I,SAAA,CAAA,cAE7JvD,EAAAA,IAAC,OAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,YACjE,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,+EAA+E,CAAA,CACtJ,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,EA9GKuE,EAAQ,YAAcC,CAAA,CAgH9B,EACH,QAGC,MAAA,CAAI,UAAU,iGACb,SAAAjB,EAAAA,KAAC,OAAA,CAAK,UAAU,qEACd,SAAA,CAAAvD,MAAC,MAAA,CAAI,UAAU,0BAA0B,KAAK,eAAe,QAAQ,YACnE,SAAAA,EAAAA,IAAC,OAAA,CAAK,SAAS,UAAU,EAAE,mHAAmH,SAAS,UAAU,EACnK,EACAA,EAAAA,IAAC,OAAA,CAAK,UAAU,cAAc,SAAA,aAAU,EACxCA,EAAAA,IAAC,OAAA,CAAK,UAAU,qDAAqD,SAAA,QAAA,CAAM,CAAA,CAAA,CAC7E,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAGN,EAEAgE,GAAmB,YAAc,qBClNjC,MAAMS,GAA+C,CACnD,YAAa,UACb,YAAa,UACb,aAAc,YACd,QAAW,UACX,IAAO,UACP,kBAAmB,SACrB,EAGMC,GAAqD,CACzD,YAAa,IACb,YAAa,IACb,aAAc,IACd,QAAW,IACX,IAAO,IACP,kBAAmB,GACrB,EAEaC,GAA0C,CAAC,CACtD,KAAA3M,EACA,QAAA4M,EACA,KAAAC,EAAO,KACP,UAAAxF,EACA,MAAAC,CACF,IAAM,CACJ,MAAMwF,EAAmBF,GAAWH,GAAkBzM,CAAI,GAAK,YACzD+M,EAAOL,GAAe1M,CAAI,EAE1BgN,EAAe3I,EACnB,mBACA,eACA,iBAAiByI,CAAgB,GACjC,iBAAiBD,CAAI,GACrBxF,CAAA,EAGF,OACEkE,EAAAA,KAAC,OAAA,CACC,UAAWyB,EACX,MAAA1F,EAEC,SAAA,CAAAyF,GAAQ/E,EAAAA,IAAC,OAAA,CAAK,UAAU,qBAAsB,SAAA+E,EAAK,EACpD/E,EAAAA,IAAC,OAAA,CAAK,UAAU,qBAAsB,SAAAhI,CAAA,CAAK,CAAA,CAAA,CAAA,CAGjD,EAEA2M,GAAY,YAAc,cCVnB,MAAMM,GAA4D,CAAC,CACxE,eAAAlG,EACA,MAAA4C,EAAQ,CAAE,KAAM,OAAA,EAChB,UAAAtC,EAAY,GACZ,MAAAC,EACA,cAAA4F,EAAgB,GAChB,gBAAAC,EAAkB,GAClB,SAAAC,EACA,QAAAC,EACA,YAAAC,EAAc,EAChB,IAAM,yBAEJnF,GAAA,EAEA,KAAM,CAACU,EAAYkB,CAAa,EAAI5E,EAAAA,SAASgI,CAAe,EAItDI,EAAqB,IAAM,CAC3BD,GACFvD,EAAc,CAAClB,CAAU,CAC3B,EAII2E,EAAqBzG,EAAe,kBAAoB,CAAA,EAExD0G,EAAkB,CACtB,CACE,MAAO,gBACP,YAAa,oBAAoB1G,EAAe,sBAAwBA,EAAe,KAAK,mDAC5F,KAAM,GAAA,EAER,CACE,MAAO,oBACP,YAAa,6BAA6BA,EAAe,sBAAwBA,EAAe,KAAK,kEACrG,KAAM,GAAA,EAER,CACE,MAAO,GAAGA,EAAe,sBAAwBA,EAAe,KAAK,YACrE,YAAaA,EAAe,4BAA8BA,EAAe,aAAe,GAAGA,EAAe,sBAAwBA,EAAe,KAAK,+EACtJ,KAAM,GAAA,EAER,CACE,MAAO,eACP,YAAa,iCAAiCA,EAAe,sBAAwBA,EAAe,KAAK,8BACzG,KAAM,GAAA,CACR,EAIIwD,EAAmBzB,GAAoB/B,EAAgB,EAAK,EAC5DyD,EAAgBxB,GAAA,EAGhB0E,EAAkBN,IAAaI,EAAmB,OAAS,EAAIA,EAAqBC,GACpFE,EAAiBN,GAAW,OAAOtG,EAAe,sBAAwBA,EAAe,KAAK,GAG9F6G,EAAS,CACb,WAAYjE,EAAM,kBAAoBA,EAAM,OAAS,OAAS,UAAY,WAC1E,QAASA,EAAM,eAAiBA,EAAM,OAAS,OAAS,UAAY,WACpE,OAAQA,EAAM,cAAgBA,EAAM,OAAS,OAAS,UAAY,WAClE,KAAMA,EAAM,YAAcA,EAAM,OAAS,OAAS,UAAY,WAC9D,cAAeA,EAAM,qBAAuBA,EAAM,OAAS,OAAS,UAAY,WAChF,OAAQA,EAAM,aAAeA,EAAM,cAAgB,UACnD,UAAWA,EAAM,gBAAkB,UAEnC,WAAUQ,EAAAR,EAAM,YAAN,YAAAQ,EAAiB,WAAYR,EAAM,OAAS,OAAS,UAAY,WAC3E,YAAWmB,EAAAnB,EAAM,YAAN,YAAAmB,EAAiB,aAAcnB,EAAM,OAAS,OAAS,UAAY,UAAA,EAI1EkE,EAAelE,EAAM,qBAAuB,GAAK,CACrD,WAAYA,EAAM,YAAc,oEAChC,aAAcA,EAAM,cAAgB,OACpC,OAAQ,aAAaiE,EAAO,MAAM,GAClC,WAAYA,EAAO,WACnB,SAAU,SACV,SAAU,QACV,YAAW7C,EAAApB,EAAM,UAAN,YAAAoB,EAAe,UAAWpB,EAAM,OAAS,OAChD,uEACA,yEACJ,SAAU,WACV,WAAY,eAAA,EAGd,OACE4B,EAAAA,KAAC,MAAA,CACC,UAAW,2CAA2ClE,CAAS,GAC/D,MAAO,CACL,GAAGwG,EACH,IAAG7C,EAAArB,EAAM,aAAN,YAAAqB,EAAkB,eACrB,GAAG1D,CAAA,EAEL,oBAAmBqC,EAAM,KAGzB,SAAA,CAAA3B,EAAAA,IAAC,MAAA,CACC,MAAO,CACL,WAAY4F,EAAO,SACnB,QAAS,OACT,aAAc/E,GAAc,CAACyE,EAAc,aAAaM,EAAO,MAAM,GAAK,OAC1E,SAAU,WACV,WAAY,eAAA,EAGd,SAAArC,EAAAA,KAAC,MAAA,CAAI,MAAO,CAAE,QAAS,OAAQ,WAAY,SAAU,eAAgB,gBAAiB,IAAK,MAAA,EACzF,SAAA,CAAAA,EAAAA,KAAC,MAAA,CAAI,MAAO,CAAE,QAAS,OAAQ,WAAY,SAAU,IAAK,OAAQ,KAAM,EAAG,SAAU,GACnF,SAAA,CAAAvD,EAAAA,IAAC,MAAA,CACC,MAAO,CACL,MAAO,OACP,OAAQ,OACR,aAAc2B,EAAM,cAAgB,MACpC,WAAYiE,EAAO,OACnB,QAAS,OACT,WAAY,SACZ,eAAgB,SAChB,MAAO,QACP,WAAU3C,EAAAtB,EAAM,WAAN,YAAAsB,EAAgB,OAAQ,OAClC,WAAY,MACZ,YAAWC,EAAAvB,EAAM,UAAN,YAAAuB,EAAe,QAAS,+BACnC,OAAQ,aAAa0C,EAAO,MAAM,EAAA,EAGlC,YAAe,sBAAwB7G,EAAe,OAAO,OAAO,CAAC,EAAE,YAAA,CAAY,CAAA,EAEvFwE,OAAC,OAAI,MAAO,CAAE,KAAM,EAAG,SAAU,GAC/B,SAAA,CAAAvD,EAAAA,IAAC,KAAA,CACC,MAAO,CACL,OAAQ,EACR,SAAU,OACV,WAAY,MACZ,MAAO4F,EAAO,KACd,WAAY,MACZ,SAAU,SACV,aAAc,WACd,WAAY,QAAA,EAGb,SAAA7G,EAAe,sBAAwBA,EAAe,KAAA,CAAA,EAEzDwE,EAAAA,KAAC,IAAA,CACC,MAAO,CACL,OAAQ,YACR,SAAU,OACV,MAAOqC,EAAO,cACd,WAAY,MACZ,SAAU,SACV,aAAc,WACd,WAAY,QAAA,EAEd,MAAOpD,EAEN,SAAA,CAAAD,EAAiB,MAAI,IAAI,IAAIxD,EAAe,KAAOA,EAAe,WAAW,EAAE,QAAA,CAAA,CAAA,CAClF,CAAA,CACF,CAAA,EACF,EAEAwE,EAAAA,KAAC,MAAA,CAAI,MAAO,CAAE,QAAS,OAAQ,WAAY,SAAU,IAAK,MAAA,EAEvD,SAAA,CAAA,CAAC1C,GAAcyE,GACdtF,EAAAA,IAACf,EAAA,CACC,KAAMF,EAAe,MACrB,WAAYA,EAAe,YAC3B,UAAWA,EAAe,WAC1B,aAAc,CACZ,MAAOA,EAAe,sBAAwBA,EAAe,MAC7D,UAAW,kBACX,SAAU,GACV,SAAU,QAAA,EAGZ,SAAAiB,EAAAA,IAAC,SAAA,CACC,MAAO,CACL,SAASmD,EAAAxB,EAAM,UAAN,MAAAwB,EAAe,MAAQ,GAAGxB,EAAM,QAAQ,KAAK,IAAIA,EAAM,QAAQ,QAAU,MAAM,GAAK,WAC7F,gBAAiBiE,EAAO,OACxB,MAAO,QACP,OAAQ,OACR,aAAcjE,EAAM,cAAgB,MACpC,WAAUyB,EAAAzB,EAAM,WAAN,YAAAyB,EAAgB,QAAS,OACnC,WAAY,MACZ,OAAQ,UACR,WAAY,gBACZ,YAAWC,EAAA1B,EAAM,UAAN,YAAA0B,EAAe,QAAS,+BACnC,WAAY,SACZ,IAAGC,EAAA3B,EAAM,aAAN,YAAA2B,EAAkB,MAAA,EAEvB,YAAcI,GAAM,OACb/B,EAAM,uBACT+B,EAAE,cAAc,MAAM,UAAY,mBAClCA,EAAE,cAAc,MAAM,YAAYvB,EAAAR,EAAM,UAAN,YAAAQ,EAAe,SAAU,gCAC7D,EAEF,WAAauB,GAAM,OACZ/B,EAAM,uBACT+B,EAAE,cAAc,MAAM,UAAY,gBAClCA,EAAE,cAAc,MAAM,YAAYvB,EAAAR,EAAM,UAAN,YAAAQ,EAAe,QAAS,+BAC5D,EAGD,SAAAwD,CAAA,CAAA,CACH,CAAA,EAKHL,GACC/B,EAAAA,KAAC,SAAA,CACC,QAASgC,EACT,MAAO,CACL,QAAS,OACT,WAAY,SACZ,IAAK,MACL,QAAS,WACT,WAAY5D,EAAM,OAAS,OAAS,UAAY,UAChD,OAAQ,aAAaA,EAAM,OAAS,OAAS,UAAY,SAAS,GAClE,aAAc,MACd,OAAQ,UACR,MAAOA,EAAM,aAAe,UAC5B,SAAU,OACV,WAAY,MACZ,WAAY,eAAA,EAEd,aAAe+B,GAAM,CACnBA,EAAE,cAAc,MAAM,WAAa/B,EAAM,OAAS,OAAS,UAAY,UACvE+B,EAAE,cAAc,MAAM,YAAc/B,EAAM,aAAe,SAAA,EAE3D,aAAe+B,GAAM,CACnBA,EAAE,cAAc,MAAM,WAAa/B,EAAM,OAAS,OAAS,UAAY,UACvE+B,EAAE,cAAc,MAAM,YAAc/B,EAAM,OAAS,OAAS,UAAY,SAAA,EAE1E,aAAYd,EAAa,oBAAsB,oBAE/C,SAAA,CAAAb,EAAAA,IAAC,OAAA,CAAM,SAAAa,EAAa,eAAiB,eAAe,EACpDb,EAAAA,IAAC,MAAA,CACC,MAAM,KACN,OAAO,KACP,QAAQ,YACR,KAAK,OACL,OAAO,eACP,YAAY,IACZ,cAAc,QACd,eAAe,QAEd,SAAAa,EAECb,EAAAA,IAAC,OAAA,CAAK,EAAE,UAAA,CAAW,EAGnBuD,OAAAO,EAAAA,SAAA,CACE,SAAA,CAAA9D,MAAC,UAAO,GAAG,KAAK,GAAG,KAAK,EAAE,KAAK,EAC/BA,EAAAA,IAAC,OAAA,CAAK,EAAE,WAAA,CAAY,EACpBA,EAAAA,IAAC,OAAA,CAAK,EAAE,WAAA,CAAY,CAAA,CAAA,CACtB,CAAA,CAAA,CAEJ,CAAA,CAAA,CACF,CAAA,CAEJ,CAAA,CAAA,CACF,CAAA,CAAA,GAIAa,GAAc,CAACyE,IACf/B,EAAAA,KAAC,OAAI,MAAO,CAAE,QAAS,GAAA,EAEpB,SAAA,CAAAmC,EAAgB,IAAI,CAACI,EAAStB,IAC7BjB,EAAAA,KAAC,MAAA,CAEC,MAAO,CACL,QAAS,OACT,gBAAiBiB,EAAQ,IAAM,EAAIoB,EAAO,WAAaA,EAAO,UAC9D,aAAcpB,EAAQkB,EAAgB,OAAS,EAAI,aAAaE,EAAO,MAAM,GAAK,MAAA,EAGpF,SAAA,CAAArC,EAAAA,KAAC,KAAA,CACC,MAAO,CACL,OAAQ,aACR,SAAU,OACV,WAAY,MACZ,MAAOqC,EAAO,KACd,QAAS,OACT,WAAY,SACZ,IAAK,MAAA,EAGN,SAAA,CAAAE,EAAQ,MAAQ9F,MAAC,OAAA,CAAM,SAAA8F,EAAQ,KAAK,EACpCA,EAAQ,KAAA,CAAA,CAAA,EAEX9F,EAAAA,IAAC,IAAA,CACC,MAAO,CACL,OAAQ,EACR,SAAU,OACV,MAAO4F,EAAO,cACd,WAAY,KAAA,EAGb,SAAAE,EAAQ,WAAA,CAAA,CACX,CAAA,EA9BKtB,CAAA,CAgCR,GAGC3D,GAAc,CAACyE,IACftF,EAAAA,IAAC,MAAA,CAAI,MAAO,CAAE,QAAS,OAAQ,UAAW,aAAa4F,EAAO,MAAM,GAAI,gBAAiBA,EAAO,YAC9F,SAAA5F,EAAAA,IAACf,EAAA,CACC,KAAMF,EAAe,MACrB,WAAYA,EAAe,YAC3B,UAAWA,EAAe,WAC1B,aAAc,CACZ,MAAOA,EAAe,MACtB,UAAW,kBACX,SAAU8B,EACV,SAAU,QAAA,EAGZ,SAAAb,EAAAA,IAAC,SAAA,CACC,MAAO,CACL,MAAO,OACP,QAAS,YACT,WAAY4F,EAAO,OACnB,MAAO,QACP,OAAQ,OACR,aAAc,OACd,SAAU,OACV,WAAY,MACZ,OAAQ,UACR,WAAY,gBACZ,UAAW,qCACX,SAAU,WACV,SAAU,QAAA,EAEZ,YAAclC,GAAM,CAClBA,EAAE,cAAc,MAAM,UAAY,+BAClCA,EAAE,cAAc,MAAM,UAAY,oCAAA,EAEpC,WAAaA,GAAM,CACjBA,EAAE,cAAc,MAAM,UAAY,yBAClCA,EAAE,cAAc,MAAM,UAAY,oCAAA,EAGnC,SAAAiC,CAAA,CAAA,CACH,CAAA,EAEJ,EAIDT,GACClF,EAAAA,IAAC,MAAA,CACC,MAAO,CACL,QAAS,WACT,UAAW,aAAa4F,EAAO,MAAM,GACrC,gBAAiBA,EAAO,QAAA,EAG1B,SAAArC,EAAAA,KAAC,MAAA,CACC,MAAO,CACL,SAAU,OACV,MAAOqC,EAAO,cACd,UAAW,QAAA,EAEd,SAAA,CAAA,cACY5F,MAAC,UAAO,MAAO,CAAE,MAAO4F,EAAO,IAAA,EAAQ,SAAA,QAAA,CAAM,CAAA,CAAA,CAAA,CAC1D,CAAA,CACF,CAAA,CAEJ,CAAA,CAAA,CAAA,CAIR,ECtZaG,EAAwE,CAAC,CACpF,eAAAhH,EACA,MAAA4C,EACA,QAAAZ,EAAU,GACV,WAAAiF,EAAa,GACb,UAAA3G,EACA,MAAAC,CACF,IAAM,OACJ,MAAMmD,EAAuB,KAAK,MAAM1D,EAAe,mBAAqB,GAAG,EAGzEwD,EAAmBzB,GAAoB/B,EAAgBgC,CAAO,EAC9DyB,EAAgBxB,GAAA,EAEhBqD,EAAmBhI,EACvB,+BACA,mDACA,CACE,+IAAgJ,CAAC0E,EACjJ,mEAAoEA,CAAA,EAEtE1B,CAAA,EAGIiF,EAAiB3C,GAAA,MAAAA,EAAO,YAAc,CAC1C,mBAAoBA,EAAM,WAAA,EACD,OAE3B,OACE3B,EAAAA,IAACf,EAAA,CACC,KAAMF,EAAe,MACrB,WAAYA,EAAe,YAC3B,UAAWA,EAAe,WAC1B,aAAc,CACZ,MAAOA,EAAe,MACtB,WAAYA,EAAe,kBAAA,EAE7B,UAAWsF,EACX,MAAO,CACL,YAAY1C,GAAA,YAAAA,EAAO,aAAc,oEACjC,IAAGQ,EAAAR,GAAA,YAAAA,EAAO,aAAP,YAAAQ,EAAmB,qBACtB,GAAG7C,CAAA,EAGL,SAAAiE,EAAAA,KAAC,MAAA,CACC,UAAU,yBACV,MAAOe,EACP,oBAAmB3C,GAAA,YAAAA,EAAO,KAG1B,SAAA,CAAA3B,EAAAA,IAAC,MAAA,CAAI,UAAU,uBACZ,SAAAjB,EAAe,cAAgBA,EAAe,aAAa,OAAS,EACnEiB,MAAC,MAAA,CAAI,UAAU,mFACb,SAAAA,EAAAA,IAAC,MAAA,CACC,IAAKjB,EAAe,aAAa,CAAC,EAAE,IACpC,IAAKA,EAAe,sBAAwBA,EAAe,MAC3D,UAAU,4BAAA,CAAA,EAEd,EACEA,EAAe,aACjBiB,EAAAA,IAAC,MAAA,CAAI,UAAU,mFACb,SAAAA,EAAAA,IAAC,MAAA,CACC,IAAKjB,EAAe,aAAa,IACjC,IAAKA,EAAe,sBAAwBA,EAAe,MAC3D,UAAU,4BAAA,CAAA,CACZ,CACF,EACEA,EAAe,oBAAsB,GACvCiB,EAAAA,IAAC,MAAA,CAAI,UAAU,mCAAA,CAAoC,EAEnDA,EAAAA,IAAC,MAAA,CAAI,UAAU,mCAAmC,EAEtD,EAGAuD,EAAAA,KAAC,MAAA,CAAI,UAAU,iBACb,SAAA,CAAAA,EAAAA,KAAC,MAAA,CAAI,UAAU,mEACb,SAAA,CAAAvD,MAAC,MAAG,UAAW3D,EACb,2DACA,gFACA,iCACA0E,EAAU,uBAAyB,sBAAA,EAElC,SAAAhC,EAAe,sBAAwBA,EAAe,KAAA,CACzD,EAGCA,EAAe,oBAAsB,IACpCwE,EAAAA,KAAC,QAAK,UAAWlH,EACf,0GACA0C,EAAe,oBAAsB,GACjC,uEACA,kEAAA,EAEH,SAAA,CAAA0D,EAAqB,SAAA,CAAA,CACxB,CAAA,EAEJ,EAGCuD,IAAejH,EAAe,4BAA8BA,EAAe,SAC1EiB,MAAC,KAAE,UAAW3D,EACZ,gDACA0E,EAAU,UAAY,SAAA,EAErB,SAAAhC,EAAe,4BAA8BA,EAAe,MAAA,CAC/D,EAIFiB,EAAAA,IAAC,IAAA,CACC,UAAW3D,EACT,wCACU,SAAY,EAExB,MAAOmG,EAEN,SAAAD,CAAA,CAAA,EAIF,CAACxB,GAAWhC,EAAe,UAAYA,EAAe,SAAS,OAAS,GACvEwE,EAAAA,KAAC,MAAA,CAAI,UAAU,4BACZ,SAAA,CAAAxE,EAAe,SAAS,MAAM,EAAG,CAAC,EAAE,IAAI,CAACqD,EAASoC,IACjDxE,EAAAA,IAAC,OAAA,CAEC,UAAU,kIAET,SAAAoC,CAAA,EAHIoC,CAAA,CAKR,EACAzF,EAAe,SAAS,OAAS,GAChCwE,EAAAA,KAAC,OAAA,CAAK,UAAU,2CAA2C,SAAA,CAAA,IACvDxE,EAAe,SAAS,OAAS,EAAE,OAAA,CAAA,CACvC,CAAA,EAEJ,EAID,CAACgC,GAAWhC,EAAe,YAAcA,EAAe,WAAa,GACpEiB,EAAAA,IAAC,MAAA,CAAI,UAAU,+BACb,SAAAuD,EAAAA,KAAC,OAAA,CAAK,UAAU,oIACb,SAAA,CAAAxE,EAAe,WAAW,YAAA,CAAA,CAC7B,CAAA,CACF,CAAA,EAEJ,EAGAiB,EAAAA,IAAC,MAAA,CAAI,UAAU,qEACb,SAAAA,EAAAA,IAAC,MAAA,CACC,UAAU,2CACV,KAAK,OACL,OAAO,eACP,QAAQ,YAER,SAAAA,EAAAA,IAAC,OAAA,CACC,cAAc,QACd,eAAe,QACf,YAAa,EACb,EAAE,cAAA,CAAA,CACJ,CAAA,CACF,CACF,CAAA,CAAA,CAAA,CACF,CAAA,CAGN,EC5KaiG,GAAsE,CAAC,CAClF,gBAAA1E,EACA,oBAAA2E,EACA,MAAAvE,EACA,uBAAAwE,EAAyB,EACzB,sBAAAC,EACA,uBAAAC,EACA,UAAAhH,CACF,IAAM,CACJ,MAAMiH,EAAqB/E,EACxB,KAAK,CAACgF,EAAGC,IAAMA,EAAE,mBAAqBD,EAAE,kBAAkB,EAC1D,MAAM,EAAGJ,CAAsB,EAE5B9B,EAAmBhI,EACvB,8BACA,yBACA,oEACA,YACAgD,CAAA,EAGIiF,EAAiB3C,GAAA,MAAAA,EAAO,YAAc,CAC1C,mBAAoBA,EAAM,WAAA,EACD,OAE3B,OACE4B,EAAAA,KAAC,MAAA,CACC,UAAWc,EACX,MAAOC,EACP,oBAAmB3C,GAAA,YAAAA,EAAO,KAG1B,SAAA,CAAA4B,EAAAA,KAAC,MAAA,CAAI,UAAU,+BACb,SAAA,CAAAvD,EAAAA,IAAC,MAAA,CAAI,UAAU,gBACb,SAAAA,EAAAA,IAAC,MAAA,CAAI,UAAU,+FACb,SAAAA,EAAAA,IAAC,MAAA,CAAI,UAAU,mDAAmD,KAAK,OAAO,OAAO,eAAe,QAAQ,YAC1G,SAAAA,EAAAA,IAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,+CAAA,CAAgD,CAAA,CACvH,EACF,EACF,EACAuD,EAAAA,KAAC,MAAA,CAAI,UAAU,iBACb,SAAA,CAAAvD,EAAAA,IAAC,KAAA,CAAG,UAAU,gEAAgE,SAAA,uBAE9E,EACAA,EAAAA,IAAC,IAAA,CAAE,UAAU,sDAAsD,SAAA,4CAAA,CAEnE,CAAA,CAAA,CACF,CAAA,EACF,EAGAA,MAAC,MAAA,CAAI,UAAU,OACb,eAAC,MAAA,CAAI,UAAU,yFACb,SAAAA,EAAAA,IAAC,IAAA,CAAE,UAAU,mDACV,SAAAkG,CAAA,CACH,EACF,EACF,EAGCI,EAAmB,OAAS,GAC3B/C,EAAAA,KAAC,MAAA,CAAI,UAAU,OACb,SAAA,CAAAA,EAAAA,KAAC,MAAA,CAAI,UAAU,+BACb,SAAA,CAAAvD,EAAAA,IAAC,OAAI,UAAU,qCAAqC,KAAK,OAAO,OAAO,eAAe,QAAQ,YAC5F,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,6BAA6B,CAAA,CACpG,EACAA,EAAAA,IAAC,KAAA,CAAG,UAAU,yCAAyC,SAAA,qBAAA,CAEvD,CAAA,EACF,EAEAA,EAAAA,IAAC,MAAA,CAAI,UAAU,YACZ,SAAAsG,EAAmB,IAAI,CAACvH,EAAgByF,IACvCjB,EAAAA,KAAC,MAAA,CAAwC,UAAU,WAEjD,SAAA,CAAAvD,MAAC,MAAA,CAAI,UAAU,8BACb,SAAAA,MAAC,OAAI,UAAW3D,EACd,0EACAmI,IAAU,EAAI,oDACdA,IAAU,EAAI,0DACd,yDAAA,EAEC,SAAAA,EAAQ,CAAA,CACX,CAAA,CACF,EAEAxE,EAAAA,IAAC,MAAA,CAAI,UAAU,OACb,SAAAA,EAAAA,IAAC+F,EAAA,CACC,eAAAhH,EACA,MAAA4C,EACA,QAAS,GACT,WAAY,GACZ,QAASyE,CAAA,CAAA,CACX,CACF,CAAA,CAAA,EArBQrH,EAAe,OAASyF,CAsBlC,CACD,CAAA,CACH,CAAA,EACF,EAIDjD,EAAgB,OAAS4E,GACxBnG,EAAAA,IAAC,OAAI,UAAU,OACb,SAAAA,EAAAA,IAAC,MAAA,CAAI,UAAU,0FACb,SAAAuD,EAAAA,KAAC,MAAA,CAAI,UAAU,0BACb,SAAA,CAAAvD,EAAAA,IAAC,OAAI,UAAU,qCAAqC,KAAK,OAAO,OAAO,eAAe,QAAQ,YAC5F,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,4DAA4D,CAAA,CACnI,EACAuD,EAAAA,KAAC,OAAA,CAAK,UAAU,uDACb,SAAA,CAAAhC,EAAgB,OAAS4E,EAAuB,6BAA2B5E,EAAgB,OAAS4E,EAAyB,EAAI,IAAM,GAAG,YAAA,CAAA,CAC7I,CAAA,CAAA,CACF,EACF,EACF,EAIF5C,EAAAA,KAAC,MAAA,CAAI,UAAU,kCACZ,SAAA,CAAA8C,GACC9C,EAAAA,KAAC,SAAA,CACC,QAAS8C,EACT,UAAU,0MAEV,SAAA,CAAArG,EAAAA,IAAC,OAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,YACjE,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,gKAAgK,CAAA,CACvO,EAAM,wBAAA,CAAA,CAAA,EAKVuD,EAAAA,KAAC,SAAA,CACC,QAAS,IAAM,CACT+C,EAAmB,OAAS,IAC9BF,GAAA,MAAAA,EAAwBE,EAAmB,CAAC,EAAE,MAAOA,EAAmB,CAAC,EAAE,aAC7E,EAEF,UAAU,wPAEV,SAAA,CAAAtG,EAAAA,IAAC,OAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,YACjE,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,+EAA+E,CAAA,CACtJ,EAAM,eAAA,CAAA,CAAA,CAER,EACF,EAGAA,EAAAA,IAAC,OAAI,UAAU,8EACb,eAAC,OAAA,CAAK,UAAU,2CAA2C,SAAA,mBAAA,CAE3D,CAAA,CACF,CAAA,CAAA,CAAA,CAGN,EC1JayG,GAAkE,CAAC,CAC9E,eAAA1H,EACA,eAAA2H,EACA,cAAAC,EAAgB,WAChB,MAAAhF,EACA,YAAAiF,EAAc,GACd,QAAAC,EACA,UAAAxH,EACA,MAAAC,CACF,IAAM,CACJ,KAAM,CAACwH,EAAWC,CAAY,EAAI5J,EAAAA,SAAS,EAAK,EAE1C6J,EAAmB,IAAM,CAC7BD,EAAa,EAAI,EACjBF,GAAA,MAAAA,EAAU9H,EAAc,EAGpBkI,EAAmB,IAAM,CAC7BF,EAAa,EAAK,CAAA,EAMdG,EAAqB,IAAM,CAC/B,OAAQP,EAAA,CACN,IAAK,YACH,MAAO,IAAID,CAAc,IAC3B,IAAK,cACH,OAAOA,EAAe,SAAA,EACxB,IAAK,WACL,QACE,OAAOA,EAAe,SAAA,CAAS,CACnC,EAGIS,EAAkB9K,EACtB,4BACA,0CACA,6CACA,gFACA,cACA,CAEE,uJAAwJsK,IAAkB,WAG1K,+BAAgCA,IAAkB,YAGlD,sCAAuCA,IAAkB,aAAA,EAE3DtH,CAAA,EAGIiF,EAAiB3C,GAAA,MAAAA,EAAO,YAAc,CAC1C,mBAAoBA,EAAM,WAAA,EACD,OAE3B,OACE4B,EAAAA,KAAC,OAAA,CAAK,UAAU,wBACd,SAAA,CAAAvD,EAAAA,IAACf,EAAA,CACC,KAAMF,EAAe,MACrB,WAAYA,EAAe,YAC3B,UAAWA,EAAe,WAC1B,aAAc,CACZ,MAAOA,EAAe,MACtB,WAAYA,EAAe,mBAC3B,eAAA2H,EACA,cAAAC,CAAA,EAEF,UAAWQ,EACX,MAAA7H,EAEA,SAAAU,EAAAA,IAAC,OAAA,CACC,MAAOsE,EACP,oBAAmB3C,GAAA,YAAAA,EAAO,KAC1B,aAAcqF,EACd,aAAcC,EAEb,SAAAC,EAAA,CAAmB,CAAA,CACtB,CAAA,EAIDN,GAAeE,GACd9G,EAAAA,IAAC,MAAA,CAAI,UAAU,qEACb,SAAAuD,EAAAA,KAAC,MAAA,CAAI,UAAU,6GACb,SAAA,CAAAvD,EAAAA,IAAC,MAAA,CAAI,UAAU,qBAAsB,SAAAjB,EAAe,MAAM,EACzDA,EAAe,QACdiB,EAAAA,IAAC,OAAI,UAAU,2CACZ,WAAe,OAAO,OAAS,IAC5B,GAAGjB,EAAe,OAAO,UAAU,EAAG,GAAG,CAAC,MAC1CA,EAAe,OAErB,EAEDA,EAAe,oBAAsB,IACpCwE,EAAAA,KAAC,MAAA,CAAI,UAAU,kDACZ,SAAA,CAAA,KAAK,MAAMxE,EAAe,mBAAqB,GAAG,EAAE,SAAA,EACvD,EAEFiB,EAAAA,IAAC,MAAA,CAAI,UAAU,uDAAuD,SAAA,8BAEtE,EAEAA,EAAAA,IAAC,MAAA,CAAI,UAAU,4JAAA,CAA6J,CAAA,CAAA,CAC9K,CAAA,CACF,CAAA,EAEJ,CAEJ,EC/GaoH,GAAwD,CAAC,CACpE,gBAAA7F,EACA,iBAAA8F,EACA,MAAA1F,EACA,iBAAA2F,EAAmB,GACnB,cAAAX,EAAgB,WAChB,gBAAAY,EACA,UAAAlI,EACA,MAAAC,CACF,IAAM,OACJ,KAAM,CAACkI,EAAuBC,CAAwB,EAAItK,EAAAA,SAAsC,IAAI,EAG9FuK,EAAmBnK,EAAAA,QAAQ,IAAM,CACrC,GAAI,CAAC8J,GAAoB9F,EAAgB,SAAW,EAClD,MAAO,CAAE,KAAM8F,EAAkB,YAAa,IAAI,GAAI,EAGxD,IAAIM,EAAgBN,EACpB,MAAMO,MAAkB,IAOxB,MAJ8B,CAAC,GAAGrG,CAAe,EAC9C,KAAK,CAACgF,EAAGC,IAAMA,EAAE,mBAAqBD,EAAE,kBAAkB,EAGvC,QAAQ,CAACxH,EAAgByF,IAAU,CACvD,MAAMkC,EAAiBlC,EAAQ,EACzBqD,EAAQ9I,EAAe,MAG7B6I,EAAY,IAAIlB,EAAgB3H,CAAc,EAG9C,MAAM+I,EAAa,IAAI,OAAO,MAAMD,EAAM,QAAQ,sBAAuB,MAAM,CAAC,MAAO,IAAI,EAG3F,GAAIC,EAAW,KAAKH,CAAa,EAC/BA,EAAgBA,EAAc,QAAQG,EAAaC,GAC1C,GAAGA,CAAK,cAAcrB,CAAc,IAC5C,MACI,CAGL,MAAMsB,EAAWjJ,EAAe,UAAY,CAAA,EAC5C,IAAIkJ,EAAW,GAEf,UAAW7F,KAAW4F,EAAU,CAC9B,MAAME,EAAe,IAAI,OAAO,MAAM9F,EAAQ,QAAQ,sBAAuB,MAAM,CAAC,MAAO,IAAI,EAC/F,GAAI8F,EAAa,KAAKP,CAAa,GAAK,CAACM,EAAU,CACjDN,EAAgBA,EAAc,QAAQO,EAAeH,IACnDE,EAAW,GACJ,GAAGF,CAAK,cAAcrB,CAAc,KAC5C,EACD,KAAA,CACF,CAIGuB,IACHN,GAAiB,cAAcjB,CAAc,KAC/C,CACF,CACD,EAEM,CAAE,KAAMiB,EAAe,YAAAC,CAAA,CAAY,EACzC,CAACP,EAAkB9F,CAAe,CAAC,EAGhC4G,EAA0B,IAAM,CACpC,KAAM,CAAE,KAAAC,EAAM,YAAAR,CAAA,EAAgBF,EAG9B,OAFcU,EAAK,MAAM,wBAAwB,EAEpC,IAAI,CAACC,EAAM7D,IAAU,CAChC,MAAM8D,EAAgBD,EAAK,MAAM,wBAAwB,EAEzD,GAAIC,EAAe,CACjB,MAAM5B,EAAiB,SAAS4B,EAAc,CAAC,CAAC,EAC1CvJ,EAAiB6I,EAAY,IAAIlB,CAAc,EAErD,GAAI3H,EACF,OACEiB,EAAAA,IAACyG,GAAA,CAEC,eAAA1H,EACA,eAAA2H,EACA,cAAAC,EACA,MAAAhF,EACA,YAAa,GACb,QAAUH,GAAQ,CAChBiG,EAAyBjG,CAAG,EAC5B+F,GAAA,MAAAA,EAAkB/F,EAAG,CACvB,EATK,YAAYkF,CAAc,IAAIlC,CAAK,EAAA,CAY9C,CAGF,OAAOxE,EAAAA,IAAC,OAAA,CAAkB,SAAAqI,CAAA,EAAR7D,CAAa,CAAA,CAChC,CAAA,EAGGH,EAAmBhI,EACvB,uBACA,YACAgD,CAAA,EAGIiF,EAAiB3C,GAAA,MAAAA,EAAO,YAAc,CAC1C,mBAAoBA,EAAM,WAAA,EACD,OAE3B,OACE4B,EAAAA,KAAC,MAAA,CACC,UAAWc,EACX,MAAO,CACL,GAAGC,EACH,YAAY3C,GAAA,YAAAA,EAAO,aAAc,oEACjC,IAAGQ,EAAAR,GAAA,YAAAA,EAAO,aAAP,YAAAQ,EAAmB,aACtB,GAAG7C,CAAA,EAEL,oBAAmBqC,GAAA,YAAAA,EAAO,KAG1B,SAAA,CAAA3B,EAAAA,IAAC,MAAA,CAAI,UAAU,wEACZ,SAAAmI,EAAA,EACH,EAGCb,GAAoB/F,EAAgB,OAAS,GAC5CvB,EAAAA,IAAC,MAAA,CAAI,UAAU,uBACb,SAAAuD,EAAAA,KAAC,MAAA,CAAI,UAAU,sDACb,SAAA,CAAAA,EAAAA,KAAC,KAAA,CAAG,UAAU,sFACZ,SAAA,CAAAvD,EAAAA,IAAC,OAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,YACjE,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,qIAAqI,CAAA,CAC5M,EAAM,YAAA,EAER,QAEC,MAAA,CAAI,UAAU,YACZ,SAAAuB,EACE,KAAK,CAACgF,EAAGC,IAAMA,EAAE,mBAAqBD,EAAE,kBAAkB,EAC1D,IAAI,CAACxH,EAAgByF,IACpBjB,EAAAA,KAAC,MAAA,CAEC,UAAWlH,EACT,uEACA,CACE,8EACEmL,GAAA,YAAAA,EAAuB,SAAUzI,EAAe,MAClD,+CACEyI,GAAA,YAAAA,EAAuB,SAAUzI,EAAe,KAAA,CACpD,EAIF,SAAA,CAAAiB,EAAAA,IAAC,MAAA,CAAI,UAAU,qBACb,SAAAA,EAAAA,IAACyG,GAAA,CACC,eAAA1H,EACA,eAAgByF,EAAQ,EACxB,cAAAmC,EACA,MAAAhF,EACA,YAAa,EAAA,CAAA,EAEjB,EAGA3B,EAAAA,IAAC,MAAA,CAAI,UAAU,iBACb,SAAAA,EAAAA,IAAC+F,EAAA,CACC,eAAAhH,EACA,MAAA4C,EACA,QAAS,GACT,WAAY,EAAA,CAAA,CACd,CACF,CAAA,CAAA,EA9BK5C,EAAe,OAASyF,CAAA,CAgChC,CAAA,CACL,CAAA,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CAAA,CAIR,ECrLa+D,GAAoE,CAAC,CAChF,gBAAAhH,EACA,OAAAtJ,EACA,MAAA0J,EACA,oBAAAuE,EACA,UAAAsC,EACA,sBAAApC,EACA,UAAAqC,EACA,UAAApJ,CACF,IAAM,CACJ,KAAM,CAACqJ,EAAWC,CAAY,EAAIxL,EAAAA,SAASlF,EAAO,WAAa,EAAK,EAC9D,CAAC2Q,EAAaC,CAAc,EAAI1L,EAAAA,SAAS,EAAK,EAcpD,GAZAuC,EAAAA,UAAU,IAAM,CACd,GAAIzH,EAAO,SAAWA,EAAO,QAAU,EAAG,CACxC,MAAM6Q,EAAQ,WAAW,IAAM,CAC7BH,EAAa,EAAI,EACjBE,EAAe,EAAI,CAAA,EAClB5Q,EAAO,OAAO,EACjB,MAAO,IAAM,aAAa6Q,CAAK,CAAA,MAE/BD,EAAe,EAAI,CACrB,EACC,CAAC5Q,EAAO,OAAO,CAAC,EAEf,CAACyQ,GAAanH,EAAgB,SAAW,EAC3C,OAAO,KAGT,MAAMwH,EAAqB9Q,EAAO,oBAAsB,EAClD+Q,EAAyBzH,EAAgB,MAAM,EAAGwH,CAAkB,EAEpEE,EAA4B,CAACtK,EAAcO,IAAuB,CACtEkH,GAAA,MAAAA,EAAwBzH,EAAMO,EAAU,EAGpCgK,EAAgB,IAAM,CAC1BP,EAAa,EAAK,EAClBF,GAAA,MAAAA,GAAY,EAIRU,EAAgB,IAAM,CAC1B,OAAQlR,EAAO,YAAA,CACb,IAAK,UACH,OAAOiO,EACLlG,EAAAA,IAACiG,GAAA,CACC,gBAAiB+C,EACjB,oBAAA9C,EACA,MAAAvE,EACA,uBAAwBoH,EACxB,sBAAuBE,EACvB,uBAAwBR,CAAA,CAAA,EAExB,KAEN,IAAK,SACH,OACEzI,MAAC,OAAI,UAAU,YACZ,WAAuB,IAAI,CAACjB,EAAgByF,IAC3CxE,EAAAA,IAAC+F,EAAA,CAEC,eAAAhH,EACA,MAAA4C,EACA,QAAS,GACT,WAAY,GACZ,QAASsH,CAAA,EALJlK,EAAe,OAASyF,CAAA,CAOhC,EACH,EAGJ,IAAK,UACH,OAAOwE,EAAuB,OAAS,EACrCzF,EAAAA,KAAC,MAAA,CAAI,UAAU,sBACb,SAAA,CAAAA,EAAAA,KAAC,MAAA,CAAI,UAAU,+BACb,SAAA,CAAAvD,EAAAA,IAAC,MAAA,CAAI,UAAU,kCAAA,CAAmC,EAClDuD,EAAAA,KAAC,OAAA,CAAK,UAAU,uDACb,SAAA,CAAAyF,EAAuB,OAAO,qBAAmBA,EAAuB,OAAS,EAAI,KAAO,GAAG,QAAA,CAAA,CAClG,CAAA,EACF,EACAhJ,EAAAA,IAAC+F,EAAA,CACC,eAAgBiD,EAAuB,CAAC,EACxC,MAAArH,EACA,QAAS,GACT,WAAY,GACZ,QAASsH,CAAA,CAAA,EAEVD,EAAuB,OAAS,GAC/BzF,EAAAA,KAAC,MAAA,CAAI,UAAU,gDAAgD,SAAA,CAAA,IAC3DyF,EAAuB,OAAS,EAAE,uBAAqBA,EAAuB,OAAS,EAAI,IAAM,EAAA,CAAA,CACrG,CAAA,CAAA,CAEJ,EACE,KAEN,IAAK,WACH,OAAO9C,EACLlG,EAAAA,IAACoH,GAAA,CACC,gBAAiB4B,EACjB,iBAAkB9C,EAClB,MAAAvE,EACA,iBAAkB,GAClB,cAAc,WACd,sBAAuBsH,CAAA,CAAA,EAEvB,KAEN,IAAK,WACH,OACE1F,EAAAA,KAAC,MAAA,CAAI,UAAU,wHACb,SAAA,CAAAA,EAAAA,KAAC,MAAA,CAAI,UAAU,wCACb,SAAA,CAAAA,EAAAA,KAAC,MAAA,CAAI,UAAU,0BACb,SAAA,CAAAvD,EAAAA,IAAC,MAAA,CAAI,UAAU,kCAAA,CAAmC,EAClDA,EAAAA,IAAC,OAAA,CAAK,UAAU,yDAAyD,SAAA,qBAAA,CAEzE,CAAA,EACF,EACCyI,GACCzI,EAAAA,IAAC,SAAA,CACC,QAASkJ,EACT,UAAU,+EACV,aAAW,0BAEX,SAAAlJ,EAAAA,IAAC,OAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,YACjE,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,uBAAuB,CAAA,CAC9F,CAAA,CAAA,CACF,EAEJ,EACAA,EAAAA,IAAC,OAAI,UAAU,YACZ,WAAuB,IAAI,CAACjB,EAAgByF,IAC3CxE,EAAAA,IAAC+F,EAAA,CAEC,eAAAhH,EACA,MAAA4C,EACA,QAAS,GACT,WAAY,GACZ,QAASsH,CAAA,EALJlK,EAAe,OAASyF,CAAA,CAOhC,CAAA,CACH,CAAA,EACF,EAGJ,QACE,OACExE,MAAC,OAAI,UAAU,YACZ,WAAuB,IAAI,CAACjB,EAAgByF,IAC3CxE,EAAAA,IAAC0B,GAAA,CAEC,eAAA3C,EACA,MAAA4C,EACA,eAAgB,GAChB,WAAY,GACZ,QAASsH,CAAA,EALJlK,EAAe,OAASyF,CAAA,CAOhC,EACH,CAAA,CAEN,EAGIH,EAAmBhI,EACvB,6BACA,0CACA,CACE,0BAA2B,CAACuM,EAC5B,4BAA6BA,EAC7B,uCAAwC3Q,EAAO,cAAgB,WAC/D,OAAQA,EAAO,cAAgB,SAC/B,2DAA4DA,EAAO,cAAgB,SAAA,EAErFoH,CAAA,EAGIiF,EAAiB3C,GAAA,MAAAA,EAAO,YAAc,CAC1C,mBAAoBA,EAAM,WAAA,EACD,OAE3B,OACE4B,EAAAA,KAAC,MAAA,CACC,UAAWc,EACX,MAAOC,EACP,oBAAmB3C,GAAA,YAAAA,EAAO,KAC1B,sBAAqB1J,EAAO,QAC5B,kBAAiBuQ,EAEhB,SAAA,CAAAW,EAAA,EAGAlR,EAAO,gBAAkB,IACxB+H,EAAAA,IAAC,MAAA,CAAI,UAAU,wBACb,SAAAA,EAAAA,IAAC,OAAA,CAAK,UAAU,2CAA2C,SAAA,mBAAA,CAE3D,CAAA,CACF,CAAA,CAAA,CAAA,CAIR,EC3MaoJ,GAAsD,CAAC,CAClE,QAAA1L,EACA,MAAAiE,EACA,sBAAAyE,EACA,UAAA/G,CACF,IAAM,CACJ,MAAMgK,EAAS3L,EAAQ,OAAS,OAC1B4L,EAAc5L,EAAQ,OAAS,YAE/B6L,EAAiBlN,EACrB,sBACA,yBACA,CACE,mBAAoBgN,CAAA,EAEtBhK,CAAA,EAGImK,EAAgBnN,EACpB,oDACA,CACE,0DAA2DgN,EAC3D,iEAAkEC,EAClE,wEAAyE5L,EAAQ,OAAS,QAAA,CAC5F,EAGI4G,EAAiB3C,GAAA,MAAAA,EAAO,YAAc,CAC1C,mBAAoBA,EAAM,WAAA,EACD,OAErB8H,EAAcC,GACXA,EAAU,mBAAmB,GAAI,CAAE,KAAM,UAAW,OAAQ,UAAW,EAGhF,OACEnG,EAAAA,KAAC,MAAA,CACC,UAAWgG,EACX,MAAOjF,EACP,oBAAmB3C,GAAA,YAAAA,EAAO,KAGzB,SAAA,CAAA,CAAC0H,GACArJ,EAAAA,IAAC,MAAA,CAAI,UAAU,oHACb,SAAAA,EAAAA,IAAC,MAAA,CAAI,UAAU,qBAAqB,KAAK,OAAO,OAAO,eAAe,QAAQ,YAC5E,SAAAA,EAAAA,IAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,4BAAA,CAA6B,CAAA,CACpG,EACF,EAGDqJ,GACCrJ,EAAAA,IAAC,MAAA,CAAI,UAAU,oGACb,SAAAA,EAAAA,IAAC,MAAA,CAAI,UAAU,2CAA2C,KAAK,OAAO,OAAO,eAAe,QAAQ,YAClG,SAAAA,EAAAA,IAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,qEAAA,CAAsE,CAAA,CAC7I,EACF,SAID,MAAA,CAAI,UAAW,iBAAiBqJ,EAAS,YAAc,aAAa,UAEnE,SAAA,CAAArJ,EAAAA,IAAC,MAAA,CAAI,UAAWwJ,EACd,SAAAxJ,EAAAA,IAAC,OAAI,UAAU,kCACZ,SAAAtC,EAAQ,OAAA,CACX,CAAA,CACF,EAGAsC,MAAC,OAAI,UAAW3D,EACd,gDACA,CAAE,aAAcgN,CAAA,CAAO,EAEtB,SAAAI,EAAW/L,EAAQ,SAAS,CAAA,CAC/B,EAGCA,EAAQ,iBAAmBA,EAAQ,gBAAgB,OAAS,GAC3D6F,EAAAA,KAAC,MAAA,CAAI,UAAU,uBAEb,SAAA,CAAAA,EAAAA,KAAC,MAAA,CAAI,UAAU,+BACb,SAAA,CAAAvD,EAAAA,IAAC,OAAI,UAAU,2CAA2C,KAAK,OAAO,OAAO,eAAe,QAAQ,YAClG,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,6BAA6B,CAAA,CACpG,EACAuD,EAAAA,KAAC,OAAA,CAAK,UAAU,uDACb,SAAA,CAAA7F,EAAQ,gBAAgB,OAAO,kBAAgBA,EAAQ,gBAAgB,OAAS,EAAI,IAAM,GAAG,QAAA,CAAA,CAChG,CAAA,EACF,EAGAsC,EAAAA,IAACuI,GAAA,CACC,gBAAiB7K,EAAQ,gBACzB,OAAQ,CACN,YAAa,SACb,QAAS,OACT,mBAAoB,EACpB,cAAe,GACf,SAAU,GACV,QAAS,GAAA,EAEX,MAAAiE,EACA,sBAAAyE,EACA,UAAU,6FAAA,CAAA,CACZ,CAAA,CACF,CAAA,CAAA,CAEJ,CAAA,CAAA,CAAA,CAGN,EC7GauD,GAAkD,CAAC,CAC9D,YAAAC,EAAc,uBACd,SAAAC,EAAW,GACX,YAAAC,EAAc,CAAA,EACd,MAAAnI,EACA,cAAAoI,EACA,UAAA1K,CACF,IAAM,CACJ,KAAM,CAAC3B,EAASsM,CAAU,EAAI7M,EAAAA,SAAS,EAAE,EACnC,CAAC8M,EAAiBC,CAAkB,EAAI/M,EAAAA,SAAS,EAAK,EACtD,CAACgN,EAAqBC,CAAsB,EAAIjN,EAAAA,SAAmB,CAAA,CAAE,EACrEkN,EAAW7K,EAAAA,OAA4B,IAAI,EAE3C8K,EAAqB5G,GAA8C,CACvE,MAAMrK,EAAQqK,EAAE,OAAO,MAIvB,GAHAsG,EAAW3Q,CAAK,EAGZA,EAAM,KAAA,GAAUyQ,EAAY,OAAS,EAAG,CAC1C,MAAMS,EAAWT,EAAY,UAC3BU,EAAW,YAAA,EAAc,SAASnR,EAAM,aAAa,CAAA,EAEvD+Q,EAAuBG,CAAQ,EAC/BL,EAAmBK,EAAS,OAAS,CAAC,CAAA,MAEtCL,EAAmB,EAAK,EAItBG,EAAS,UACXA,EAAS,QAAQ,MAAM,OAAS,OAChCA,EAAS,QAAQ,MAAM,OAAS,GAAG,KAAK,IAAIA,EAAS,QAAQ,aAAc,GAAG,CAAC,KACjF,EAGII,EAAiB/G,GAA0C,CAC3DA,EAAE,MAAQ,SAAW,CAACA,EAAE,WAC1BA,EAAE,eAAA,EACFgH,EAAA,EACF,EAGIA,EAAa,IAAM,CACvB,MAAMC,EAAiBjN,EAAQ,KAAA,EAC3BiN,GAAkB,CAACd,GAAYE,IACjCA,EAAcY,CAAc,EAC5BX,EAAW,EAAE,EACbE,EAAmB,EAAK,EAGpBG,EAAS,UACXA,EAAS,QAAQ,MAAM,OAAS,QAEpC,EAGIO,EAAyBJ,GAAuB,CACpDR,EAAWQ,CAAU,EACrBN,EAAmB,EAAK,EACpBG,EAAS,SACXA,EAAS,QAAQ,MAAA,CACnB,EAGIhG,EAAmBhI,EACvB,oBACA,WACAgD,CAAA,EAGIwL,EAAexO,EACnB,6EACA,8DACA,iDACA,qFACA,qGACA,oCACA,CACE,gCAAiCwN,CAAA,CACnC,EAGIiB,EAAoBzO,EACxB,uEACA,mCACA,CACE,2CAA4CqB,EAAQ,KAAA,GAAU,CAACmM,EAC/D,oFAAqF,CAACnM,EAAQ,QAAUmM,CAAA,CAC1G,EAGIvF,EAAiB3C,GAAA,MAAAA,EAAO,YAAc,CAC1C,mBAAoBA,EAAM,WAAA,EACD,OAE3B,OACE4B,EAAAA,KAAC,MAAA,CACC,UAAWc,EACX,MAAOC,EACP,oBAAmB3C,GAAA,YAAAA,EAAO,KAGzB,SAAA,CAAAsI,GAAmBE,EAAoB,OAAS,GAC/CnK,EAAAA,IAAC,OAAI,UAAU,sKACZ,SAAAmK,EAAoB,MAAM,EAAG,CAAC,EAAE,IAAI,CAACK,EAAYhG,IAChDxE,EAAAA,IAAC,SAAA,CAEC,QAAS,IAAM4K,EAAsBJ,CAAU,EAC/C,UAAU,uKAET,SAAAA,CAAA,EAJIhG,CAAA,CAMR,EACH,EAIFjB,EAAAA,KAAC,MAAA,CAAI,UAAU,WACb,SAAA,CAAAvD,EAAAA,IAAC,WAAA,CACC,IAAKqK,EACL,MAAO3M,EACP,SAAU4M,EACV,UAAWG,EACX,YAAAb,EACA,SAAAC,EACA,KAAM,EACN,UAAWgB,EACX,MAAO,CAAE,UAAW,OAAQ,UAAW,OAAA,CAAQ,CAAA,EAIjD7K,EAAAA,IAAC,SAAA,CACC,QAAS0K,EACT,SAAU,CAAChN,EAAQ,KAAA,GAAUmM,EAC7B,UAAWiB,EACX,aAAW,eAEX,SAAA9K,EAAAA,IAAC,OAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,YACjE,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,mCAAmC,CAAA,CAC1G,CAAA,CAAA,CACF,EACF,EAGAuD,EAAAA,KAAC,MAAA,CAAI,UAAU,kFACb,SAAA,CAAAvD,EAAAA,IAAC,QAAK,SAAA,+CAAA,CAA6C,EACnDuD,OAAC,QAAK,UAAWlH,EACf,kCACA,CAAE,YAAaqB,EAAQ,OAAS,GAAA,CAAI,EAEnC,SAAA,CAAAA,EAAQ,OAAO,MAAA,CAAA,CAClB,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CAGN,ECzJaqN,GAA0D,CAAC,CACtE,SAAAC,EACA,OAAA/S,EACA,MAAA0J,EACA,UAAAsJ,EAAY,GACZ,cAAAlB,EACA,sBAAA3D,EACA,UAAA/G,CACF,IAAM,CACJ,MAAM6L,EAAiB1L,EAAAA,OAAuB,IAAI,EAC5C2L,EAAuB3L,EAAAA,OAAuB,IAAI,EAGxDE,EAAAA,UAAU,IAAM,CACVwL,EAAe,SACjBA,EAAe,QAAQ,eAAe,CAAE,SAAU,SAAU,CAC9D,EACC,CAACF,CAAQ,CAAC,EAEb,MAAM3G,EAAmBhI,EACvB,wBACA,kDACAgD,CAAA,EAGIiF,EAAiB3C,GAAA,MAAAA,EAAO,YAAc,CAC1C,mBAAoBA,EAAM,WAAA,EACD,OAGrByJ,EAAkBnT,EAAO,YAC3B+S,EAAS,MAAM,CAAC/S,EAAO,WAAW,EAClC+S,EAEJ,OACEzH,EAAAA,KAAC,MAAA,CACC,UAAWc,EACX,MAAOC,EACP,oBAAmB3C,GAAA,YAAAA,EAAO,KAG1B,SAAA,CAAA3B,EAAAA,IAAC,MAAA,CACC,IAAKmL,EACL,UAAU,8GAET,WAAgB,SAAW,EAC1B5H,EAAAA,KAAC,MAAA,CAAI,UAAU,+DACb,SAAA,CAAAvD,EAAAA,IAAC,MAAA,CAAI,UAAU,mJACb,SAAAA,EAAAA,IAAC,MAAA,CAAI,UAAU,2CAA2C,KAAK,OAAO,OAAO,eAAe,QAAQ,YAClG,SAAAA,EAAAA,IAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,4BAAA,CAA6B,CAAA,CACpG,CAAA,CACF,EACAA,EAAAA,IAAC,KAAA,CAAG,UAAU,8DAA8D,SAAA,uBAE5E,EACAA,EAAAA,IAAC,IAAA,CAAE,UAAU,oDAAoD,SAAA,6GAAA,CAEjE,CAAA,CAAA,CACF,EAEAuD,EAAAA,KAAAO,EAAAA,SAAA,CACG,SAAA,CAAAsH,EAAgB,IAAK1N,GACpBsC,EAAAA,IAACoJ,GAAA,CAEC,QAAA1L,EACA,MAAAiE,EACA,sBAAAyE,CAAA,EAHK1I,EAAQ,EAAA,CAKhB,EAGAuN,GAAahT,EAAO,wBAA0B,IAC7CsL,EAAAA,KAAC,MAAA,CAAI,UAAU,yBACb,SAAA,CAAAvD,EAAAA,IAAC,MAAA,CAAI,UAAU,oHACb,SAAAA,EAAAA,IAAC,MAAA,CAAI,UAAU,qBAAqB,KAAK,OAAO,OAAO,eAAe,QAAQ,YAC5E,SAAAA,EAAAA,IAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,4BAAA,CAA6B,CAAA,CACpG,CAAA,CACF,QACC,MAAA,CAAI,UAAU,8DACb,SAAAuD,EAAAA,KAAC,MAAA,CAAI,UAAU,iBACb,SAAA,CAAAvD,EAAAA,IAAC,MAAA,CAAI,UAAU,kEAAA,CAAmE,EAClFA,MAAC,OAAI,UAAU,mEAAmE,MAAO,CAAE,eAAgB,QAAU,EACrHA,MAAC,OAAI,UAAU,mEAAmE,MAAO,CAAE,eAAgB,OAAO,CAAG,CAAA,CAAA,CACvH,CAAA,CACF,CAAA,EACF,EAGFA,EAAAA,IAAC,MAAA,CAAI,IAAKkL,CAAA,CAAgB,CAAA,CAAA,CAC5B,CAAA,CAAA,EAKHjT,EAAO,mBAAqBA,EAAO,aAAeA,EAAO,YAAY,OAAS,GAAK+S,EAAS,SAAW,GACtGzH,OAAC,MAAA,CAAI,UAAU,YACb,SAAA,CAAAvD,EAAAA,IAAC,MAAA,CAAI,UAAU,gDAAgD,SAAA,qBAAkB,EACjFA,EAAAA,IAAC,MAAA,CAAI,UAAU,uBACZ,SAAA/H,EAAO,YAAY,MAAM,EAAG,CAAC,EAAE,IAAI,CAACuS,EAAYhG,IAC/CxE,EAAAA,IAAC,SAAA,CAEC,QAAS,IAAM+J,GAAA,YAAAA,EAAgBS,GAC/B,UAAU,8JAET,SAAAA,CAAA,EAJIhG,CAAA,CAMR,CAAA,CACH,CAAA,EACF,EAIDvM,EAAO,iBAAmB,IAAS8R,GAClC/J,EAAAA,IAAC,MAAA,CAAI,UAAU,qDACb,SAAAA,EAAAA,IAAC2J,GAAA,CACC,YAAa1R,EAAO,aAAe,+CACnC,SAAUgT,EACV,YAAahT,EAAO,YACpB,MAAA0J,EACA,cAAAoI,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CAAA,CAIR,EC9HasB,GAAwD,CAAC,CACpE,OAAApT,EACA,MAAA0J,EACA,MAAAkG,EAAQ,eACR,SAAAyD,EAAW,mCACX,OAAQC,EACR,SAAAC,EACA,cAAAzB,EACA,sBAAA3D,EACA,oBAAAqF,EACA,0BAAAC,EACA,eAAAC,EAAiB,GACjB,wBAAAC,EAA0B,GAC1B,4BAAAC,EACA,UAAAxM,CACF,IAAM,CACJ,KAAM,CAACyM,EAAgBC,CAAiB,EAAI5O,EAAAA,SAASlF,EAAO,UAAY,EAAK,EACvE,CAAC+S,EAAUgB,CAAW,EAAI7O,EAAAA,SAAwB,CAAA,CAAE,EACpD,CAAC8N,EAAWgB,CAAY,EAAI9O,EAAAA,SAAS,EAAK,EAC1C,CAAC+O,EAAeC,CAAgB,EAAIhP,EAAAA,SAAS,EAAK,EAElDiP,EAASb,IAAqB,OAAYA,EAAmBO,EAGnEpM,EAAAA,UAAU,IAAM,CACd,GAAIzH,EAAO,oBAAsBA,EAAO,gBAAkB+S,EAAS,SAAW,EAAG,CAC/E,MAAMqB,EAA8B,CAClC,GAAI,UACJ,KAAM,YACN,QAASpU,EAAO,eAChB,cAAe,IAAK,EAEtB+T,EAAY,CAACK,CAAc,CAAC,CAAA,CAC9B,EACC,CAACpU,EAAO,mBAAoBA,EAAO,eAAgB+S,EAAS,MAAM,CAAC,EAGtEtL,EAAAA,UAAU,IAAM,CACd,GAAI+L,GAAuBA,EAAoB,OAAS,GAAKG,EAAyB,CACpF,MAAMU,EAA2B,CAC/B,GAAI,QAAQ,KAAK,IAAA,CAAK,GACtB,KAAM,YACN,QAASZ,EACL,aAAaA,CAAyB,6CACtC,iDACJ,cAAe,KACf,gBAAiBD,CAAA,EAIfF,IAAqB,QACvBQ,EAAkB,EAAI,EAIxBC,EAAYO,GAEaA,EAAK,KAAKC,GAAOA,EAAI,GAAG,WAAW,OAAO,CAAC,EAEzDD,EAAK,OACVC,EAAI,GAAG,WAAW,OAAO,EAAIF,EAAcE,CAAA,EAGxC,CAAC,GAAGD,EAAMD,CAAW,CAC7B,CAAA,CACH,EACC,CAACb,EAAqBG,EAAyBF,EAA2BH,CAAgB,CAAC,EAE9F,MAAMkB,EAAe,IAAM,CACrBjB,EACFA,EAAA,EAEAO,EAAkB,CAACD,CAAc,EAEnCK,EAAiB,EAAI,CAAA,EAGjBO,EAAoB,MAAOC,GAA2B,CAC1D,GAAI,CAAC5C,EAAe,OAGpB,MAAM6C,EAA2B,CAC/B,GAAI,QAAQ,KAAK,IAAA,CAAK,GACtB,KAAM,OACN,QAASD,EACT,cAAe,IAAK,EAGtBX,EAAYO,GAAQ,CAAC,GAAGA,EAAMK,CAAW,CAAC,EAC1CX,EAAa,EAAI,EAEjB,GAAI,CAEF,MAAMlC,EAAc4C,CAAc,CAAA,OAE3BvP,EAAO,CACd,QAAQ,MAAM,yBAA0BA,CAAK,EAC7C,MAAMyP,EAA4B,CAChC,GAAI,SAAS,KAAK,IAAA,CAAK,GACvB,KAAM,YACN,QAAS,mDACT,cAAe,IAAK,EAEtBb,EAAYO,GAAQ,CAAC,GAAGA,EAAMM,CAAY,CAAC,CAAA,QAC7C,CACEZ,EAAa,EAAK,CAAA,CACpB,EAIIa,EAAoB,IAAM,CAC9B,OAAQ7U,EAAO,KAAA,CACb,IAAK,KAAM,MAAO,YAClB,IAAK,KAAM,MAAO,iBAClB,IAAK,KAAM,MAAO,sBAClB,IAAK,KAAM,MAAO,sBAClB,QAAS,MAAO,gBAAA,CAClB,EAcIoM,EAAmBhI,EACvB,uBACA,sDAZyB,IAAM,CAC/B,OAAQpE,EAAO,SAAA,CACb,IAAK,eAAgB,MAAO,mBAC5B,IAAK,cAAe,MAAO,kBAC3B,IAAK,YAAa,MAAO,gBACzB,IAAK,WAAY,MAAO,eACxB,QAAS,MAAO,kBAAA,CAClB,GAMA,EACAoH,CAAA,EAGI0N,EAAc1Q,EAClB,gHACAyQ,EAAA,EACA,CACE,yCAA0C,CAACV,EAC3C,wBAAyBA,CAAA,CAC3B,EAGI9H,EAAiB3C,GAAA,MAAAA,EAAO,YAAc,CAC1C,mBAAoBA,EAAM,WAAA,EACD,OAE3B,OACE4B,EAAAA,KAAC,MAAA,CACC,UAAWc,EACX,MAAOC,EACP,oBAAmB3C,GAAA,YAAAA,EAAO,KAG1B,SAAA,CAAA3B,MAAC,MAAA,CAAI,UAAW+M,EACb,SAAAX,GACC7I,EAAAA,KAAAO,WAAA,CAEE,SAAA,CAAAP,EAAAA,KAAC,MAAA,CAAI,UAAU,gGACb,SAAA,CAAAA,EAAAA,KAAC,MAAA,CAAI,UAAU,0BACb,SAAA,CAAAvD,EAAAA,IAAC,MAAA,CAAI,UAAU,+EACb,SAAAA,EAAAA,IAAC,MAAA,CAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,YACjE,SAAAA,EAAAA,IAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,4BAAA,CAA6B,CAAA,CACpG,CAAA,CACF,SACC,MAAA,CACC,SAAA,CAAAA,EAAAA,IAAC,KAAA,CAAG,UAAU,wBAAyB,SAAA6H,EAAM,EAC7C7H,EAAAA,IAAC,IAAA,CAAE,UAAU,wBAAyB,SAAAsL,CAAA,CAAS,CAAA,CAAA,CACjD,CAAA,EACF,EACA/H,EAAAA,KAAC,MAAA,CAAI,UAAU,0BAEZ,SAAA,CAAAkI,GAAuBA,EAAoB,OAAS,GAAKI,GACxD7L,EAAAA,IAAC,SAAA,CACC,QAAS,IAAM,CACb6L,EAAA,EACAG,EAAYO,GAAQA,EAAK,OAAOC,GAAO,CAACA,EAAI,GAAG,WAAW,OAAO,CAAC,CAAC,CAAA,EAErE,UAAU,wEACV,aAAW,0BACX,MAAM,0BAEN,SAAAxM,EAAAA,IAAC,OAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,YACjE,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,sLAAsL,CAAA,CAC7P,CAAA,CAAA,EAIJA,EAAAA,IAAC,SAAA,CACC,QAASyM,EACT,UAAU,wEACV,aAAW,aAEX,SAAAzM,EAAAA,IAAC,OAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,YACjE,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,uBAAuB,CAAA,CAC9F,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAGAA,EAAAA,IAAC+K,GAAA,CACC,SAAAC,EACA,OAAQ,CACN,GAAG/S,EACH,eAAA0T,CAAA,EAEF,MAAAhK,EACA,UAAAsJ,EACA,cAAeU,EAAiBe,EAAoB,IAAM,CAAA,EAC1D,sBAAAtG,EACA,UAAU,QAAA,CAAA,CACZ,CAAA,CACF,CAAA,CAEJ,EAGC,CAACgG,GACA7I,EAAAA,KAAC,SAAA,CACC,QAASkJ,EACT,UAAWpQ,EACT,iGACA,gFACA,2CAAA,EAEF,aAAW,YAEX,SAAA,CAAA2D,EAAAA,IAAC,OAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,YACjE,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,gKAAgK,CAAA,CACvO,EAGC,CAACkM,GACAlM,EAAAA,IAAC,MAAA,CAAI,UAAU,wEAAA,CAAyE,CAAA,CAAA,CAAA,EAM7FoM,GACCpM,EAAAA,IAAC,MAAA,CAAI,UAAU,4HAA4H,SAAA,mBAAA,CAE3I,CAAA,CAAA,CAAA,CAIR,EC5PagN,GAA0D,CAAC,CACtE,MAAAnF,EACA,MAAAlG,EACA,YAAA2D,EAAc,GACd,YAAA2H,EAAc,GACd,SAAAzB,EACA,SAAA0B,EACA,WAAAC,EAAa,GACb,UAAA9N,CACF,IAAM,CACJ,KAAM,CAAC+N,EAAaC,CAAc,EAAIlQ,EAAAA,SAAS,EAAE,EAC3C,CAACmQ,EAAiBC,CAAkB,EAAIpQ,EAAAA,SAAS,EAAK,EACtD,CAACqQ,EAAUC,CAAW,EAAItQ,EAAAA,SAAS,EAAK,EAG9CuC,EAAAA,UAAU,IAAM,CACd,MAAMgO,EAAc,IAAM,CACxBD,EAAY,OAAO,WAAa,GAAG,CAAA,EAGrC,OAAAC,EAAA,EACA,OAAO,iBAAiB,SAAUA,CAAW,EACtC,IAAM,OAAO,oBAAoB,SAAUA,CAAW,CAAA,EAC5D,EAAE,EAEL,MAAMC,EAAsBjK,GAA2C,CACrE,MAAMrK,EAAQqK,EAAE,OAAO,MACvB2J,EAAehU,CAAK,EACpB6T,GAAA,MAAAA,EAAW7T,EAAK,EAGZuU,EAAoB,IAAM,CAC9BP,EAAe,EAAE,EACjBH,GAAA,MAAAA,EAAW,GAAE,EAGTW,EAAgBxR,EACpB,wBACA,gGACAgD,CAAA,EAGIiF,EAAiB3C,GAAA,MAAAA,EAAO,YAAc,CAC1C,mBAAoBA,EAAM,WAAA,EACD,OAE3B,OACE4B,EAAAA,KAAC,MAAA,CACC,UAAWsK,EACX,MAAOvJ,EACP,oBAAmB3C,GAAA,YAAAA,EAAO,KAG1B,SAAA,CAAA4B,EAAAA,KAAC,MAAA,CAAI,UAAU,yCACb,SAAA,CAAAvD,EAAAA,IAAC,KAAA,CAAG,UAAU,kEACX,SAAA6H,EACH,EAEAtE,EAAAA,KAAC,MAAA,CAAI,UAAU,0BAEZ,SAAA,CAAAiK,GAAYhC,GACXxL,EAAAA,IAAC,SAAA,CACC,QAASwL,EACT,UAAU,uGACV,MAAM,gBAEN,SAAAxL,EAAAA,IAAC,MAAA,CACC,UAAU,2CACV,KAAK,OACL,OAAO,eACP,QAAQ,YAER,SAAAA,EAAAA,IAAC,QAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,sBAAA,CAAuB,CAAA,CAAA,CAC9F,CAAA,EAKHsF,GACCtF,EAAAA,IAAC,SAAA,CACC,QAASwL,EACT,UAAU,6GACV,MAAOyB,EAAc,iBAAmB,mBAExC,SAAAjN,EAAAA,IAAC,MAAA,CACC,UAAW3D,EACT,6EACA,CAAE,aAAc4Q,CAAA,CAAY,EAE9B,KAAK,OACL,OAAO,eACP,QAAQ,YAER,SAAAjN,EAAAA,IAAC,QAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,iBAAA,CAAkB,CAAA,CAAA,CACzF,CAAA,CACF,CAAA,CAEJ,CAAA,EACF,EAGCmN,GAAc,CAACF,GACd1J,EAAAA,KAAC,MAAA,CAAI,UAAU,WACb,SAAA,CAAAA,OAAC,OAAI,UAAWlH,EACd,yDACA,CACE,0CAA2CiR,CAAA,CAC7C,EAGA,SAAA,CAAAtN,EAAAA,IAAC,MAAA,CAAI,UAAU,sCACb,SAAAA,EAAAA,IAAC,MAAA,CAAI,UAAU,2CAA2C,KAAK,OAAO,OAAO,eAAe,QAAQ,YAClG,SAAAA,EAAAA,IAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,6CAAA,CAA8C,CAAA,CACrH,CAAA,CACF,EAGAA,EAAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAOoN,EACP,SAAUO,EACV,QAAS,IAAMJ,EAAmB,EAAI,EACtC,OAAQ,IAAMA,EAAmB,EAAK,EACtC,YAAY,4BACZ,UAAWlR,EACT,qHACA,kFACA,wGACA,6BAAA,CACF,CAAA,EAID+Q,GACCpN,EAAAA,IAAC,SAAA,CACC,QAAS4N,EACT,UAAU,kGACV,MAAM,eAEN,SAAA5N,EAAAA,IAAC,OAAI,UAAU,2CAA2C,KAAK,OAAO,OAAO,eAAe,QAAQ,YAClG,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,uBAAuB,CAAA,CAC9F,CAAA,CAAA,CACF,EAEJ,EAGCoN,GACCpN,EAAAA,IAAC,MAAA,CAAI,UAAU,gDAAgD,SAAA,8CAAA,CAE/D,CAAA,EAEJ,EAID,CAACiN,GACA1J,EAAAA,KAAC,MAAA,CAAI,UAAU,wEACb,SAAA,CAAAA,EAAAA,KAAC,MAAA,CAAI,UAAU,0BACb,SAAA,CAAAvD,EAAAA,IAAC,MAAA,CAAI,UAAU,mCAAA,CAAoC,EACnDA,EAAAA,IAAC,QAAK,SAAA,sBAAA,CAAoB,CAAA,EAC5B,EACAuD,EAAAA,KAAC,MAAA,CAAI,UAAU,0BACb,SAAA,CAAAvD,EAAAA,IAAC,OAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,YACjE,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,6BAA6B,CAAA,CACpG,EACAA,EAAAA,IAAC,QAAK,SAAA,YAAA,CAAU,CAAA,CAAA,CAClB,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CAIR,EC1Ka8N,GAA4D,CAAC,CACxE,gBAAAvM,EACA,YAAAwM,EACA,MAAApM,EACA,mBAAAoH,EACA,sBAAA3C,EACA,UAAA/G,CACF,IAAM,CACJ,KAAM,CAAC2O,EAAaC,CAAc,EAAI9Q,EAAAA,SAAS,EAAK,EAC9C,CAAC+Q,EAAWC,CAAY,EAAIhR,EAAAA,SAAmC,KAAK,EAEpE6L,EAAyBD,EAC3BxH,EAAgB,MAAM,EAAGwH,CAAkB,EAC3CxH,EAeE6M,GAbwB,IAAM,CAClC,OAAQF,EAAA,CACN,IAAK,MACH,OAAOlF,EACJ,OAAOxH,GAAOA,EAAI,oBAAsB,EAAG,EAC3C,MAAM,EAAG,CAAC,EACf,IAAK,SACH,OAAOwH,EAAuB,MAAM,EAAG,CAAC,EAC1C,QACE,OAAOA,CAAA,CACX,GAGyB,EAErBqF,EAAiBhS,EACrB,yBACA,uBACAgD,CAAA,EAGIiF,EAAiB3C,GAAA,MAAAA,EAAO,YAAc,CAC1C,mBAAoBA,EAAM,WAAA,EACD,OAErB2M,EAAwB,IAAM,CAClC,GAAIF,EAAmB,SAAW,EAChC,OACE7K,EAAAA,KAAC,MAAA,CAAI,UAAU,mEACb,SAAA,CAAAvD,EAAAA,IAAC,MAAA,CAAI,UAAU,6FACb,SAAAA,EAAAA,IAAC,MAAA,CAAI,UAAU,2CAA2C,KAAK,OAAO,OAAO,eAAe,QAAQ,YAClG,SAAAA,EAAAA,IAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,6CAAA,CAA8C,CAAA,CACrH,CAAA,CACF,EACAA,EAAAA,IAAC,KAAA,CAAG,UAAU,4DAA4D,SAAA,2BAE1E,EACAA,EAAAA,IAAC,IAAA,CAAE,UAAU,2CAA2C,SAAA,sCAAA,CAExD,CAAA,EACF,EAIJ,OAAQ+N,EAAA,CACN,IAAK,kBACH,OACE/N,MAAC,OAAI,UAAU,YACZ,WAAmB,IAAI,CAACjB,EAAgByF,IACvCxE,EAAAA,IAAC+F,EAAA,CAEC,eAAAhH,EACA,MAAA4C,EACA,QAAS,GACT,WAAY,GACZ,QAASyE,CAAA,EALJrH,EAAe,OAASyF,CAAA,CAOhC,EACH,EAGJ,IAAK,UACH,OACExE,EAAAA,IAAC,MAAA,CAAI,UAAU,YACZ,SAAAoO,EAAmB,IAAI,CAACrP,EAAgByF,IACvCjB,OAAC,MAAA,CAAwC,UAAU,oGACjD,SAAA,CAAAvD,EAAAA,IAAC,MAAA,CAAI,UAAU,gDAAA,CAAiD,EAChEuD,EAAAA,KAAC,MAAA,CAAI,UAAU,iBACb,SAAA,CAAAvD,EAAAA,IAAC,MAAA,CAAI,UAAU,gEACZ,SAAAjB,EAAe,MAClB,EACAwE,EAAAA,KAAC,MAAA,CAAI,UAAU,2CACZ,SAAA,CAAA,KAAK,MAAMxE,EAAe,mBAAqB,GAAG,EAAE,SAAA,CAAA,CACvD,CAAA,CAAA,CACF,CAAA,CAAA,EATQA,EAAe,OAASyF,CAUlC,CACD,CAAA,CACH,EAGJ,IAAK,YACH,aACG,MAAA,CAAI,UAAU,YACZ,SAAA4J,EAAmB,MAAM,EAAG,CAAC,EAAE,IAAI,CAACrP,EAAgByF,IACnDjB,EAAAA,KAAC,MAAA,CAAwC,UAAU,WACjD,SAAA,CAAAvD,EAAAA,IAAC+F,EAAA,CACC,eAAAhH,EACA,MAAA4C,EACA,QAAS,GACT,WAAY,GACZ,QAASyE,CAAA,CAAA,QAEV,SAAA,CAAO,UAAU,sGAChB,SAAApG,EAAAA,IAAC,OAAI,UAAU,0BAA0B,KAAK,eAAe,QAAQ,YACnE,SAAAA,EAAAA,IAAC,QAAK,EAAE,+FAA+F,EACzG,CAAA,CACF,CAAA,CAAA,EAZQjB,EAAe,OAASyF,CAalC,CACD,CAAA,CACH,EAGJ,IAAK,QACH,OACEjB,EAAAA,KAAC,MAAA,CAAI,UAAU,YAEZ,SAAA,CAAA6K,EAAmB,CAAC,GACnB7K,EAAAA,KAAC,MAAA,CACC,SAAA,CAAAvD,EAAAA,IAAC,KAAA,CAAG,UAAU,oFAAoF,SAAA,WAElG,EACAA,EAAAA,IAAC0B,GAAA,CACC,eAAgB0M,EAAmB,CAAC,EACpC,MAAAzM,EACA,eAAgB,GAChB,WAAY,GACZ,QAASyE,EACT,UAAU,SAAA,CAAA,CACZ,EACF,EAIDgI,EAAmB,MAAM,CAAC,EAAE,OAAS,UACnC,MAAA,CACC,SAAA,CAAApO,EAAAA,IAAC,KAAA,CAAG,UAAU,oFAAoF,SAAA,eAElG,EACAA,EAAAA,IAAC,MAAA,CAAI,UAAU,YACZ,SAAAoO,EAAmB,MAAM,EAAG,CAAC,EAAE,IAAI,CAACrP,EAAgByF,IACnDxE,EAAAA,IAAC+F,EAAA,CAEC,eAAAhH,EACA,MAAA4C,EACA,QAAS,GACT,WAAY,GACZ,QAASyE,CAAA,EALJrH,EAAe,OAASyF,CAAA,CAOhC,CAAA,CACH,CAAA,CAAA,CACF,CAAA,EAEJ,EAGJ,QACE,OACExE,MAAC,OAAI,UAAU,YACZ,WAAmB,IAAI,CAACjB,EAAgByF,IACvCxE,EAAAA,IAAC+F,EAAA,CAEC,eAAAhH,EACA,MAAA4C,EACA,QAAS,GACT,WAAY,GACZ,QAASyE,CAAA,EALJrH,EAAe,OAASyF,CAAA,CAOhC,EACH,CAAA,CAEN,EAGF,OACEjB,EAAAA,KAAC,MAAA,CACC,UAAW8K,EACX,MAAO/J,EACP,oBAAmB3C,GAAA,YAAAA,EAAO,KAG1B,SAAA,CAAA4B,EAAAA,KAAC,MAAA,CAAI,UAAU,iFACb,SAAA,CAAAA,EAAAA,KAAC,SAAA,CACC,QAAS,IAAM4K,EAAa,KAAK,EACjC,UAAW9R,EACT,yDACA6R,IAAc,MACV,mFACA,+EAAA,EAEP,SAAA,CAAA,QACO3M,EAAgB,OAAO,GAAA,CAAA,CAAA,EAE/BvB,EAAAA,IAAC,SAAA,CACC,QAAS,IAAMmO,EAAa,KAAK,EACjC,UAAW9R,EACT,yDACA6R,IAAc,MACV,mFACA,+EAAA,EAEP,SAAA,KAAA,CAAA,EAGDlO,EAAAA,IAAC,SAAA,CACC,QAAS,IAAMmO,EAAa,QAAQ,EACpC,UAAW9R,EACT,yDACA6R,IAAc,SACV,mFACA,+EAAA,EAEP,SAAA,QAAA,CAAA,CAED,EACF,EAGAlO,EAAAA,IAAC,MAAA,CAAI,UAAU,qCAAqC,MAAO,CACzD,wBAAyB,QACzB,mBAAoB,SAAA,EAEnB,aACH,QAGC,MAAA,CAAI,UAAU,kFACb,SAAAuD,EAAAA,KAAC,MAAA,CAAI,UAAU,4CACb,SAAA,CAAAA,EAAAA,KAAC,OAAA,CAAK,UAAU,mCACb,SAAA,CAAA6K,EAAmB,OAAO,kBAAgBA,EAAmB,SAAW,EAAI,IAAM,EAAA,EACrF,EACApO,EAAAA,IAAC,SAAA,CACC,QAAS,IAAMiO,EAAe,CAACD,CAAW,EAC1C,UAAU,kGACX,SAAA,SAAA,CAAA,CAED,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CAAA,CAGN,ECrPaO,GAA8C,CAAC,CAC1D,gBAAAhN,EACA,OAAAtJ,EACA,MAAA0J,EACA,MAAAkG,EAAQ,kBACR,OAAAuE,EAAS,GACT,SAAAZ,EACA,sBAAApF,EACA,SAAA8G,EAEA,UAAA7N,EACA,cAAAmP,EAAgB,EAClB,IAAM,CACJ,KAAM,CAACvB,EAAawB,CAAc,EAAItR,EAAAA,SAASlF,EAAO,kBAAoB,EAAK,EACzE,CAACmV,EAAaC,CAAc,EAAIlQ,EAAAA,SAAS,EAAE,EAC3C,CAACuR,CAAO,EAAIvR,EAAAA,SAAyB,EAAE,EACvC,CAACqQ,EAAUC,CAAW,EAAItQ,EAAAA,SAAS,EAAK,EAG9CuC,EAAAA,UAAU,IAAM,CACd,MAAMgO,EAAc,IAAM,CACxBD,EAAY,OAAO,WAAa,GAAG,CAAA,EAGrC,OAAAC,EAAA,EACA,OAAO,iBAAiB,SAAUA,CAAW,EACtC,IAAM,OAAO,oBAAoB,SAAUA,CAAW,CAAA,EAC5D,EAAE,EAGLhO,EAAAA,UAAU,IAAM,CACd,GAAI8N,GAAYpB,GAAU,CAACa,GAAe,CAACuB,EAAe,CACxD,MAAMG,EAAgB,OAAO,iBAAiB,SAAS,IAAI,EAAE,SAC7D,gBAAS,KAAK,MAAM,SAAW,SAC/B,SAAS,KAAK,MAAM,SAAW,QAC/B,SAAS,KAAK,MAAM,MAAQ,OAErB,IAAM,CACX,SAAS,KAAK,MAAM,SAAWA,EAC/B,SAAS,KAAK,MAAM,SAAW,GAC/B,SAAS,KAAK,MAAM,MAAQ,EAAA,CAC9B,CACF,EACC,CAACnB,EAAUpB,EAAQa,EAAauB,CAAa,CAAC,EAGjD9O,EAAAA,UAAU,IAAM,CACd,GAAIzH,EAAO,aAAeA,EAAO,gBAAiB,CAChD,MAAM2W,EAAW,YAAY,IAAM,CAEjC,QAAQ,IAAI,oCAAoC,CAAA,EAC/C3W,EAAO,eAAe,EAEzB,MAAO,IAAM,cAAc2W,CAAQ,CAAA,CACrC,EACC,CAAC3W,EAAO,YAAaA,EAAO,eAAe,CAAC,EAG/C,MAAM4W,EAA0BtR,EAAAA,QAAQ,IAAM,CAC5C,IAAIgN,EAAW,CAAC,GAAGhJ,CAAe,EAGlC,GAAI6L,EAAY,OAAQ,CACtB,MAAM0B,EAAQ1B,EAAY,YAAA,EAC1B7C,EAAWA,EAAS,OAAO/I,GAAA,OACzB,OAAAA,EAAI,MAAM,YAAA,EAAc,SAASsN,CAAK,GACtCtN,EAAI,OAAO,YAAA,EAAc,SAASsN,CAAK,KACvC3M,EAAAX,EAAI,WAAJ,YAAAW,EAAc,KAAKC,GAAWA,EAAQ,YAAA,EAAc,SAAS0M,CAAK,IAAC,CACrE,CAIF,OAAIJ,EAAQ,YAAcA,EAAQ,WAAW,OAAS,IACpDnE,EAAWA,EAAS,OAAO/I,GAAA,OACzB,OAAAW,EAAAX,EAAI,aAAJ,YAAAW,EAAgB,eAAY,OAAAA,EAAAuM,EAAQ,aAAR,YAAAvM,EAAoB,SAAS4M,KAAI,GAK7DL,EAAQ,YAORA,EAAQ,WACVnE,EAAWA,EAAS,OAAO/I,GAAOA,EAAI,YAAcA,EAAI,WAAa,CAAC,GAIpEkN,EAAQ,gBAAkB,SAC5BnE,EAAWA,EAAS,OAAO/I,GAAOA,EAAI,oBAAsBkN,EAAQ,aAAc,GAIpFnE,EAAS,KAAK,CAAChE,EAAGC,IAAMA,EAAE,mBAAqBD,EAAE,kBAAkB,EAG/DtO,EAAO,qBACTsS,EAAWA,EAAS,MAAM,EAAGtS,EAAO,kBAAkB,GAGjDsS,CAAA,EACN,CAAChJ,EAAiB6L,EAAasB,EAASzW,EAAO,kBAAkB,CAAC,EAE/DwU,EAAe,IAAM,CACrBxU,EAAO,cACTwW,EAAe,CAACxB,CAAW,EAC3BzB,GAAA,MAAAA,IACF,EAGIwD,EAAgBF,GAAkB,CACtCzB,EAAeyB,CAAK,EACpB5B,GAAA,MAAAA,EAAW4B,EAAK,EAsBZG,EAAiB5S,EACrB,iBACA,oIAfsB,IAAM,CAC5B,GAAI4Q,EAAa,MAAO,OAGxB,OAAQhV,EAAO,KAAA,CACb,IAAK,KAAM,MAAO,0CAClB,IAAK,KAAM,MAAO,0CAClB,IAAK,KAAM,MAAO,0CAClB,IAAK,KAAM,MAAO,+CAClB,QAAS,MAAO,yCAAA,CAClB,GAMA,EACA,CACE,WAAYA,EAAO,WAAa,OAChC,WAAYA,EAAO,WAAa,QAGhC,gCAAiC,CAACuW,EAClC,kBAAmBA,EACnB,SAAUvW,EAAO,WAAa,QAAU,CAACuW,EACzC,UAAWvW,EAAO,WAAa,SAAW,CAACuW,EAE3C,8BAA+BvW,EAAO,WAAa,QAAU,CAACmU,GAAU,CAACoC,EACzE,6BAA8BvW,EAAO,WAAa,SAAW,CAACmU,GAAU,CAACoC,EAEzE,UAAW,GACX,kBAAmB,CAACA,CAAA,EAEtBnP,CAAA,EAGIiF,EAAiB3C,GAAA,MAAAA,EAAO,YAAc,CAC1C,mBAAoBA,EAAM,WAAA,EACD,OAE3B,MAAI,CAACyK,GAAU,CAACnU,EAAO,YACd,KAIPsL,EAAAA,KAAAO,WAAA,CAEG,SAAA,CAAAsI,GAAU,CAACa,GACVjN,EAAAA,IAAC,MAAA,CACC,UAAW3D,EACT,4EACAmS,EAAgB,mBAAqB,eAAA,EAEvC,QAAS,IAAMhD,GAAA,YAAAA,IACf,MAAO,CAEL,SAAUgD,EAAgB,WAAa,QACvC,IAAK,EACL,KAAM,EACN,MAAO,EACP,OAAQ,EACR,YAAa,MAAA,CACf,CAAA,EAKJjL,EAAAA,KAAC,MAAA,CACC,UAAW0L,EACX,MAAO3K,EACP,oBAAmB3C,GAAA,YAAAA,EAAO,KAC1B,wBAAuB1J,EAAO,SAC9B,oBAAmBA,EAAO,KAC1B,mBAAkBuV,GAAYpB,GAAU,CAACa,EAAc,OAAS,QAChE,sBAAqBuB,EAAgB,OAAS,QAG7C,SAAA,CAAAvW,EAAO,aAAe,IACrB+H,EAAAA,IAACgN,GAAA,CACC,MAAAnF,EACA,MAAAlG,EACA,YAAa1J,EAAO,YACpB,YAAAgV,EACA,SAAUR,EACV,SAAUxU,EAAO,WAAa+W,EAAe,OAC7C,WAAY/W,EAAO,YAAc,CAACgV,CAAA,CAAA,EAKrC,CAACA,GACAjN,EAAAA,IAAC8N,GAAA,CACC,gBAAiBe,EACjB,YAAa5W,EAAO,YACpB,MAAA0J,EACA,mBAAoB1J,EAAO,mBAC3B,sBAAAmO,EACA,UAAU,gCAAA,CAAA,EAKb6G,GAAehV,EAAO,aACrBsL,EAAAA,KAAC,MAAA,CAAI,UAAU,uDACb,SAAA,CAAAvD,EAAAA,IAAC,SAAA,CACC,QAASyM,EACT,UAAU,6EACV,MAAM,iBAEN,SAAAzM,EAAAA,IAAC,OAAI,UAAU,2CAA2C,KAAK,OAAO,OAAO,eAAe,QAAQ,YAClG,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,eAAe,CAAA,CACtF,CAAA,CAAA,EAEFA,EAAAA,IAAC,MAAA,CAAI,UAAU,uFACZ,WAAwB,MAAA,CAC3B,CAAA,EACF,EAID,CAACiN,GACAjN,EAAAA,IAAC,MAAA,CAAI,UAAU,qDACb,SAAAA,EAAAA,IAAC,MAAA,CAAI,UAAU,uDAAuD,SAAA,mBAAA,CAEtE,CAAA,CACF,CAAA,CAAA,CAAA,CAEJ,EACF,CAEJ,ECjPakP,GAAgF,CAAC,CAC5F,gBAAA3N,EACA,QAAA4N,EACA,MAAAxN,EACA,MAAAkG,EAAQ,qBACR,SAAAuH,EAAW,eACX,KAAAvK,EAAO,KACP,SAAAwK,EAAW,GACX,UAAAC,EAAY,IACZ,sBAAAlJ,EACA,UAAAqC,EACA,UAAApJ,CACF,IAAM,CACJ,KAAM,CAACqJ,EAAWC,CAAY,EAAIxL,EAAAA,SAAS,EAAK,EAC1C,CAACyL,EAAaC,CAAc,EAAI1L,EAAAA,SAAS,EAAK,EAGpDuC,EAAAA,UAAU,IAAM,CACd,GAAI2P,GAAY9N,EAAgB,OAAS,EAAG,CAC1C,MAAMuH,EAAQ,WAAW,IAAM,CAC7BH,EAAa,EAAI,EACjBE,EAAe,EAAI,CAAA,EAClByG,CAAS,EAEZ,MAAO,IAAM,aAAaxG,CAAK,CAAA,CACjC,EACC,CAACuG,EAAU9N,EAAgB,OAAQ+N,CAAS,CAAC,EAEhD,MAAMpG,EAAgB,IAAM,CAC1BP,EAAa,EAAK,EAClBF,GAAA,MAAAA,GAAY,EAIR8G,EAAsB,IAAM,CAChC,OAAQ1K,EAAA,CACN,IAAK,KAAM,MAAO,gBAClB,IAAK,KAAM,MAAO,gBAClB,IAAK,KAAM,MAAO,qBAClB,QAAS,MAAO,eAAA,CAClB,EAII2K,EAAqB,IAAM,CAC/B,OAAQJ,EAAA,CACN,IAAK,eAAgB,MAAO,mBAC5B,IAAK,cAAe,MAAO,kBAC3B,IAAK,YAAa,MAAO,gBACzB,IAAK,WAAY,MAAO,eACxB,QAAS,MAAO,kBAAA,CAClB,EAGF,GAAI,CAAC1G,GAAanH,EAAgB,SAAW,EAC3C,OAAO,KAGT,MAAM8C,EAAmBhI,EACvB,oCACA,kDACAmT,EAAA,EACAD,EAAA,EACA,CACE,mCAAoC,CAAC3G,EACrC,sCAAuCA,CAAA,EAEzCvJ,CAAA,EAGIiF,EAAiB3C,GAAA,MAAAA,EAAO,YAAc,CAC1C,mBAAoBA,EAAM,WAAA,EACD,OAE3B,OACE3B,EAAAA,IAAC,MAAA,CACC,UAAWqE,EACX,MAAOC,EACP,oBAAmB3C,GAAA,YAAAA,EAAO,KAE1B,SAAA4B,EAAAA,KAAC,MAAA,CAAI,UAAU,gHAEb,SAAA,CAAAA,EAAAA,KAAC,MAAA,CAAI,UAAU,gGACb,SAAA,CAAAA,EAAAA,KAAC,MAAA,CAAI,UAAU,0BACb,SAAA,CAAAvD,EAAAA,IAAC,MAAA,CAAI,UAAU,+EACb,SAAAA,EAAAA,IAAC,MAAA,CAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,YACjE,SAAAA,EAAAA,IAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,4BAAA,CAA6B,CAAA,CACpG,CAAA,CACF,SACC,MAAA,CACC,SAAA,CAAAA,EAAAA,IAAC,KAAA,CAAG,UAAU,wBAAyB,SAAA6H,EAAM,EAC5CsH,GACC5L,EAAAA,KAAC,IAAA,CAAE,UAAU,0CAA0C,SAAA,CAAA,cACzC4L,EAAQ,GAAA,CAAA,CACtB,CAAA,CAAA,CAEJ,CAAA,EACF,EACAnP,EAAAA,IAAC,SAAA,CACC,QAASkJ,EACT,UAAU,wEACV,aAAW,0BAEX,SAAAlJ,EAAAA,IAAC,OAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,YACjE,eAAC,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,uBAAuB,CAAA,CAC9F,CAAA,CAAA,CACF,EACF,EAGAuD,EAAAA,KAAC,MAAA,CAAI,UAAU,sGAEb,SAAA,CAAAA,EAAAA,KAAC,MAAA,CAAI,UAAU,+BACb,SAAA,CAAAvD,EAAAA,IAAC,MAAA,CAAI,UAAU,iDAAA,CAAkD,EACjEuD,EAAAA,KAAC,OAAA,CAAK,UAAU,uDACb,SAAA,CAAAhC,EAAgB,OAAO,qBAAmBA,EAAgB,OAAS,EAAI,KAAO,GAAG,QAAA,CAAA,CACpF,CAAA,EACF,EAGAvB,EAAAA,IAACuI,GAAA,CACC,gBAAAhH,EACA,OAAQ,CACN,YAAa,SACb,QAAS,YACT,mBAAoB,EACpB,cAAe,GACf,SAAU,GACV,QAAS,GAAA,EAEX,MAAAI,EACA,sBAAAyE,CAAA,CAAA,CACF,EACF,QAGC,MAAA,CAAI,UAAU,wFACb,SAAA7C,EAAAA,KAAC,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAvD,EAAAA,IAAC,OAAA,CAAK,UAAU,2CAA2C,SAAA,oBAE3D,EACAA,EAAAA,IAAC,SAAA,CACC,QAASkJ,EACT,UAAU,0GACX,SAAA,SAAA,CAAA,CAED,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAGN,EClKauG,EAAoB,CAACC,EAAoC,KAAoB,CACxF,MAAMC,EAAyB,CAC7B,KAAM,QACN,aAAc,UACd,eAAgB,UAChB,YAAa,UACb,gBAAiB,UACjB,aAAc,UACd,YAAa,UACb,UAAW,UACX,mBAAoB,UACpB,WAAY,oEACZ,SAAU,CACR,MAAO,OACP,KAAM,OACN,MAAO,OACP,MAAO,MAAA,EAET,aAAc,MACd,QAAS,CACP,MAAO,MACP,OAAQ,MACR,MAAO,MAAA,EAET,QAAS,CACP,MAAO,+BACP,OAAQ,+BACR,MAAO,gCAAA,EAET,MAAO,CACL,WAAY,IACZ,aAAc,IACd,SAAU,IACV,UAAW,IACX,UAAW,GAAA,CACb,EAGF,MAAO,CACL,GAAGA,EACH,GAAGD,EACH,SAAU,CACR,GAAGC,EAAU,SACb,GAAGD,EAAY,QAAA,EAEjB,QAAS,CACP,GAAGC,EAAU,QACb,GAAGD,EAAY,OAAA,EAEjB,QAAS,CACP,GAAGC,EAAU,QACb,GAAGD,EAAY,OAAA,EAEjB,MAAO,CACL,GAAGC,EAAU,MACb,GAAGD,EAAY,KAAA,EAEjB,WAAY,CACV,GAAGC,EAAU,WACb,GAAGD,EAAY,UAAA,CACjB,CAEJ,EAKaE,GAAkB,CAACF,EAAoC,KAe3DD,EAAkB,CACvB,GAfyC,CACzC,KAAM,OACN,gBAAiB,UACjB,aAAc,UACd,YAAa,UACb,UAAW,UACX,mBAAoB,UACpB,QAAS,CACP,MAAO,+BACP,OAAQ,+BACR,MAAO,gCAAA,CACT,EAKA,GAAGC,CAAA,CACJ,EAMUG,GAAe,CAE1B,QAASJ,EAAkB,CACzB,aAAc,UACd,eAAgB,UAChB,aAAc,MACd,QAAS,CACP,MAAO,OACP,OAAQ,+BACR,MAAO,8BAAA,CACT,CACD,EAGD,QAASA,EAAkB,CACzB,aAAc,UACd,eAAgB,UAChB,YAAa,UACb,aAAc,OACd,UAAW,CACT,QAAS,oDACT,UAAW,oDACX,OAAQ,mDAAA,CACV,CACD,EAGD,UAAWA,EAAkB,CAC3B,aAAc,UACd,eAAgB,UAChB,gBAAiB,UACjB,aAAc,UACd,YAAa,UACb,aAAc,MACd,WAAY,wDAAA,CACb,EAGD,aAAcA,EAAkB,CAC9B,aAAc,UACd,eAAgB,UAChB,gBAAiB,UACjB,aAAc,UACd,YAAa,UACb,UAAW,UACX,mBAAoB,UACpB,aAAc,MACd,QAAS,CACP,MAAO,OACP,OAAQ,oBACR,MAAO,mBAAA,CACT,CACD,CACH,EAKaK,GAAc,IAAIC,IAAgD,CAC7E,MAAMJ,EAAYF,EAAA,EAClB,OAAOM,EAAO,OAAO,CAACC,EAAQrO,IACvBA,EACE8N,EAAkB,CACvB,GAAGO,EACH,GAAGrO,CAAA,CACJ,EAJkBqO,EAKlBL,CAAS,CACd,EAKaM,GAA0BC,GAA+C,iBACpF,MAAMC,EAAgB,iBAAiBD,CAAO,EAE9C,MAAO,CACL,eAAc/N,EAAAgO,EAAc,iBAAiB,wBAAwB,IAAvD,YAAAhO,EAA0D,SAAU,OAClF,iBAAgBW,EAAAqN,EAAc,iBAAiB,0BAA0B,IAAzD,YAAArN,EAA4D,SAAU,OACtF,kBAAiBC,EAAAoN,EAAc,iBAAiB,2BAA2B,IAA1D,YAAApN,EAA6D,SAAU,OACxF,YAAWC,EAAAmN,EAAc,iBAAiB,qBAAqB,IAApD,YAAAnN,EAAuD,SAAU,OAC5E,eAAcC,EAAAkN,EAAc,iBAAiB,wBAAwB,IAAvD,YAAAlN,EAA0D,SAAU,OAClF,aAAYC,EAAAiN,EAAc,iBAAiB,sBAAsB,IAArD,YAAAjN,EAAwD,SAAU,MAAA,CAElF,EC5EakN,GAAU,QAGVC,GAAiB,CAC5B,gBAAiB,GACjB,MAAO,GACP,MAAO,CACL,KAAM,QACN,YAAa,SAAA,CAEjB", "x_google_ignoreList": [0, 1, 2, 3]}