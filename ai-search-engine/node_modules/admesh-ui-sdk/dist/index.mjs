import we, { useState as R, use<PERSON>em<PERSON> as te, use<PERSON><PERSON>back as X, useRef as re, useEffect as W } from "react";
function je(t) {
  return t && t.__esModule && Object.prototype.hasOwnProperty.call(t, "default") ? t.default : t;
}
var se = { exports: {} }, Z = {};
/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var ue;
function Ne() {
  if (ue) return Z;
  ue = 1;
  var t = Symbol.for("react.transitional.element"), r = Symbol.for("react.fragment");
  function s(i, n, a) {
    var d = null;
    if (a !== void 0 && (d = "" + a), n.key !== void 0 && (d = "" + n.key), "key" in n) {
      a = {};
      for (var c in n)
        c !== "key" && (a[c] = n[c]);
    } else a = n;
    return n = a.ref, {
      $$typeof: t,
      type: i,
      key: d,
      ref: n !== void 0 ? n : null,
      props: a
    };
  }
  return Z.Fragment = r, Z.jsx = s, Z.jsxs = s, Z;
}
var ee = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var ge;
function _e() {
  return ge || (ge = 1, process.env.NODE_ENV !== "production" && function() {
    function t(l) {
      if (l == null) return null;
      if (typeof l == "function")
        return l.$$typeof === z ? null : l.displayName || l.name || null;
      if (typeof l == "string") return l;
      switch (l) {
        case y:
          return "Fragment";
        case v:
          return "Profiler";
        case g:
          return "StrictMode";
        case C:
          return "Suspense";
        case T:
          return "SuspenseList";
        case I:
          return "Activity";
      }
      if (typeof l == "object")
        switch (typeof l.tag == "number" && console.error(
          "Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."
        ), l.$$typeof) {
          case f:
            return "Portal";
          case j:
            return (l.displayName || "Context") + ".Provider";
          case b:
            return (l._context.displayName || "Context") + ".Consumer";
          case M:
            var N = l.render;
            return l = l.displayName, l || (l = N.displayName || N.name || "", l = l !== "" ? "ForwardRef(" + l + ")" : "ForwardRef"), l;
          case w:
            return N = l.displayName || null, N !== null ? N : t(l.type) || "Memo";
          case S:
            N = l._payload, l = l._init;
            try {
              return t(l(N));
            } catch {
            }
        }
      return null;
    }
    function r(l) {
      return "" + l;
    }
    function s(l) {
      try {
        r(l);
        var N = !1;
      } catch {
        N = !0;
      }
      if (N) {
        N = console;
        var A = N.error, P = typeof Symbol == "function" && Symbol.toStringTag && l[Symbol.toStringTag] || l.constructor.name || "Object";
        return A.call(
          N,
          "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",
          P
        ), r(l);
      }
    }
    function i(l) {
      if (l === y) return "<>";
      if (typeof l == "object" && l !== null && l.$$typeof === S)
        return "<...>";
      try {
        var N = t(l);
        return N ? "<" + N + ">" : "<...>";
      } catch {
        return "<...>";
      }
    }
    function n() {
      var l = E.A;
      return l === null ? null : l.getOwner();
    }
    function a() {
      return Error("react-stack-top-frame");
    }
    function d(l) {
      if (F.call(l, "key")) {
        var N = Object.getOwnPropertyDescriptor(l, "key").get;
        if (N && N.isReactWarning) return !1;
      }
      return l.key !== void 0;
    }
    function c(l, N) {
      function A() {
        L || (L = !0, console.error(
          "%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",
          N
        ));
      }
      A.isReactWarning = !0, Object.defineProperty(l, "key", {
        get: A,
        configurable: !0
      });
    }
    function u() {
      var l = t(this.type);
      return U[l] || (U[l] = !0, console.error(
        "Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."
      )), l = this.props.ref, l !== void 0 ? l : null;
    }
    function m(l, N, A, P, H, $, oe, ne) {
      return A = $.ref, l = {
        $$typeof: k,
        type: l,
        key: N,
        props: $,
        _owner: H
      }, (A !== void 0 ? A : null) !== null ? Object.defineProperty(l, "ref", {
        enumerable: !1,
        get: u
      }) : Object.defineProperty(l, "ref", { enumerable: !1, value: null }), l._store = {}, Object.defineProperty(l._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: 0
      }), Object.defineProperty(l, "_debugInfo", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: null
      }), Object.defineProperty(l, "_debugStack", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: oe
      }), Object.defineProperty(l, "_debugTask", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: ne
      }), Object.freeze && (Object.freeze(l.props), Object.freeze(l)), l;
    }
    function p(l, N, A, P, H, $, oe, ne) {
      var B = N.children;
      if (B !== void 0)
        if (P)
          if (V(B)) {
            for (P = 0; P < B.length; P++)
              h(B[P]);
            Object.freeze && Object.freeze(B);
          } else
            console.error(
              "React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead."
            );
        else h(B);
      if (F.call(N, "key")) {
        B = t(l);
        var Q = Object.keys(N).filter(function(ve) {
          return ve !== "key";
        });
        P = 0 < Q.length ? "{key: someKey, " + Q.join(": ..., ") + ": ...}" : "{key: someKey}", Y[B + P] || (Q = 0 < Q.length ? "{" + Q.join(": ..., ") + ": ...}" : "{}", console.error(
          `A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,
          P,
          B,
          Q,
          B
        ), Y[B + P] = !0);
      }
      if (B = null, A !== void 0 && (s(A), B = "" + A), d(N) && (s(N.key), B = "" + N.key), "key" in N) {
        A = {};
        for (var le in N)
          le !== "key" && (A[le] = N[le]);
      } else A = N;
      return B && c(
        A,
        typeof l == "function" ? l.displayName || l.name || "Unknown" : l
      ), m(
        l,
        B,
        $,
        H,
        n(),
        A,
        oe,
        ne
      );
    }
    function h(l) {
      typeof l == "object" && l !== null && l.$$typeof === k && l._store && (l._store.validated = 1);
    }
    var o = we, k = Symbol.for("react.transitional.element"), f = Symbol.for("react.portal"), y = Symbol.for("react.fragment"), g = Symbol.for("react.strict_mode"), v = Symbol.for("react.profiler"), b = Symbol.for("react.consumer"), j = Symbol.for("react.context"), M = Symbol.for("react.forward_ref"), C = Symbol.for("react.suspense"), T = Symbol.for("react.suspense_list"), w = Symbol.for("react.memo"), S = Symbol.for("react.lazy"), I = Symbol.for("react.activity"), z = Symbol.for("react.client.reference"), E = o.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, F = Object.prototype.hasOwnProperty, V = Array.isArray, x = console.createTask ? console.createTask : function() {
      return null;
    };
    o = {
      "react-stack-bottom-frame": function(l) {
        return l();
      }
    };
    var L, U = {}, O = o["react-stack-bottom-frame"].bind(
      o,
      a
    )(), G = x(i(a)), Y = {};
    ee.Fragment = y, ee.jsx = function(l, N, A, P, H) {
      var $ = 1e4 > E.recentlyCreatedOwnerStacks++;
      return p(
        l,
        N,
        A,
        !1,
        P,
        H,
        $ ? Error("react-stack-top-frame") : O,
        $ ? x(i(l)) : G
      );
    }, ee.jsxs = function(l, N, A, P, H) {
      var $ = 1e4 > E.recentlyCreatedOwnerStacks++;
      return p(
        l,
        N,
        A,
        !0,
        P,
        H,
        $ ? Error("react-stack-top-frame") : O,
        $ ? x(i(l)) : G
      );
    };
  }()), ee;
}
var fe;
function Ce() {
  return fe || (fe = 1, process.env.NODE_ENV === "production" ? se.exports = Ne() : se.exports = _e()), se.exports;
}
var e = Ce(), ie = { exports: {} };
/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/
var be;
function Me() {
  return be || (be = 1, function(t) {
    (function() {
      var r = {}.hasOwnProperty;
      function s() {
        for (var a = "", d = 0; d < arguments.length; d++) {
          var c = arguments[d];
          c && (a = n(a, i(c)));
        }
        return a;
      }
      function i(a) {
        if (typeof a == "string" || typeof a == "number")
          return a;
        if (typeof a != "object")
          return "";
        if (Array.isArray(a))
          return s.apply(null, a);
        if (a.toString !== Object.prototype.toString && !a.toString.toString().includes("[native code]"))
          return a.toString();
        var d = "";
        for (var c in a)
          r.call(a, c) && a[c] && (d = n(d, c));
        return d;
      }
      function n(a, d) {
        return d ? a ? a + " " + d : a + d : a;
      }
      t.exports ? (s.default = s, t.exports = s) : window.classNames = s;
    })();
  }(ie)), ie.exports;
}
var Se = Me();
const _ = /* @__PURE__ */ je(Se), Le = "https://api.useadmesh.com/track";
let ce = {
  apiBaseUrl: Le,
  enabled: !0,
  debug: !1,
  retryAttempts: 3,
  retryDelay: 1e3
};
const qe = (t) => {
  ce = { ...ce, ...t };
}, Te = (t) => {
  const [r, s] = R(!1), [i, n] = R(null), a = te(() => ({ ...ce, ...t }), [t]), d = X((h, o) => {
    a.debug && console.log(`[AdMesh Tracker] ${h}`, o);
  }, [a.debug]), c = X(async (h, o) => {
    if (!a.enabled) {
      d("Tracking disabled, skipping event", { eventType: h, data: o });
      return;
    }
    if (!o.adId || !o.admeshLink) {
      const g = "Missing required tracking data: adId and admeshLink are required";
      d(g, o), n(g);
      return;
    }
    s(!0), n(null);
    const k = {
      event_type: h,
      ad_id: o.adId,
      admesh_link: o.admeshLink,
      product_id: o.productId,
      user_id: o.userId,
      session_id: o.sessionId,
      revenue: o.revenue,
      conversion_type: o.conversionType,
      metadata: o.metadata,
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      user_agent: navigator.userAgent,
      referrer: document.referrer,
      page_url: window.location.href
    };
    d(`Sending ${h} event`, k);
    let f = null;
    for (let g = 1; g <= (a.retryAttempts || 3); g++)
      try {
        const v = await fetch(`${a.apiBaseUrl}/events`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify(k)
        });
        if (!v.ok)
          throw new Error(`HTTP ${v.status}: ${v.statusText}`);
        const b = await v.json();
        d(`${h} event tracked successfully`, b), s(!1);
        return;
      } catch (v) {
        f = v, d(`Attempt ${g} failed for ${h} event`, v), g < (a.retryAttempts || 3) && await new Promise(
          (b) => setTimeout(b, (a.retryDelay || 1e3) * g)
        );
      }
    const y = `Failed to track ${h} event after ${a.retryAttempts} attempts: ${f == null ? void 0 : f.message}`;
    d(y, f), n(y), s(!1);
  }, [a, d]), u = X(async (h) => c("click", h), [c]), m = X(async (h) => c("view", h), [c]), p = X(async (h) => (!h.revenue && !h.conversionType && d("Warning: Conversion tracking without revenue or conversion type", h), c("conversion", h)), [c, d]);
  return {
    trackClick: u,
    trackView: m,
    trackConversion: p,
    isTracking: r,
    error: i
  };
}, Ye = (t, r, s) => {
  try {
    const i = new URL(t);
    return i.searchParams.set("ad_id", r), i.searchParams.set("utm_source", "admesh"), i.searchParams.set("utm_medium", "recommendation"), s && Object.entries(s).forEach(([n, a]) => {
      i.searchParams.set(n, a);
    }), i.toString();
  } catch (i) {
    return console.warn("[AdMesh] Invalid URL provided to buildAdMeshLink:", t, i), t;
  }
}, He = (t, r) => ({
  adId: t.ad_id,
  admeshLink: t.admesh_link,
  productId: t.product_id,
  ...r
}), D = ({
  adId: t,
  admeshLink: r,
  productId: s,
  children: i,
  trackingData: n,
  className: a,
  style: d
}) => {
  const { trackClick: c, trackView: u } = Te(), m = re(null), p = re(!1);
  W(() => {
    if (!m.current || p.current) return;
    const o = new IntersectionObserver(
      (k) => {
        k.forEach((f) => {
          f.isIntersecting && !p.current && (p.current = !0, u({
            adId: t,
            admeshLink: r,
            productId: s,
            ...n
          }).catch(console.error));
        });
      },
      {
        threshold: 0.5,
        // Track when 50% of the element is visible
        rootMargin: "0px"
      }
    );
    return o.observe(m.current), () => {
      o.disconnect();
    };
  }, [t, r, s, n, u]);
  const h = X(async (o) => {
    try {
      await c({
        adId: t,
        admeshLink: r,
        productId: s,
        ...n
      });
    } catch (y) {
      console.error("Failed to track click:", y);
    }
    o.target.closest("a") || window.open(r, "_blank", "noopener,noreferrer");
  }, [t, r, s, n, c]);
  return /* @__PURE__ */ e.jsx(
    "div",
    {
      ref: m,
      className: a,
      onClick: h,
      style: {
        cursor: "pointer",
        ...d
      },
      children: i
    }
  );
};
D.displayName = "AdMeshLinkTracker";
const Re = `
/* AdMesh UI SDK - Complete Self-Contained Styles */

/* CSS Reset for AdMesh components */
.admesh-component, .admesh-component * {
  box-sizing: border-box;
}

/* CSS Variables */
.admesh-component {
  --admesh-primary: #6366f1;
  --admesh-primary-hover: #4f46e5;
  --admesh-secondary: #8b5cf6;
  --admesh-accent: #06b6d4;
  --admesh-background: #ffffff;
  --admesh-surface: #ffffff;
  --admesh-border: #e2e8f0;
  --admesh-text: #0f172a;
  --admesh-text-muted: #64748b;
  --admesh-text-light: #94a3b8;
  --admesh-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --admesh-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --admesh-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --admesh-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --admesh-radius: 0.75rem;
  --admesh-radius-sm: 0.375rem;
  --admesh-radius-lg: 1rem;
  --admesh-radius-xl: 1.5rem;
}

.admesh-component[data-admesh-theme="dark"] {
  --admesh-background: #111827;
  --admesh-surface: #1f2937;
  --admesh-border: #374151;
  --admesh-text: #f9fafb;
  --admesh-text-muted: #9ca3af;
  --admesh-text-light: #6b7280;
  --admesh-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.3), 0 1px 2px -1px rgb(0 0 0 / 0.3);
  --admesh-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
  --admesh-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
  --admesh-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3);
}

/* Layout Styles */
.admesh-layout {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  color: var(--admesh-text);
  background-color: var(--admesh-background);
  border-radius: var(--admesh-radius);
  padding: 1.5rem;
  box-shadow: var(--admesh-shadow);
  border: 1px solid var(--admesh-border);
}

.admesh-layout__header {
  margin-bottom: 1.5rem;
  text-align: center;
}

.admesh-layout__title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--admesh-text);
  margin-bottom: 0.5rem;
}

.admesh-layout__subtitle {
  font-size: 0.875rem;
  color: var(--admesh-text-muted);
}

.admesh-layout__cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.admesh-layout__more-indicator {
  text-align: center;
  padding: 1rem;
  color: var(--admesh-text-muted);
  font-size: 0.875rem;
}

.admesh-layout__empty {
  text-align: center;
  padding: 3rem 1rem;
}

.admesh-layout__empty-content h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--admesh-text-muted);
  margin-bottom: 0.5rem;
}

.admesh-layout__empty-content p {
  font-size: 0.875rem;
  color: var(--admesh-text-muted);
}

/* Product Card Styles */
.admesh-product-card {
  background-color: var(--admesh-surface);
  border: 1px solid var(--admesh-border);
  border-radius: var(--admesh-radius);
  padding: 1.5rem;
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.admesh-product-card:hover {
  box-shadow: var(--admesh-shadow-lg);
  transform: translateY(-2px);
  border-color: var(--admesh-primary);
}

.admesh-product-card__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.admesh-product-card__title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--admesh-text);
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.admesh-product-card__reason {
  font-size: 0.875rem;
  color: var(--admesh-text-muted);
  line-height: 1.5;
  margin-bottom: 1rem;
}

.admesh-product-card__match-score {
  margin-bottom: 1rem;
}

.admesh-product-card__match-score-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  color: var(--admesh-text-muted);
  margin-bottom: 0.25rem;
}

.admesh-product-card__match-score-bar {
  width: 100%;
  height: 0.375rem;
  background-color: var(--admesh-border);
  border-radius: var(--admesh-radius-sm);
  overflow: hidden;
}

.admesh-product-card__match-score-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--admesh-primary), #8b5cf6);
  border-radius: var(--admesh-radius-sm);
  transition: width 0.3s ease-in-out;
}

.admesh-product-card__badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.admesh-product-card__badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background-color: var(--admesh-primary);
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: var(--admesh-radius-sm);
}

.admesh-product-card__badge--secondary {
  background-color: var(--admesh-secondary);
}

.admesh-product-card__keywords {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-bottom: 1rem;
}

.admesh-product-card__keyword {
  padding: 0.125rem 0.375rem;
  background-color: var(--admesh-border);
  color: var(--admesh-text-muted);
  font-size: 0.75rem;
  border-radius: var(--admesh-radius-sm);
}

/* Dark mode specific enhancements */
.admesh-component[data-admesh-theme="dark"] .admesh-product-card__keyword {
  background-color: #4b5563;
  color: #d1d5db;
}

.admesh-component[data-admesh-theme="dark"] .admesh-product-card:hover {
  border-color: var(--admesh-primary);
  background-color: #374151;
}

.admesh-component[data-admesh-theme="dark"] .admesh-product-card__button:hover {
  background: linear-gradient(90deg, var(--admesh-primary-hover), var(--admesh-primary));
}

.admesh-product-card__footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 1.5rem;
}

/* Mobile-specific sidebar improvements */
@media (max-width: 640px) {
  .admesh-sidebar {
    /* Ensure proper mobile viewport handling */
    height: 100vh !important;
    height: 100dvh !important; /* Dynamic viewport height for mobile browsers */
    max-height: 100vh !important;
    max-height: 100dvh !important;
    width: 100vw !important;
    max-width: 90vw !important;
    overflow: hidden !important;
  }

  .admesh-sidebar.relative {
    height: 100% !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  /* Improve touch scrolling */
  .admesh-sidebar .overflow-y-auto {
    -webkit-overflow-scrolling: touch !important;
    overscroll-behavior: contain !important;
    scroll-behavior: smooth !important;
  }

  /* Prevent body scroll when sidebar is open */
  body:has(.admesh-sidebar[data-mobile-open="true"]) {
    overflow: hidden !important;
    position: fixed !important;
    width: 100% !important;
  }
}

/* Tablet improvements */
@media (min-width: 641px) and (max-width: 1024px) {
  .admesh-sidebar {
    max-width: 400px !important;
  }
}

/* Mobile responsiveness improvements for all components */
@media (max-width: 640px) {
  /* Product cards mobile optimization */
  .admesh-card {
    padding: 0.75rem !important;
    margin-bottom: 0.75rem !important;
  }

  /* Inline recommendations mobile optimization */
  .admesh-inline-recommendation {
    padding: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  /* Conversation summary mobile optimization */
  .admesh-conversation-summary {
    padding: 1rem !important;
  }

  /* Percentage text mobile improvements */
  .admesh-component .text-xs {
    font-size: 0.75rem !important;
    line-height: 1rem !important;
  }

  .admesh-component .text-sm {
    font-size: 0.875rem !important;
    line-height: 1.25rem !important;
  }

  /* Button mobile improvements */
  .admesh-component button {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.75rem !important;
    min-height: 2rem !important;
    touch-action: manipulation !important;
  }

  /* Badge mobile improvements */
  .admesh-component .rounded-full {
    padding: 0.25rem 0.5rem !important;
    font-size: 0.625rem !important;
    line-height: 1rem !important;
  }

  /* Progress bar mobile improvements */
  .admesh-component .bg-gray-200,
  .admesh-component .bg-slate-600 {
    height: 0.25rem !important;
  }

  /* Flex layout mobile improvements */
  .admesh-component .flex {
    flex-wrap: wrap !important;
  }

  .admesh-component .gap-2 {
    gap: 0.375rem !important;
  }

  .admesh-component .gap-3 {
    gap: 0.5rem !important;
  }
}

.admesh-product-card__button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(90deg, var(--admesh-primary), var(--admesh-primary-hover));
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  border: none;
  border-radius: var(--admesh-radius);
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
}

.admesh-product-card__button:hover {
  transform: translateY(-1px);
  box-shadow: var(--admesh-shadow-lg);
}

/* Utility Classes */
.admesh-text-xs { font-size: 0.75rem; }
.admesh-text-sm { font-size: 0.875rem; }
.admesh-text-base { font-size: 1rem; }
.admesh-text-lg { font-size: 1.125rem; }
.admesh-text-xl { font-size: 1.25rem; }

.admesh-font-medium { font-weight: 500; }
.admesh-font-semibold { font-weight: 600; }
.admesh-font-bold { font-weight: 700; }

.admesh-text-muted { color: var(--admesh-text-muted); }

/* Comparison Table Styles */
.admesh-compare-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--admesh-surface);
  border: 1px solid var(--admesh-border);
  border-radius: var(--admesh-radius);
  overflow: hidden;
}

.admesh-compare-table th,
.admesh-compare-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--admesh-border);
}

.admesh-compare-table th {
  background-color: var(--admesh-background);
  font-weight: 600;
  color: var(--admesh-text);
  font-size: 0.875rem;
}

.admesh-compare-table td {
  color: var(--admesh-text);
  font-size: 0.875rem;
}

.admesh-compare-table tr:hover {
  background-color: var(--admesh-border);
}

/* Dark mode table enhancements */
.admesh-component[data-admesh-theme="dark"] .admesh-compare-table th {
  background-color: #374151;
}

.admesh-component[data-admesh-theme="dark"] .admesh-compare-table tr:hover {
  background-color: #4b5563;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admesh-layout {
    padding: 1rem;
  }

  .admesh-layout__cards-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .admesh-product-card {
    padding: 1rem;
  }

  .admesh-compare-table {
    font-size: 0.75rem;
  }

  .admesh-compare-table th,
  .admesh-compare-table td {
    padding: 0.5rem;
  }
}

/* Essential Utility Classes for Self-Contained SDK - High Specificity */
.admesh-component .relative { position: relative !important; }
.admesh-component .absolute { position: absolute !important; }
.admesh-component .flex { display: flex !important; }
.admesh-component .inline-flex { display: inline-flex !important; }
.admesh-component .grid { display: grid !important; }
.admesh-component .hidden { display: none !important; }
.admesh-component .block { display: block !important; }
.admesh-component .inline-block { display: inline-block !important; }

/* Flexbox utilities */
.admesh-component .flex-col { flex-direction: column !important; }
.admesh-component .flex-row { flex-direction: row !important; }
.admesh-component .flex-wrap { flex-wrap: wrap !important; }
.admesh-component .items-center { align-items: center !important; }
.admesh-component .items-start { align-items: flex-start !important; }
.admesh-component .items-end { align-items: flex-end !important; }
.admesh-component .justify-center { justify-content: center !important; }
.admesh-component .justify-between { justify-content: space-between !important; }
.admesh-component .justify-end { justify-content: flex-end !important; }
.admesh-component .flex-1 { flex: 1 1 0% !important; }
.admesh-component .flex-shrink-0 { flex-shrink: 0 !important; }

/* Grid utilities */
.admesh-component .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.admesh-component .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.admesh-component .grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }

/* Spacing utilities */
.admesh-component .gap-1 { gap: 0.25rem; }
.admesh-component .gap-2 { gap: 0.5rem; }
.admesh-component .gap-3 { gap: 0.75rem; }
.admesh-component .gap-4 { gap: 1rem; }
.admesh-component .gap-6 { gap: 1.5rem; }
.admesh-component .gap-8 { gap: 2rem; }

/* Padding utilities */
.admesh-component .p-1 { padding: 0.25rem; }
.admesh-component .p-2 { padding: 0.5rem; }
.admesh-component .p-3 { padding: 0.75rem; }
.admesh-component .p-4 { padding: 1rem; }
.admesh-component .p-5 { padding: 1.25rem; }
.admesh-component .p-6 { padding: 1.5rem; }
.admesh-component .px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.admesh-component .px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.admesh-component .px-4 { padding-left: 1rem; padding-right: 1rem; }
.admesh-component .py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.admesh-component .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.admesh-component .py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.admesh-component .pt-2 { padding-top: 0.5rem; }
.admesh-component .pt-3 { padding-top: 0.75rem; }
.admesh-component .pb-2 { padding-bottom: 0.5rem; }
.admesh-component .pb-3 { padding-bottom: 0.75rem; }

/* Margin utilities */
.admesh-component .m-0 { margin: 0; }
.admesh-component .mb-1 { margin-bottom: 0.25rem; }
.admesh-component .mb-2 { margin-bottom: 0.5rem; }
.admesh-component .mb-3 { margin-bottom: 0.75rem; }
.admesh-component .mb-4 { margin-bottom: 1rem; }
.admesh-component .mb-6 { margin-bottom: 1.5rem; }
.admesh-component .mt-1 { margin-top: 0.25rem; }
.admesh-component .mt-2 { margin-top: 0.5rem; }
.admesh-component .mt-4 { margin-top: 1rem; }
.admesh-component .mt-6 { margin-top: 1.5rem; }
.admesh-component .mt-auto { margin-top: auto; }
.admesh-component .ml-1 { margin-left: 0.25rem; }
.admesh-component .mr-1 { margin-right: 0.25rem; }
.admesh-component .mr-2 { margin-right: 0.5rem; }

/* Width and height utilities */
.admesh-component .w-2 { width: 0.5rem; }
.admesh-component .w-3 { width: 0.75rem; }
.admesh-component .w-4 { width: 1rem; }
.admesh-component .w-5 { width: 1.25rem; }
.admesh-component .w-6 { width: 1.5rem; }
.admesh-component .w-full { width: 100%; }
.admesh-component .w-fit { width: fit-content; }
.admesh-component .h-2 { height: 0.5rem; }
.admesh-component .h-3 { height: 0.75rem; }
.admesh-component .h-4 { height: 1rem; }
.admesh-component .h-5 { height: 1.25rem; }
.admesh-component .h-6 { height: 1.5rem; }
.admesh-component .h-full { height: 100%; }
.admesh-component .min-w-0 { min-width: 0px; }

/* Border utilities */
.admesh-component .border { border-width: 1px; }
.admesh-component .border-t { border-top-width: 1px; }
.admesh-component .border-gray-100 { border-color: #f3f4f6; }
.admesh-component .border-gray-200 { border-color: #e5e7eb; }
.admesh-component .border-gray-300 { border-color: #d1d5db; }
.admesh-component .border-blue-200 { border-color: #bfdbfe; }
.admesh-component .border-green-200 { border-color: #bbf7d0; }

/* Border radius utilities */
.admesh-component .rounded { border-radius: 0.25rem !important; }
.admesh-component .rounded-md { border-radius: 0.375rem !important; }
.admesh-component .rounded-lg { border-radius: 0.5rem !important; }
.admesh-component .rounded-xl { border-radius: 0.75rem !important; }
.admesh-component .rounded-full { border-radius: 9999px !important; }

/* Background utilities */
.admesh-component .bg-white { background-color: #ffffff; }
.admesh-component .bg-gray-50 { background-color: #f9fafb; }
.admesh-component .bg-gray-100 { background-color: #f3f4f6; }
.admesh-component .bg-blue-50 { background-color: #eff6ff; }
.admesh-component .bg-blue-100 { background-color: #dbeafe; }
.admesh-component .bg-green-100 { background-color: #dcfce7; }
.admesh-component .bg-green-500 { background-color: #22c55e; }
.admesh-component .bg-blue-500 { background-color: #3b82f6; }

/* Gradients */
.admesh-component .bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)); }
.admesh-component .bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }
.admesh-component .from-white { --tw-gradient-from: #ffffff; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(255, 255, 255, 0)); }
.admesh-component .to-gray-50 { --tw-gradient-to: #f9fafb; }
.admesh-component .from-purple-500 { --tw-gradient-from: #a855f7; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(168, 85, 247, 0)); }
.admesh-component .to-pink-500 { --tw-gradient-to: #ec4899; }
.admesh-component .from-green-400 { --tw-gradient-from: #4ade80; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(74, 222, 128, 0)); }
.admesh-component .to-blue-500 { --tw-gradient-to: #3b82f6; }

/* Text utilities */
.admesh-component .text-xs { font-size: 0.75rem; line-height: 1rem; }
.admesh-component .text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.admesh-component .text-base { font-size: 1rem; line-height: 1.5rem; }
.admesh-component .text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.admesh-component .text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.admesh-component .font-medium { font-weight: 500; }
.admesh-component .font-semibold { font-weight: 600; }
.admesh-component .font-bold { font-weight: 700; }
.admesh-component .leading-relaxed { line-height: 1.625; }

/* Text colors */
.admesh-component .text-white { color: #ffffff; }
.admesh-component .text-gray-400 { color: #9ca3af; }
.admesh-component .text-gray-500 { color: #6b7280; }
.admesh-component .text-gray-600 { color: #4b5563; }
.admesh-component .text-gray-700 { color: #374151; }
.admesh-component .text-gray-800 { color: #1f2937; }
.admesh-component .text-blue-600 { color: #2563eb; }
.admesh-component .text-blue-700 { color: #1d4ed8; }
.admesh-component .text-green-700 { color: #15803d; }

/* Shadow utilities */
.admesh-component .shadow-sm { box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05); }
.admesh-component .shadow { box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1); }
.admesh-component .shadow-md { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); }
.admesh-component .shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); }
.admesh-component .shadow-xl { box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1); }

/* Transition utilities */
.admesh-component .transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.admesh-component .transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.admesh-component .duration-200 { transition-duration: 200ms; }
.admesh-component .duration-300 { transition-duration: 300ms; }

/* Transform utilities */
.admesh-component .hover\\:-translate-y-1:hover { transform: translateY(-0.25rem); }
.admesh-component .hover\\:scale-105:hover { transform: scale(1.05); }

/* Hover utilities */
.admesh-component .hover\\:shadow-xl:hover { box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1); }
.admesh-component .hover\\:bg-gray-100:hover { background-color: #f3f4f6; }
.admesh-component .hover\\:text-blue-800:hover { color: #1e40af; }

/* Cursor utilities */
.admesh-component .cursor-pointer { cursor: pointer; }

/* Overflow utilities */
.admesh-component .overflow-hidden { overflow: hidden; }
.admesh-component .truncate { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }

/* Text decoration */
.admesh-component .underline { text-decoration-line: underline; }

/* Whitespace */
.admesh-component .whitespace-nowrap { white-space: nowrap; }

/* Dark mode utilities */
@media (prefers-color-scheme: dark) {
  .admesh-component .dark\\:bg-slate-800 { background-color: #1e293b; }
  .admesh-component .dark\\:bg-slate-900 { background-color: #0f172a; }
  .admesh-component .dark\\:border-slate-700 { border-color: #334155; }
  .admesh-component .dark\\:text-white { color: #ffffff; }
  .admesh-component .dark\\:text-gray-200 { color: #e5e7eb; }
  .admesh-component .dark\\:text-gray-300 { color: #d1d5db; }
  .admesh-component .dark\\:text-gray-400 { color: #9ca3af; }
  .admesh-component .dark\\:text-blue-400 { color: #60a5fa; }
}

/* Responsive utilities */
@media (min-width: 640px) {
  .admesh-component .sm\\:p-5 { padding: 1.25rem; }
  .admesh-component .sm\\:text-base { font-size: 1rem; line-height: 1.5rem; }
  .admesh-component .sm\\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .admesh-component .sm\\:flex-row { flex-direction: row; }
  .admesh-component .sm\\:items-center { align-items: center; }
  .admesh-component .sm\\:justify-between { justify-content: space-between; }
}

@media (min-width: 768px) {
  .admesh-component .md\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
  .admesh-component .lg\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .admesh-component .lg\\:col-span-1 { grid-column: span 1 / span 1; }
}
`;
let de = !1;
const me = () => {
  W(() => {
    if (de) return;
    const t = document.createElement("style");
    return t.id = "admesh-ui-sdk-styles", t.textContent = Re, document.getElementById("admesh-ui-sdk-styles") || (document.head.appendChild(t), de = !0), () => {
      const r = document.getElementById("admesh-ui-sdk-styles");
      r && document.head.contains(r) && (document.head.removeChild(r), de = !1);
    };
  }, []);
}, K = (t, r = {}) => {
  const s = t.intent_match_score || 0, i = r.customLabels || {};
  return s >= 0.8 ? i.smartPick || "Smart Pick" : s >= 0.6 ? i.partnerMatch || "Partner Match" : s >= 0.3 ? i.promotedOption || "Promoted Option" : i.relatedOption || "Related Option";
}, ae = (t, r) => {
  const s = t.intent_match_score || 0;
  return s >= 0.8 ? "This recommendation is from a partner who compensates us when you engage. We've matched it to your needs based on your query." : s >= 0.6 ? "Top-rated partner solution matched to your specific requirements. Partner compensates us for qualified referrals." : s >= 0.3 ? "This partner solution may be relevant to your needs. The partner compensates us when you take qualifying actions." : "This solution is somewhat related to your query. While not a perfect match, it might still be helpful. This partner compensates us for qualified referrals.";
}, Ge = (t = !0, r = !1) => t ? r ? "These curated recommendations are from partners who compensate us for referrals." : "Personalized Partner Recommendations: All results are from vetted partners who compensate us for qualified matches. We've ranked them based on relevance to your specific needs." : "Expanded Results: While these don't perfectly match your query, they're related solutions from our partner network. All partners compensate us for referrals.", xe = (t, r = !1) => {
  const s = t.intent_match_score || 0;
  return r ? "Promoted Match" : s >= 0.8 ? "Smart Pick" : s >= 0.6 ? "Partner Match" : "Promoted Option";
}, he = () => "We've partnered with trusted providers to bring you relevant solutions. These partners compensate us for qualified referrals, which helps us keep our service free.", Ae = (t) => ({
  "Top Match": "Top Match",
  "Smart Pick": "Smart Pick",
  "Perfect Fit": "Perfect Fit",
  "Great Match": "Great Match",
  Recommended: "Recommended",
  "Good Fit": "Good Fit",
  Featured: "Featured",
  "Popular Choice": "Popular Choice",
  "Premium Pick": "Premium Pick",
  "Free Tier": "Free Tier",
  "AI Powered": "AI Powered",
  Popular: "Popular",
  New: "New",
  "Trial Available": "Trial Available",
  "Related Option": "Related Option",
  "Alternative Solution": "Alternative Solution",
  "Expanded Match": "Expanded Match"
})[t] || t, Ke = (t, r = "button") => {
  const s = t.recommendation_title || t.title;
  return r === "link" ? s : t.trial_days && t.trial_days > 0 ? `Try ${s}` : "Learn More";
}, Je = (t) => t.some((r) => (r.intent_match_score || 0) >= 0.8), Qe = (t = !1) => t ? "Powered by AdMesh" : "Recommendations powered by AdMesh", pe = ({
  recommendation: t,
  theme: r,
  showMatchScore: s = !1,
  showBadges: i = !0,
  variation: n = "default",
  className: a,
  style: d
}) => {
  var v, b, j, M, C, T, w, S, I, z, E, F, V;
  me();
  const [c, u] = R(!1), m = te(() => {
    var G;
    const x = [];
    K(t) === "Smart Pick" && x.push("Top Match"), t.trial_days && t.trial_days > 0 && x.push("Trial Available");
    const U = ["ai", "artificial intelligence", "machine learning", "ml", "automation"];
    return (((G = t.keywords) == null ? void 0 : G.some(
      (Y) => U.some((l) => Y.toLowerCase().includes(l))
    )) || t.title.toLowerCase().includes("ai")) && x.push("AI Powered"), t.badges && t.badges.length > 0 && t.badges.forEach((Y) => {
      ["Top Match", "Free Tier", "AI Powered", "Popular", "New", "Trial Available"].includes(Y) && !x.includes(Y) && x.push(Y);
    }), x;
  }, [t]), p = xe(t, !1), h = he(), o = Math.round(t.intent_match_score * 100), f = (() => {
    const x = t.content_variations;
    return n === "simple" ? {
      title: t.recommendation_title || t.title,
      description: t.recommendation_description || t.description || t.reason,
      ctaText: t.recommendation_title || t.title,
      isSimple: !0
    } : n === "question" && (x != null && x.question) ? {
      title: x.question.cta || t.recommendation_title || t.title,
      description: x.question.text,
      ctaText: x.question.cta || t.recommendation_title || t.title
    } : n === "statement" && (x != null && x.statement) ? {
      title: t.recommendation_title || t.title,
      description: x.statement.text,
      ctaText: x.statement.cta || t.recommendation_title || t.title
    } : {
      title: t.recommendation_title || t.title,
      description: t.recommendation_description || t.description || t.reason,
      ctaText: t.recommendation_title || t.title
    };
  })(), y = _(
    "admesh-component",
    "admesh-card",
    "relative p-4 sm:p-5 rounded-xl bg-gradient-to-br from-white to-gray-50 dark:from-slate-800 dark:to-slate-900 border border-gray-200/50 dark:border-slate-700/50 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1",
    a
  ), g = r ? {
    "--admesh-primary": r.primaryColor || r.accentColor || "#3b82f6",
    "--admesh-secondary": r.secondaryColor || "#10b981",
    "--admesh-accent": r.accentColor || "#3b82f6",
    "--admesh-background": r.backgroundColor,
    "--admesh-surface": r.surfaceColor,
    "--admesh-border": r.borderColor,
    "--admesh-text": r.textColor,
    "--admesh-text-secondary": r.textSecondaryColor,
    "--admesh-radius": r.borderRadius || "12px",
    "--admesh-shadow-sm": (v = r.shadows) == null ? void 0 : v.small,
    "--admesh-shadow-md": (b = r.shadows) == null ? void 0 : b.medium,
    "--admesh-shadow-lg": (j = r.shadows) == null ? void 0 : j.large,
    "--admesh-spacing-sm": (M = r.spacing) == null ? void 0 : M.small,
    "--admesh-spacing-md": (C = r.spacing) == null ? void 0 : C.medium,
    "--admesh-spacing-lg": (T = r.spacing) == null ? void 0 : T.large,
    "--admesh-font-size-sm": (w = r.fontSize) == null ? void 0 : w.small,
    "--admesh-font-size-base": (S = r.fontSize) == null ? void 0 : S.base,
    "--admesh-font-size-lg": (I = r.fontSize) == null ? void 0 : I.large,
    "--admesh-font-size-title": (z = r.fontSize) == null ? void 0 : z.title,
    fontFamily: r.fontFamily
  } : void 0;
  return n === "simple" ? /* @__PURE__ */ e.jsxs(
    "div",
    {
      className: _(
        "admesh-component admesh-simple-ad",
        "inline-block text-sm leading-relaxed",
        a
      ),
      style: {
        fontFamily: (r == null ? void 0 : r.fontFamily) || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        ...(E = r == null ? void 0 : r.components) == null ? void 0 : E.productCard,
        ...d
      },
      "data-admesh-theme": r == null ? void 0 : r.mode,
      children: [
        /* @__PURE__ */ e.jsx(
          "span",
          {
            style: {
              fontSize: "11px",
              fontWeight: "600",
              color: (r == null ? void 0 : r.accentColor) || "#2563eb",
              backgroundColor: (r == null ? void 0 : r.mode) === "dark" ? "#374151" : "#f3f4f6",
              padding: "2px 6px",
              borderRadius: "4px",
              marginRight: "8px"
            },
            title: ae(t, K(t)),
            children: K(t)
          }
        ),
        /* @__PURE__ */ e.jsxs(
          "span",
          {
            style: {
              color: (r == null ? void 0 : r.mode) === "dark" ? "#f3f4f6" : "#374151",
              marginRight: "4px"
            },
            children: [
              f.description,
              " "
            ]
          }
        ),
        /* @__PURE__ */ e.jsx(
          D,
          {
            adId: t.ad_id,
            admeshLink: t.admesh_link,
            productId: t.product_id,
            trackingData: {
              title: t.title,
              matchScore: t.intent_match_score,
              component: "simple_ad_cta"
            },
            children: /* @__PURE__ */ e.jsx(
              "span",
              {
                style: {
                  color: (r == null ? void 0 : r.accentColor) || "#2563eb",
                  textDecoration: "underline",
                  cursor: "pointer",
                  fontSize: "inherit",
                  fontFamily: "inherit"
                },
                children: f.ctaText
              }
            )
          }
        ),
        /* @__PURE__ */ e.jsxs(
          "span",
          {
            style: {
              fontSize: "10px",
              color: (r == null ? void 0 : r.mode) === "dark" ? "#9ca3af" : "#6b7280",
              marginLeft: "8px"
            },
            title: h,
            children: [
              "(",
              p,
              ")"
            ]
          }
        )
      ]
    }
  ) : n === "question" || n === "statement" ? /* @__PURE__ */ e.jsx(
    "div",
    {
      className: _(
        "admesh-component admesh-expandable-variation transition-all duration-300",
        c ? "p-4 sm:p-5 rounded-xl bg-gradient-to-br from-white to-gray-50 dark:from-slate-800 dark:to-slate-900 border border-gray-200/50 dark:border-slate-700/50 shadow-lg" : "p-4 rounded-lg bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 shadow-sm hover:shadow-md",
        a
      ),
      style: {
        fontFamily: (r == null ? void 0 : r.fontFamily) || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        ...(F = r == null ? void 0 : r.components) == null ? void 0 : F.productCard,
        ...d
      },
      "data-admesh-theme": r == null ? void 0 : r.mode,
      children: c ? (
        // Expanded full card layout (same as default variation)
        /* @__PURE__ */ e.jsxs(
          "div",
          {
            className: "h-full flex flex-col",
            style: g,
            "data-admesh-theme": r == null ? void 0 : r.mode,
            children: [
              /* @__PURE__ */ e.jsxs("div", { className: "flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3 mb-4", children: [
                /* @__PURE__ */ e.jsxs("div", { className: "flex flex-col sm:flex-row sm:items-center gap-2 flex-1 min-w-0", children: [
                  i && m.includes("Top Match") && /* @__PURE__ */ e.jsx(
                    "span",
                    {
                      className: "text-xs font-semibold text-white px-3 py-1 rounded-full w-fit shadow-md",
                      style: {
                        backgroundColor: (r == null ? void 0 : r.primaryColor) || (r == null ? void 0 : r.accentColor) || "#f59e0b",
                        borderRadius: (r == null ? void 0 : r.borderRadius) || "9999px"
                      },
                      title: ae(t),
                      children: Ae("Top Match")
                    }
                  ),
                  /* @__PURE__ */ e.jsxs("div", { className: "flex items-center gap-2 min-w-0", children: [
                    t.product_logo && /* @__PURE__ */ e.jsx(
                      "img",
                      {
                        src: t.product_logo.url,
                        alt: `${t.title} logo`,
                        className: "w-6 h-6 rounded flex-shrink-0",
                        onError: (x) => {
                          x.target.style.display = "none";
                        }
                      }
                    ),
                    /* @__PURE__ */ e.jsx("h4", { className: "font-semibold text-gray-800 dark:text-gray-200 text-sm sm:text-base truncate", children: f.title })
                  ] })
                ] }),
                /* @__PURE__ */ e.jsxs("div", { className: "flex gap-3 flex-shrink-0", children: [
                  /* @__PURE__ */ e.jsxs(
                    "button",
                    {
                      onClick: () => u(!1),
                      className: "flex items-center gap-2 px-3 py-2 text-xs font-medium text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-800/50 rounded-lg transition-all duration-200 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600",
                      title: "Show less details",
                      children: [
                        /* @__PURE__ */ e.jsx("span", { children: "Less Details" }),
                        /* @__PURE__ */ e.jsx("svg", { className: "h-4 w-4", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M20 12H4" }) })
                      ]
                    }
                  ),
                  /* @__PURE__ */ e.jsx(
                    D,
                    {
                      adId: t.ad_id,
                      admeshLink: t.admesh_link,
                      productId: t.product_id,
                      trackingData: {
                        title: t.title,
                        matchScore: t.intent_match_score,
                        component: "product_card_cta"
                      },
                      children: /* @__PURE__ */ e.jsxs("button", { className: "text-xs sm:text-sm px-3 py-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600 flex items-center transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg", children: [
                        n === "question" ? "Try" : "Visit",
                        " ",
                        f.ctaText,
                        /* @__PURE__ */ e.jsx("svg", { className: "ml-1 h-3 w-3", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" }) })
                      ] })
                    }
                  )
                ] })
              ] }),
              /* @__PURE__ */ e.jsx("div", { className: "mb-6", children: /* @__PURE__ */ e.jsx("p", { className: "text-sm text-gray-600 dark:text-gray-300 leading-relaxed", children: f.description }) }),
              /* @__PURE__ */ e.jsx("div", { className: "mb-6", children: /* @__PURE__ */ e.jsx(
                "p",
                {
                  className: "text-xs text-gray-500 dark:text-gray-400 leading-relaxed",
                  title: h,
                  children: p
                }
              ) }),
              s && typeof t.intent_match_score == "number" && /* @__PURE__ */ e.jsxs("div", { className: "mb-4", children: [
                /* @__PURE__ */ e.jsxs("div", { className: "flex items-center justify-between text-xs sm:text-sm text-gray-600 dark:text-gray-300 mb-2", children: [
                  /* @__PURE__ */ e.jsx("span", { className: "font-medium", children: "Match Score" }),
                  /* @__PURE__ */ e.jsxs("span", { className: "font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-blue-500 whitespace-nowrap", children: [
                    o,
                    "% match"
                  ] })
                ] }),
                /* @__PURE__ */ e.jsx("div", { className: "w-full bg-gray-200 dark:bg-slate-600 rounded-full h-2 overflow-hidden", children: /* @__PURE__ */ e.jsx(
                  "div",
                  {
                    className: "bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full transition-all duration-500 ease-out",
                    style: { width: `${o}%` }
                  }
                ) })
              ] }),
              /* @__PURE__ */ e.jsxs("div", { className: "flex flex-wrap gap-2 text-xs mb-3", children: [
                t.pricing && /* @__PURE__ */ e.jsxs("span", { className: "flex items-center px-2 py-1 rounded-full bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-700", children: [
                  /* @__PURE__ */ e.jsx("svg", { className: "h-3 w-3 mr-1", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" }) }),
                  t.pricing
                ] }),
                t.trial_days && t.trial_days > 0 && /* @__PURE__ */ e.jsxs("span", { className: "flex items-center px-2 py-1 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900/30 dark:to-cyan-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700", children: [
                  /* @__PURE__ */ e.jsx("svg", { className: "h-3 w-3 mr-1", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6 0h6" }) }),
                  t.trial_days,
                  "-day trial"
                ] })
              ] }),
              t.features && t.features.length > 0 && /* @__PURE__ */ e.jsxs("div", { className: "mb-3", children: [
                /* @__PURE__ */ e.jsx("div", { className: "text-xs text-gray-600 dark:text-gray-300 mb-2 font-medium", children: "✨ Key Features" }),
                /* @__PURE__ */ e.jsxs("div", { className: "flex flex-wrap gap-1.5", children: [
                  t.features.slice(0, 4).map((x, L) => /* @__PURE__ */ e.jsxs(
                    "span",
                    {
                      className: "text-xs px-2 py-1 rounded-full bg-gradient-to-r from-indigo-100 to-purple-100 dark:from-indigo-900/30 dark:to-purple-900/30 text-indigo-700 dark:text-indigo-300 border border-indigo-200 dark:border-indigo-700",
                      children: [
                        /* @__PURE__ */ e.jsx("svg", { className: "h-3 w-3 mr-0.5 inline text-indigo-500", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" }) }),
                        x
                      ]
                    },
                    L
                  )),
                  t.features.length > 4 && /* @__PURE__ */ e.jsxs("span", { className: "text-xs text-gray-500 dark:text-gray-400 px-2 py-1", children: [
                    "+",
                    t.features.length - 4,
                    " more"
                  ] })
                ] })
              ] }),
              t.integrations && t.integrations.length > 0 && /* @__PURE__ */ e.jsxs("div", { className: "mb-3", children: [
                /* @__PURE__ */ e.jsx("div", { className: "text-xs text-gray-600 dark:text-gray-300 mb-2 font-medium", children: "🔗 Integrations" }),
                /* @__PURE__ */ e.jsxs("div", { className: "flex flex-wrap gap-1.5", children: [
                  t.integrations.slice(0, 3).map((x, L) => /* @__PURE__ */ e.jsxs(
                    "span",
                    {
                      className: "text-xs px-2 py-1 rounded-full bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30 text-orange-700 dark:text-orange-300 border border-orange-200 dark:border-orange-700",
                      children: [
                        /* @__PURE__ */ e.jsx("svg", { className: "h-3 w-3 mr-0.5 inline text-orange-500", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" }) }),
                        x
                      ]
                    },
                    L
                  )),
                  t.integrations.length > 3 && /* @__PURE__ */ e.jsxs("span", { className: "text-xs text-gray-500 dark:text-gray-400 px-2 py-1", children: [
                    "+",
                    t.integrations.length - 3,
                    " more"
                  ] })
                ] })
              ] }),
              /* @__PURE__ */ e.jsx("div", { className: "flex justify-end mt-auto pt-2", children: /* @__PURE__ */ e.jsx("span", { className: "text-xs text-gray-400 dark:text-gray-500", children: "Powered by AdMesh" }) })
            ]
          }
        )
      ) : (
        // Simple inline layout with top label
        /* @__PURE__ */ e.jsxs(e.Fragment, { children: [
          /* @__PURE__ */ e.jsx("div", { className: "mb-2", children: /* @__PURE__ */ e.jsx(
            "span",
            {
              style: {
                fontSize: "11px",
                fontWeight: "600",
                color: (r == null ? void 0 : r.accentColor) || "#2563eb",
                backgroundColor: (r == null ? void 0 : r.mode) === "dark" ? "#374151" : "#f3f4f6",
                padding: "2px 6px",
                borderRadius: "4px"
              },
              title: ae(t, K(t)),
              children: K(t)
            }
          ) }),
          /* @__PURE__ */ e.jsxs("div", { className: "flex items-center justify-between gap-3", children: [
            /* @__PURE__ */ e.jsx("div", { className: "flex-1 min-w-0", children: /* @__PURE__ */ e.jsxs("p", { className: "text-sm text-gray-700 dark:text-gray-300 leading-relaxed", children: [
              f.description,
              " ",
              /* @__PURE__ */ e.jsx(
                D,
                {
                  adId: t.ad_id,
                  admeshLink: t.admesh_link,
                  productId: t.product_id,
                  trackingData: {
                    title: t.title,
                    matchScore: t.intent_match_score,
                    component: "simple_variation_cta"
                  },
                  children: /* @__PURE__ */ e.jsx(
                    "span",
                    {
                      className: "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline cursor-pointer font-medium transition-colors",
                      children: f.ctaText
                    }
                  )
                }
              )
            ] }) }),
            /* @__PURE__ */ e.jsx("div", { className: "flex items-center gap-3 flex-shrink-0", children: /* @__PURE__ */ e.jsxs(
              "button",
              {
                onClick: () => u(!0),
                className: "flex items-center gap-2 px-3 py-2 text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-all duration-200 border border-blue-200 dark:border-blue-700 hover:border-blue-300 dark:hover:border-blue-600",
                title: "View more details",
                children: [
                  /* @__PURE__ */ e.jsx("span", { children: "More Details" }),
                  /* @__PURE__ */ e.jsx("svg", { className: "h-4 w-4", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" }) })
                ]
              }
            ) })
          ] })
        ] })
      )
    }
  ) : /* @__PURE__ */ e.jsx(
    "div",
    {
      className: y,
      style: {
        fontFamily: (r == null ? void 0 : r.fontFamily) || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        ...(V = r == null ? void 0 : r.components) == null ? void 0 : V.productCard,
        ...d
      },
      "data-admesh-theme": r == null ? void 0 : r.mode,
      children: /* @__PURE__ */ e.jsxs(
        "div",
        {
          className: "h-full flex flex-col",
          style: g,
          children: [
            /* @__PURE__ */ e.jsx("div", { className: "mb-3", children: /* @__PURE__ */ e.jsx(
              "span",
              {
                style: {
                  fontSize: "11px",
                  fontWeight: "600",
                  color: (r == null ? void 0 : r.accentColor) || "#2563eb",
                  backgroundColor: (r == null ? void 0 : r.mode) === "dark" ? "#374151" : "#f3f4f6",
                  padding: "2px 6px",
                  borderRadius: "4px"
                },
                title: ae(t, K(t)),
                children: K(t)
              }
            ) }),
            /* @__PURE__ */ e.jsxs("div", { className: "flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3 mb-4", children: [
              /* @__PURE__ */ e.jsxs("div", { className: "flex items-center gap-2 flex-1 min-w-0", children: [
                t.product_logo && /* @__PURE__ */ e.jsx(
                  "img",
                  {
                    src: t.product_logo.url,
                    alt: `${t.title} logo`,
                    className: "w-6 h-6 rounded flex-shrink-0",
                    onError: (x) => {
                      x.target.style.display = "none";
                    }
                  }
                ),
                /* @__PURE__ */ e.jsx("h4", { className: "font-semibold text-gray-800 dark:text-gray-200 text-sm sm:text-base truncate", children: f.title })
              ] }),
              /* @__PURE__ */ e.jsx("div", { className: "flex gap-2 flex-shrink-0", children: /* @__PURE__ */ e.jsx(
                D,
                {
                  adId: t.ad_id,
                  admeshLink: t.admesh_link,
                  productId: t.product_id,
                  trackingData: {
                    title: t.title,
                    matchScore: t.intent_match_score,
                    component: "product_card_cta"
                  },
                  children: /* @__PURE__ */ e.jsxs("button", { className: "text-xs sm:text-sm px-3 py-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600 flex items-center transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg", children: [
                    "Visit ",
                    f.ctaText,
                    /* @__PURE__ */ e.jsx("svg", { className: "ml-1 h-3 w-3", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" }) })
                  ] })
                }
              ) })
            ] }),
            /* @__PURE__ */ e.jsx("div", { className: "mb-6", children: /* @__PURE__ */ e.jsx("p", { className: "text-sm text-gray-600 dark:text-gray-300 leading-relaxed", children: f.description }) }),
            s && typeof t.intent_match_score == "number" && /* @__PURE__ */ e.jsxs("div", { className: "mb-6", children: [
              /* @__PURE__ */ e.jsxs("div", { className: "flex items-center justify-between text-xs sm:text-sm text-gray-600 dark:text-gray-300 mb-2", children: [
                /* @__PURE__ */ e.jsx("span", { className: "font-medium", children: "Match Score" }),
                /* @__PURE__ */ e.jsxs("span", { className: "font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-blue-500 whitespace-nowrap", children: [
                  o,
                  "% match"
                ] })
              ] }),
              /* @__PURE__ */ e.jsx("div", { className: "w-full bg-gray-200 dark:bg-slate-600 rounded-full h-2 overflow-hidden", children: /* @__PURE__ */ e.jsx(
                "div",
                {
                  className: "bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full transition-all duration-500 ease-out",
                  style: { width: `${o}%` }
                }
              ) })
            ] }),
            /* @__PURE__ */ e.jsxs("div", { className: "flex flex-wrap gap-2 text-xs mb-3", children: [
              t.pricing && /* @__PURE__ */ e.jsxs("span", { className: "flex items-center px-2 py-1 rounded-full bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-700", children: [
                /* @__PURE__ */ e.jsx("svg", { className: "h-3 w-3 mr-1", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" }) }),
                t.pricing
              ] }),
              t.trial_days && t.trial_days > 0 && /* @__PURE__ */ e.jsxs("span", { className: "flex items-center px-2 py-1 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900/30 dark:to-cyan-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700", children: [
                /* @__PURE__ */ e.jsx("svg", { className: "h-3 w-3 mr-1", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6 0h6" }) }),
                t.trial_days,
                "-day trial"
              ] })
            ] }),
            t.features && t.features.length > 0 && /* @__PURE__ */ e.jsxs("div", { className: "mb-3", children: [
              /* @__PURE__ */ e.jsx("div", { className: "text-xs text-gray-600 dark:text-gray-300 mb-2 font-medium", children: "✨ Key Features" }),
              /* @__PURE__ */ e.jsxs("div", { className: "flex flex-wrap gap-1.5", children: [
                t.features.slice(0, 4).map((x, L) => /* @__PURE__ */ e.jsxs(
                  "span",
                  {
                    className: "text-xs px-2 py-1 rounded-full bg-gradient-to-r from-indigo-100 to-purple-100 dark:from-indigo-900/30 dark:to-purple-900/30 text-indigo-700 dark:text-indigo-300 border border-indigo-200 dark:border-indigo-700",
                    children: [
                      /* @__PURE__ */ e.jsx("svg", { className: "h-3 w-3 mr-0.5 inline text-indigo-500", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" }) }),
                      x
                    ]
                  },
                  L
                )),
                t.features.length > 4 && /* @__PURE__ */ e.jsxs("span", { className: "text-xs text-gray-500 dark:text-gray-400 px-2 py-1", children: [
                  "+",
                  t.features.length - 4,
                  " more"
                ] })
              ] })
            ] }),
            t.integrations && t.integrations.length > 0 && /* @__PURE__ */ e.jsxs("div", { className: "mb-3", children: [
              /* @__PURE__ */ e.jsx("div", { className: "text-xs text-gray-600 dark:text-gray-300 mb-2 font-medium", children: "🔗 Integrations" }),
              /* @__PURE__ */ e.jsxs("div", { className: "flex flex-wrap gap-1.5", children: [
                t.integrations.slice(0, 3).map((x, L) => /* @__PURE__ */ e.jsxs(
                  "span",
                  {
                    className: "text-xs px-2 py-1 rounded-full bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30 text-orange-700 dark:text-orange-300 border border-orange-200 dark:border-orange-700",
                    children: [
                      /* @__PURE__ */ e.jsx("svg", { className: "h-3 w-3 mr-0.5 inline text-orange-500", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" }) }),
                      x
                    ]
                  },
                  L
                )),
                t.integrations.length > 3 && /* @__PURE__ */ e.jsxs("span", { className: "text-xs text-gray-500 dark:text-gray-400 px-2 py-1", children: [
                  "+",
                  t.integrations.length - 3,
                  " more"
                ] })
              ] })
            ] }),
            /* @__PURE__ */ e.jsx("div", { className: "mt-auto pt-3 border-t border-gray-100 dark:border-slate-700", children: /* @__PURE__ */ e.jsxs("div", { className: "flex items-center justify-between text-xs text-gray-500 dark:text-gray-400", children: [
              /* @__PURE__ */ e.jsx("span", { title: h, children: p }),
              /* @__PURE__ */ e.jsx("span", { className: "text-gray-400 dark:text-gray-500", children: "Powered by AdMesh" })
            ] }) })
          ]
        }
      )
    }
  );
};
pe.displayName = "AdMeshProductCard";
const Ie = ({
  recommendations: t,
  theme: r,
  maxProducts: s = 3,
  showMatchScores: i = !0,
  showFeatures: n = !0,
  className: a,
  style: d
}) => {
  var p, h;
  me();
  const c = te(() => t.slice(0, s), [t, s]), u = _(
    "admesh-component",
    "admesh-compare-layout",
    a
  ), m = r != null && r.accentColor ? {
    "--admesh-primary": r.accentColor
  } : void 0;
  return c.length === 0 ? /* @__PURE__ */ e.jsx(
    "div",
    {
      className: u,
      style: {
        ...m,
        fontFamily: (r == null ? void 0 : r.fontFamily) || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        ...(p = r == null ? void 0 : r.components) == null ? void 0 : p.compareTable,
        ...d
      },
      "data-admesh-theme": r == null ? void 0 : r.mode,
      children: /* @__PURE__ */ e.jsx("div", { className: "p-8 text-center text-gray-500 dark:text-gray-400", children: /* @__PURE__ */ e.jsx("p", { children: "No products to compare" }) })
    }
  ) : /* @__PURE__ */ e.jsx(
    "div",
    {
      className: u,
      style: {
        ...m,
        fontFamily: (r == null ? void 0 : r.fontFamily) || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        ...(h = r == null ? void 0 : r.components) == null ? void 0 : h.compareTable,
        ...d
      },
      "data-admesh-theme": r == null ? void 0 : r.mode,
      children: /* @__PURE__ */ e.jsxs("div", { className: "space-y-6", children: [
        /* @__PURE__ */ e.jsxs("div", { className: "text-center", children: [
          /* @__PURE__ */ e.jsxs("div", { className: "flex items-center justify-center gap-2 mb-2", children: [
            /* @__PURE__ */ e.jsx("svg", { className: "w-5 h-5 text-gray-600 dark:text-gray-400", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" }) }),
            /* @__PURE__ */ e.jsx("h3", { className: "text-lg font-semibold text-gray-800 dark:text-gray-200", children: "Smart Comparison" })
          ] }),
          /* @__PURE__ */ e.jsxs("p", { className: "text-sm text-gray-600 dark:text-gray-400", children: [
            c.length,
            " intelligent matches found"
          ] })
        ] }),
        /* @__PURE__ */ e.jsx("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4", children: c.map((o, k) => /* @__PURE__ */ e.jsxs(
          "div",
          {
            className: "relative p-4 rounded-lg bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 shadow-sm hover:shadow transition-shadow",
            children: [
              /* @__PURE__ */ e.jsxs("div", { className: "flex justify-between items-start mb-3", children: [
                /* @__PURE__ */ e.jsxs("div", { className: "flex items-center gap-2", children: [
                  k === 0 && /* @__PURE__ */ e.jsx("span", { className: "text-xs font-semibold text-white bg-black px-2 py-0.5 rounded-full", children: "Top Match" }),
                  /* @__PURE__ */ e.jsxs("span", { className: "text-xs text-gray-400 dark:text-gray-500", children: [
                    "#",
                    k + 1
                  ] })
                ] }),
                i && /* @__PURE__ */ e.jsxs("div", { className: "text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap", children: [
                  Math.round(o.intent_match_score * 100),
                  "% match"
                ] })
              ] }),
              /* @__PURE__ */ e.jsx("h4", { className: "font-semibold text-gray-800 dark:text-gray-200 mb-2", children: o.title }),
              i && /* @__PURE__ */ e.jsxs("div", { className: "mb-3", children: [
                /* @__PURE__ */ e.jsxs("div", { className: "flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1", children: [
                  /* @__PURE__ */ e.jsx("span", { children: "Match Score" }),
                  /* @__PURE__ */ e.jsxs("span", { className: "whitespace-nowrap", children: [
                    Math.round(o.intent_match_score * 100),
                    "% match"
                  ] })
                ] }),
                /* @__PURE__ */ e.jsx("div", { className: "w-full bg-gray-200 dark:bg-slate-600 rounded h-1.5 overflow-hidden", children: /* @__PURE__ */ e.jsx(
                  "div",
                  {
                    className: "bg-black h-1.5",
                    style: { width: `${Math.round(o.intent_match_score * 100)}%` }
                  }
                ) })
              ] }),
              /* @__PURE__ */ e.jsxs("div", { className: "flex flex-wrap gap-2 text-xs mb-3", children: [
                o.pricing && /* @__PURE__ */ e.jsxs("span", { className: "flex items-center text-gray-600 dark:text-gray-400", children: [
                  /* @__PURE__ */ e.jsx("svg", { className: "h-3 w-3 mr-1", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" }) }),
                  o.pricing
                ] }),
                o.trial_days && o.trial_days > 0 && /* @__PURE__ */ e.jsxs("span", { className: "flex items-center text-gray-600 dark:text-gray-400", children: [
                  /* @__PURE__ */ e.jsx("svg", { className: "h-3 w-3 mr-1", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6 0h6" }) }),
                  o.trial_days,
                  "-day trial"
                ] })
              ] }),
              n && o.features && o.features.length > 0 && /* @__PURE__ */ e.jsxs("div", { className: "mb-3", children: [
                /* @__PURE__ */ e.jsx("div", { className: "text-xs text-gray-500 dark:text-gray-400 mb-1", children: "Key Features:" }),
                /* @__PURE__ */ e.jsxs("div", { className: "flex flex-wrap gap-1.5", children: [
                  o.features.slice(0, 4).map((f, y) => /* @__PURE__ */ e.jsxs(
                    "span",
                    {
                      className: "text-xs px-2 py-0.5 rounded-full bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-gray-300",
                      children: [
                        /* @__PURE__ */ e.jsx("svg", { className: "h-3 w-3 mr-0.5 inline text-gray-500", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" }) }),
                        f
                      ]
                    },
                    y
                  )),
                  (o.features.length || 0) > 4 && /* @__PURE__ */ e.jsxs("span", { className: "text-xs text-gray-500 dark:text-gray-400 italic", children: [
                    "+",
                    o.features.length - 4,
                    " more"
                  ] })
                ] })
              ] }),
              /* @__PURE__ */ e.jsx(
                D,
                {
                  adId: o.ad_id,
                  admeshLink: o.admesh_link,
                  productId: o.product_id,
                  trackingData: {
                    title: o.title,
                    matchScore: o.intent_match_score,
                    component: "compare_table_cta"
                  },
                  children: /* @__PURE__ */ e.jsxs("button", { className: "w-full text-xs px-3 py-2 rounded-lg bg-black text-white hover:bg-gray-800 flex items-center justify-center gap-1 mt-auto transition-colors", children: [
                    "Visit Offer",
                    /* @__PURE__ */ e.jsx("svg", { className: "h-3 w-3", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" }) })
                  ] })
                }
              )
            ]
          },
          o.product_id || k
        )) }),
        /* @__PURE__ */ e.jsx("div", { className: "flex items-center justify-center mt-6 pt-4 border-t border-gray-200/50 dark:border-gray-700/50", children: /* @__PURE__ */ e.jsxs("span", { className: "flex items-center gap-1.5 text-xs text-gray-400 dark:text-gray-500", children: [
          /* @__PURE__ */ e.jsx("svg", { className: "w-3 h-3 text-indigo-500", fill: "currentColor", viewBox: "0 0 20 20", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z", clipRule: "evenodd" }) }),
          /* @__PURE__ */ e.jsx("span", { className: "font-medium", children: "Powered by" }),
          /* @__PURE__ */ e.jsx("span", { className: "font-semibold text-indigo-600 dark:text-indigo-400", children: "AdMesh" })
        ] }) })
      ] })
    }
  );
};
Ie.displayName = "AdMeshCompareTable";
const Ee = {
  "Top Match": "primary",
  "Free Tier": "success",
  "AI Powered": "secondary",
  Popular: "warning",
  New: "primary",
  "Trial Available": "success"
}, ze = {
  "Top Match": "★",
  "Free Tier": "◆",
  "AI Powered": "◉",
  Popular: "▲",
  New: "●",
  "Trial Available": "◈"
}, Pe = ({
  type: t,
  variant: r,
  size: s = "md",
  className: i,
  style: n
}) => {
  const a = r || Ee[t] || "secondary", d = ze[t], c = _(
    "admesh-component",
    "admesh-badge",
    `admesh-badge--${a}`,
    `admesh-badge--${s}`,
    i
  );
  return /* @__PURE__ */ e.jsxs(
    "span",
    {
      className: c,
      style: n,
      children: [
        d && /* @__PURE__ */ e.jsx("span", { className: "admesh-badge__icon", children: d }),
        /* @__PURE__ */ e.jsx("span", { className: "admesh-badge__text", children: t })
      ]
    }
  );
};
Pe.displayName = "AdMeshBadge";
const Xe = ({
  recommendation: t,
  theme: r = { mode: "light" },
  className: s = "",
  style: i,
  showPoweredBy: n = !0,
  initialExpanded: a = !1,
  sections: d,
  ctaText: c,
  collapsible: u = !0
}) => {
  var M, C, T, w, S, I, z, E, F, V;
  me();
  const [m, p] = R(a), h = () => {
    u && p(!m);
  }, o = t.feature_sections || [], k = [
    {
      title: "Documentation",
      description: `Learn more about ${t.recommendation_title || t.title}. Start exploring the features and capabilities.`,
      icon: "◆"
    },
    {
      title: "Talk To An Expert",
      description: `Ready to learn more about ${t.recommendation_title || t.title}? Reach out to a platform specialist for personalized guidance.`,
      icon: "◉"
    },
    {
      title: `${t.recommendation_title || t.title} Features`,
      description: t.recommendation_description || t.description || `${t.recommendation_title || t.title} offers comprehensive solutions for your needs. Discover the full potential.`,
      icon: "▲"
    },
    {
      title: "How it Works",
      description: `Learn how to get started with ${t.recommendation_title || t.title}. Begin your journey today.`,
      icon: "●"
    }
  ], f = xe(t, !1), y = he(), g = d || (o.length > 0 ? o : k), v = c || `Try ${t.recommendation_title || t.title}`, b = {
    background: r.backgroundColor || (r.mode === "dark" ? "#1f2937" : "#ffffff"),
    surface: r.surfaceColor || (r.mode === "dark" ? "#374151" : "#f9fafb"),
    border: r.borderColor || (r.mode === "dark" ? "#4b5563" : "#e5e7eb"),
    text: r.textColor || (r.mode === "dark" ? "#f9fafb" : "#111827"),
    textSecondary: r.textSecondaryColor || (r.mode === "dark" ? "#9ca3af" : "#6b7280"),
    accent: r.accentColor || r.primaryColor || "#3b82f6",
    secondary: r.secondaryColor || "#10b981",
    // Remove excessive gradients, use clean solid colors or subtle gradients
    headerBg: ((M = r.gradients) == null ? void 0 : M.primary) || (r.mode === "dark" ? "#374151" : "#f8fafc"),
    sectionBg: ((C = r.gradients) == null ? void 0 : C.secondary) || (r.mode === "dark" ? "#4b5563" : "#ffffff")
  }, j = r.disableDefaultStyles ? {} : {
    fontFamily: r.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    borderRadius: r.borderRadius || "12px",
    border: `1px solid ${b.border}`,
    background: b.background,
    overflow: "hidden",
    maxWidth: "420px",
    boxShadow: ((T = r.shadows) == null ? void 0 : T.medium) || (r.mode === "dark" ? "0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)" : "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"),
    position: "relative",
    transition: "all 0.2s ease"
  };
  return /* @__PURE__ */ e.jsxs(
    "div",
    {
      className: `admesh-component admesh-expandable-unit ${s}`,
      style: {
        ...j,
        ...(w = r.components) == null ? void 0 : w.expandableUnit,
        ...i
      },
      "data-admesh-theme": r.mode,
      children: [
        /* @__PURE__ */ e.jsx(
          "div",
          {
            style: {
              background: b.headerBg,
              padding: "20px",
              borderBottom: m || !u ? `1px solid ${b.border}` : "none",
              position: "relative",
              transition: "all 0.2s ease"
            },
            children: /* @__PURE__ */ e.jsxs("div", { style: { display: "flex", alignItems: "center", justifyContent: "space-between", gap: "16px" }, children: [
              /* @__PURE__ */ e.jsxs("div", { style: { display: "flex", alignItems: "center", gap: "16px", flex: 1, minWidth: 0 }, children: [
                /* @__PURE__ */ e.jsx(
                  "div",
                  {
                    style: {
                      width: "40px",
                      height: "40px",
                      borderRadius: r.borderRadius || "8px",
                      background: b.accent,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      color: "white",
                      fontSize: ((S = r.fontSize) == null ? void 0 : S.base) || "16px",
                      fontWeight: "600",
                      boxShadow: ((I = r.shadows) == null ? void 0 : I.small) || "0 2px 4px rgba(0, 0, 0, 0.1)",
                      border: `1px solid ${b.border}`
                    },
                    children: (t.recommendation_title || t.title).charAt(0).toUpperCase()
                  }
                ),
                /* @__PURE__ */ e.jsxs("div", { style: { flex: 1, minWidth: 0 }, children: [
                  /* @__PURE__ */ e.jsx(
                    "h3",
                    {
                      style: {
                        margin: 0,
                        fontSize: "18px",
                        fontWeight: "600",
                        color: b.text,
                        lineHeight: "1.4",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap"
                      },
                      children: t.recommendation_title || t.title
                    }
                  ),
                  /* @__PURE__ */ e.jsxs(
                    "p",
                    {
                      style: {
                        margin: "8px 0 0 0",
                        fontSize: "13px",
                        color: b.textSecondary,
                        fontWeight: "400",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap"
                      },
                      title: y,
                      children: [
                        f,
                        " • ",
                        new URL(t.url || t.admesh_link).hostname
                      ]
                    }
                  )
                ] })
              ] }),
              /* @__PURE__ */ e.jsxs("div", { style: { display: "flex", alignItems: "center", gap: "12px" }, children: [
                !m && u && /* @__PURE__ */ e.jsx(
                  D,
                  {
                    adId: t.ad_id,
                    admeshLink: t.admesh_link,
                    productId: t.product_id,
                    trackingData: {
                      title: t.recommendation_title || t.title,
                      component: "expandable_unit",
                      expanded: !1,
                      location: "header"
                    },
                    children: /* @__PURE__ */ e.jsx(
                      "button",
                      {
                        style: {
                          padding: (z = r.spacing) != null && z.small ? `${r.spacing.small} ${r.spacing.medium || "12px"}` : "6px 12px",
                          backgroundColor: b.accent,
                          color: "white",
                          border: "none",
                          borderRadius: r.borderRadius || "6px",
                          fontSize: ((E = r.fontSize) == null ? void 0 : E.small) || "12px",
                          fontWeight: "500",
                          cursor: "pointer",
                          transition: "all 0.2s ease",
                          boxShadow: ((F = r.shadows) == null ? void 0 : F.small) || "0 1px 3px rgba(0, 0, 0, 0.1)",
                          whiteSpace: "nowrap",
                          ...(V = r.components) == null ? void 0 : V.button
                        },
                        onMouseOver: (x) => {
                          var L;
                          r.disableDefaultStyles || (x.currentTarget.style.transform = "translateY(-1px)", x.currentTarget.style.boxShadow = ((L = r.shadows) == null ? void 0 : L.medium) || "0 2px 6px rgba(0, 0, 0, 0.15)");
                        },
                        onMouseOut: (x) => {
                          var L;
                          r.disableDefaultStyles || (x.currentTarget.style.transform = "translateY(0)", x.currentTarget.style.boxShadow = ((L = r.shadows) == null ? void 0 : L.small) || "0 1px 3px rgba(0, 0, 0, 0.1)");
                        },
                        children: v
                      }
                    )
                  }
                ),
                u && /* @__PURE__ */ e.jsxs(
                  "button",
                  {
                    onClick: h,
                    style: {
                      display: "flex",
                      alignItems: "center",
                      gap: "8px",
                      padding: "8px 12px",
                      background: r.mode === "dark" ? "#374151" : "#f3f4f6",
                      border: `1px solid ${r.mode === "dark" ? "#4b5563" : "#d1d5db"}`,
                      borderRadius: "8px",
                      cursor: "pointer",
                      color: r.accentColor || "#2563eb",
                      fontSize: "12px",
                      fontWeight: "600",
                      transition: "all 0.2s ease"
                    },
                    onMouseEnter: (x) => {
                      x.currentTarget.style.background = r.mode === "dark" ? "#4b5563" : "#e5e7eb", x.currentTarget.style.borderColor = r.accentColor || "#2563eb";
                    },
                    onMouseLeave: (x) => {
                      x.currentTarget.style.background = r.mode === "dark" ? "#374151" : "#f3f4f6", x.currentTarget.style.borderColor = r.mode === "dark" ? "#4b5563" : "#d1d5db";
                    },
                    "aria-label": m ? "Show less details" : "Show more details",
                    children: [
                      /* @__PURE__ */ e.jsx("span", { children: m ? "Less Details" : "More Details" }),
                      /* @__PURE__ */ e.jsx(
                        "svg",
                        {
                          width: "16",
                          height: "16",
                          viewBox: "0 0 24 24",
                          fill: "none",
                          stroke: "currentColor",
                          strokeWidth: "2",
                          strokeLinecap: "round",
                          strokeLinejoin: "round",
                          children: m ? (
                            // Minus icon for collapse
                            /* @__PURE__ */ e.jsx("path", { d: "M5 12h14" })
                          ) : (
                            // Info icon for expand
                            /* @__PURE__ */ e.jsxs(e.Fragment, { children: [
                              /* @__PURE__ */ e.jsx("circle", { cx: "12", cy: "12", r: "10" }),
                              /* @__PURE__ */ e.jsx("path", { d: "M12 16v-4" }),
                              /* @__PURE__ */ e.jsx("path", { d: "M12 8h.01" })
                            ] })
                          )
                        }
                      )
                    ]
                  }
                )
              ] })
            ] })
          }
        ),
        (m || !u) && /* @__PURE__ */ e.jsxs("div", { style: { padding: "0" }, children: [
          g.map((x, L) => /* @__PURE__ */ e.jsxs(
            "div",
            {
              style: {
                padding: "24px",
                backgroundColor: L % 2 === 0 ? b.background : b.sectionBg,
                borderBottom: L < g.length - 1 ? `1px solid ${b.border}` : "none"
              },
              children: [
                /* @__PURE__ */ e.jsxs(
                  "h4",
                  {
                    style: {
                      margin: "0 0 12px 0",
                      fontSize: "15px",
                      fontWeight: "600",
                      color: b.text,
                      display: "flex",
                      alignItems: "center",
                      gap: "12px"
                    },
                    children: [
                      x.icon && /* @__PURE__ */ e.jsx("span", { children: x.icon }),
                      x.title
                    ]
                  }
                ),
                /* @__PURE__ */ e.jsx(
                  "p",
                  {
                    style: {
                      margin: 0,
                      fontSize: "14px",
                      color: b.textSecondary,
                      lineHeight: "1.6"
                    },
                    children: x.description
                  }
                )
              ]
            },
            L
          )),
          (m || !u) && /* @__PURE__ */ e.jsx("div", { style: { padding: "24px", borderTop: `1px solid ${b.border}`, backgroundColor: b.background }, children: /* @__PURE__ */ e.jsx(
            D,
            {
              adId: t.ad_id,
              admeshLink: t.admesh_link,
              productId: t.product_id,
              trackingData: {
                title: t.title,
                component: "expandable_unit",
                expanded: m,
                location: "footer"
              },
              children: /* @__PURE__ */ e.jsx(
                "button",
                {
                  style: {
                    width: "100%",
                    padding: "14px 28px",
                    background: b.accent,
                    color: "white",
                    border: "none",
                    borderRadius: "12px",
                    fontSize: "15px",
                    fontWeight: "600",
                    cursor: "pointer",
                    transition: "all 0.3s ease",
                    boxShadow: "0 4px 12px rgba(99, 102, 241, 0.3)",
                    position: "relative",
                    overflow: "hidden"
                  },
                  onMouseOver: (x) => {
                    x.currentTarget.style.transform = "translateY(-2px) scale(1.02)", x.currentTarget.style.boxShadow = "0 8px 20px rgba(99, 102, 241, 0.4)";
                  },
                  onMouseOut: (x) => {
                    x.currentTarget.style.transform = "translateY(0) scale(1)", x.currentTarget.style.boxShadow = "0 4px 12px rgba(99, 102, 241, 0.3)";
                  },
                  children: v
                }
              )
            }
          ) }),
          n && /* @__PURE__ */ e.jsx(
            "div",
            {
              style: {
                padding: "8px 16px",
                borderTop: `1px solid ${b.border}`,
                backgroundColor: b.headerBg
              },
              children: /* @__PURE__ */ e.jsxs(
                "div",
                {
                  style: {
                    fontSize: "11px",
                    color: b.textSecondary,
                    textAlign: "center"
                  },
                  children: [
                    "powered by ",
                    /* @__PURE__ */ e.jsx("strong", { style: { color: b.text }, children: "AdMesh" })
                  ]
                }
              )
            }
          )
        ] })
      ]
    }
  );
}, q = ({
  recommendation: t,
  theme: r,
  compact: s = !1,
  showReason: i = !0,
  className: n,
  style: a
}) => {
  var h;
  const d = Math.round(t.intent_match_score * 100), c = xe(t, s), u = he(), m = _(
    "admesh-inline-recommendation",
    "group cursor-pointer transition-all duration-200",
    {
      "p-2 sm:p-3 rounded-md bg-gray-50 dark:bg-slate-800/50 hover:bg-gray-100 dark:hover:bg-slate-800 border border-gray-200 dark:border-slate-700": !s,
      "p-1.5 sm:p-2 rounded hover:bg-gray-50 dark:hover:bg-slate-800/30": s
    },
    n
  ), p = r != null && r.accentColor ? {
    "--admesh-primary": r.accentColor
  } : void 0;
  return /* @__PURE__ */ e.jsx(
    D,
    {
      adId: t.ad_id,
      admeshLink: t.admesh_link,
      productId: t.product_id,
      trackingData: {
        title: t.title,
        matchScore: t.intent_match_score
      },
      className: m,
      style: {
        fontFamily: (r == null ? void 0 : r.fontFamily) || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        ...(h = r == null ? void 0 : r.components) == null ? void 0 : h.inlineRecommendation,
        ...a
      },
      children: /* @__PURE__ */ e.jsxs(
        "div",
        {
          className: "flex items-start gap-3",
          style: p,
          "data-admesh-theme": r == null ? void 0 : r.mode,
          children: [
            /* @__PURE__ */ e.jsx("div", { className: "flex-shrink-0 mt-0.5", children: t.offer_images && t.offer_images.length > 0 ? /* @__PURE__ */ e.jsx("div", { className: "w-6 h-6 rounded-full overflow-hidden border border-gray-200 dark:border-gray-600", children: /* @__PURE__ */ e.jsx(
              "img",
              {
                src: t.offer_images[0].url,
                alt: t.recommendation_title || t.title,
                className: "w-full h-full object-cover"
              }
            ) }) : t.product_logo ? /* @__PURE__ */ e.jsx("div", { className: "w-6 h-6 rounded-full overflow-hidden border border-gray-200 dark:border-gray-600", children: /* @__PURE__ */ e.jsx(
              "img",
              {
                src: t.product_logo.url,
                alt: t.recommendation_title || t.title,
                className: "w-full h-full object-cover"
              }
            ) }) : t.intent_match_score >= 0.8 ? /* @__PURE__ */ e.jsx("div", { className: "w-2 h-2 bg-green-500 rounded-full" }) : /* @__PURE__ */ e.jsx("div", { className: "w-2 h-2 bg-blue-500 rounded-full" }) }),
            /* @__PURE__ */ e.jsxs("div", { className: "flex-1 min-w-0", children: [
              /* @__PURE__ */ e.jsxs("div", { className: "flex items-start sm:items-center gap-2 mb-1 flex-col sm:flex-row", children: [
                /* @__PURE__ */ e.jsx("h4", { className: _(
                  "font-medium transition-colors duration-200 flex-shrink-0",
                  "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300",
                  "cursor-pointer hover:underline",
                  s ? "text-sm sm:text-base" : "text-base sm:text-lg"
                ), children: t.recommendation_title || t.title }),
                t.intent_match_score >= 0.7 && /* @__PURE__ */ e.jsxs("span", { className: _(
                  "inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium flex-shrink-0 whitespace-nowrap",
                  t.intent_match_score >= 0.8 ? "bg-green-100 text-green-800 dark:bg-green-800/80 dark:text-green-100" : "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"
                ), children: [
                  d,
                  "% match"
                ] })
              ] }),
              i && (t.recommendation_description || t.reason) && /* @__PURE__ */ e.jsx("p", { className: _(
                "text-gray-600 dark:text-gray-400 line-clamp-2",
                s ? "text-xs" : "text-sm"
              ), children: t.recommendation_description || t.reason }),
              /* @__PURE__ */ e.jsx(
                "p",
                {
                  className: _(
                    "text-gray-500 dark:text-gray-400 mt-1",
                    "text-xs"
                  ),
                  title: u,
                  children: c
                }
              ),
              !s && t.keywords && t.keywords.length > 0 && /* @__PURE__ */ e.jsxs("div", { className: "flex flex-wrap gap-1 mt-2", children: [
                t.keywords.slice(0, 3).map((o, k) => /* @__PURE__ */ e.jsx(
                  "span",
                  {
                    className: "inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-slate-700 dark:text-gray-300",
                    children: o
                  },
                  k
                )),
                t.keywords.length > 3 && /* @__PURE__ */ e.jsxs("span", { className: "text-xs text-gray-500 dark:text-gray-400", children: [
                  "+",
                  t.keywords.length - 3,
                  " more"
                ] })
              ] }),
              !s && t.trial_days && t.trial_days > 0 && /* @__PURE__ */ e.jsx("div", { className: "flex items-center gap-2 mt-2", children: /* @__PURE__ */ e.jsxs("span", { className: "inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400", children: [
                t.trial_days,
                "-day trial"
              ] }) })
            ] }),
            /* @__PURE__ */ e.jsx("div", { className: "flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity", children: /* @__PURE__ */ e.jsx(
              "svg",
              {
                className: "w-4 h-4 text-gray-400 dark:text-gray-500",
                fill: "none",
                stroke: "currentColor",
                viewBox: "0 0 24 24",
                children: /* @__PURE__ */ e.jsx(
                  "path",
                  {
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: 2,
                    d: "M9 5l7 7-7 7"
                  }
                )
              }
            ) })
          ]
        }
      )
    }
  );
}, Be = ({
  recommendations: t,
  conversationSummary: r,
  theme: s,
  showTopRecommendations: i = 3,
  onRecommendationClick: n,
  onStartNewConversation: a,
  className: d
}) => {
  const c = t.sort((p, h) => h.intent_match_score - p.intent_match_score).slice(0, i), u = _(
    "admesh-conversation-summary",
    "bg-white dark:bg-black",
    "rounded-lg border border-gray-200 dark:border-gray-800 p-4 sm:p-6",
    "font-sans",
    // Standardize font family
    d
  ), m = s != null && s.accentColor ? {
    "--admesh-primary": s.accentColor
  } : void 0;
  return /* @__PURE__ */ e.jsxs(
    "div",
    {
      className: u,
      style: m,
      "data-admesh-theme": s == null ? void 0 : s.mode,
      children: [
        /* @__PURE__ */ e.jsxs("div", { className: "flex items-center gap-3 mb-4", children: [
          /* @__PURE__ */ e.jsx("div", { className: "flex-shrink-0", children: /* @__PURE__ */ e.jsx("div", { className: "w-8 h-8 sm:w-10 sm:h-10 bg-black dark:bg-white rounded-full flex items-center justify-center", children: /* @__PURE__ */ e.jsx("svg", { className: "w-4 h-4 sm:w-5 sm:h-5 text-white dark:text-black", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" }) }) }) }),
          /* @__PURE__ */ e.jsxs("div", { className: "min-w-0 flex-1", children: [
            /* @__PURE__ */ e.jsx("h3", { className: "text-base sm:text-lg font-semibold text-black dark:text-white", children: "Conversation Summary" }),
            /* @__PURE__ */ e.jsx("p", { className: "text-xs sm:text-sm text-gray-600 dark:text-gray-300", children: "Here's what we discussed and found for you" })
          ] })
        ] }),
        /* @__PURE__ */ e.jsx("div", { className: "mb-6", children: /* @__PURE__ */ e.jsx("div", { className: "bg-gray-50 dark:bg-gray-900 rounded-lg p-4 border border-gray-200 dark:border-gray-700", children: /* @__PURE__ */ e.jsx("p", { className: "text-gray-800 dark:text-gray-200 leading-relaxed", children: r }) }) }),
        c.length > 0 && /* @__PURE__ */ e.jsxs("div", { className: "mb-6", children: [
          /* @__PURE__ */ e.jsxs("div", { className: "flex items-center gap-2 mb-3", children: [
            /* @__PURE__ */ e.jsx("svg", { className: "w-5 h-5 text-black dark:text-white", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M13 10V3L4 14h7v7l9-11h-7z" }) }),
            /* @__PURE__ */ e.jsx("h4", { className: "font-medium text-black dark:text-white", children: "Top Recommendations" })
          ] }),
          /* @__PURE__ */ e.jsx("div", { className: "space-y-2", children: c.map((p, h) => /* @__PURE__ */ e.jsxs("div", { className: "relative", children: [
            /* @__PURE__ */ e.jsx("div", { className: "absolute -left-2 top-2 z-10", children: /* @__PURE__ */ e.jsx("div", { className: _(
              "w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold",
              h === 0 ? "bg-black dark:bg-white text-white dark:text-black" : h === 1 ? "bg-gray-600 dark:bg-gray-400 text-white dark:text-black" : "bg-gray-800 dark:bg-gray-200 text-white dark:text-black"
            ), children: h + 1 }) }),
            /* @__PURE__ */ e.jsx("div", { className: "ml-4", children: /* @__PURE__ */ e.jsx(
              q,
              {
                recommendation: p,
                theme: s,
                compact: !0,
                showReason: !0,
                onClick: n
              }
            ) })
          ] }, p.ad_id || h)) })
        ] }),
        t.length > i && /* @__PURE__ */ e.jsx("div", { className: "mb-6", children: /* @__PURE__ */ e.jsx("div", { className: "bg-gray-100 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700", children: /* @__PURE__ */ e.jsxs("div", { className: "flex items-center gap-2", children: [
          /* @__PURE__ */ e.jsx("svg", { className: "w-4 h-4 text-black dark:text-white", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" }) }),
          /* @__PURE__ */ e.jsxs("span", { className: "text-sm font-medium text-gray-800 dark:text-gray-200", children: [
            t.length - i,
            " additional recommendation",
            t.length - i > 1 ? "s" : "",
            " available"
          ] })
        ] }) }) }),
        /* @__PURE__ */ e.jsxs("div", { className: "flex flex-col sm:flex-row gap-3", children: [
          a && /* @__PURE__ */ e.jsxs(
            "button",
            {
              onClick: a,
              className: "flex-1 bg-black dark:bg-white hover:bg-gray-800 dark:hover:bg-gray-100 text-white dark:text-black font-medium py-2.5 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2",
              children: [
                /* @__PURE__ */ e.jsx("svg", { className: "w-4 h-4", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" }) }),
                "Start New Conversation"
              ]
            }
          ),
          /* @__PURE__ */ e.jsxs(
            "button",
            {
              onClick: () => {
                c.length > 0 && (n == null || n(c[0].ad_id, c[0].admesh_link));
              },
              className: "flex-1 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 text-black dark:text-white font-medium py-2.5 px-4 rounded-lg border border-gray-300 dark:border-gray-600 transition-all duration-200 flex items-center justify-center gap-2",
              children: [
                /* @__PURE__ */ e.jsx("svg", { className: "w-4 h-4", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" }) }),
                "View Top Pick"
              ]
            }
          )
        ] }),
        /* @__PURE__ */ e.jsx("div", { className: "flex justify-center mt-4 pt-4 border-t border-gray-200 dark:border-gray-700", children: /* @__PURE__ */ e.jsx("span", { className: "text-xs text-gray-500 dark:text-gray-400", children: "Powered by AdMesh" }) })
      ]
    }
  );
}, ye = ({
  recommendation: t,
  citationNumber: r,
  citationStyle: s = "numbered",
  theme: i,
  showTooltip: n = !0,
  onHover: a,
  className: d,
  style: c
}) => {
  const [u, m] = R(!1), p = () => {
    m(!0), a == null || a(t);
  }, h = () => {
    m(!1);
  }, o = () => {
    switch (s) {
      case "bracketed":
        return `[${r}]`;
      case "superscript":
        return r.toString();
      case "numbered":
      default:
        return r.toString();
    }
  }, k = _(
    "admesh-citation-reference",
    "inline-flex items-center justify-center",
    "cursor-pointer transition-all duration-200",
    "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300",
    "font-medium",
    {
      // Numbered style (default)
      "w-5 h-5 bg-blue-100 dark:bg-blue-900/30 rounded-full text-xs border border-blue-300 dark:border-blue-700 hover:bg-blue-200 dark:hover:bg-blue-900/50": s === "numbered",
      // Bracketed style
      "px-1 text-sm hover:underline": s === "bracketed",
      // Superscript style
      "text-xs align-super hover:underline": s === "superscript"
    },
    d
  ), f = i != null && i.accentColor ? {
    "--admesh-primary": i.accentColor
  } : void 0;
  return /* @__PURE__ */ e.jsxs("span", { className: "relative inline-block", children: [
    /* @__PURE__ */ e.jsx(
      D,
      {
        adId: t.ad_id,
        admeshLink: t.admesh_link,
        productId: t.product_id,
        trackingData: {
          title: t.title,
          matchScore: t.intent_match_score,
          citationNumber: r,
          citationStyle: s
        },
        className: k,
        style: c,
        children: /* @__PURE__ */ e.jsx(
          "span",
          {
            style: f,
            "data-admesh-theme": i == null ? void 0 : i.mode,
            onMouseEnter: p,
            onMouseLeave: h,
            children: o()
          }
        )
      }
    ),
    n && u && /* @__PURE__ */ e.jsx("div", { className: "absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-50", children: /* @__PURE__ */ e.jsxs("div", { className: "bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs rounded-lg px-3 py-2 shadow-lg max-w-xs", children: [
      /* @__PURE__ */ e.jsx("div", { className: "font-semibold mb-1", children: t.title }),
      t.reason && /* @__PURE__ */ e.jsx("div", { className: "text-gray-300 dark:text-gray-600 text-xs", children: t.reason.length > 100 ? `${t.reason.substring(0, 100)}...` : t.reason }),
      t.intent_match_score >= 0.7 && /* @__PURE__ */ e.jsxs("div", { className: "text-green-400 dark:text-green-600 text-xs mt-1", children: [
        Math.round(t.intent_match_score * 100),
        "% match"
      ] }),
      /* @__PURE__ */ e.jsx("div", { className: "text-gray-400 dark:text-gray-500 text-xs mt-1 italic", children: "Click to visit product page" }),
      /* @__PURE__ */ e.jsx("div", { className: "absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-100" })
    ] }) })
  ] });
}, We = ({
  recommendations: t,
  conversationText: r,
  theme: s,
  showCitationList: i = !0,
  citationStyle: n = "numbered",
  onCitationHover: a,
  className: d,
  style: c
}) => {
  var f;
  const [u, m] = R(null), p = te(() => {
    if (!r || t.length === 0)
      return { text: r, citationMap: /* @__PURE__ */ new Map() };
    let y = r;
    const g = /* @__PURE__ */ new Map();
    return [...t].sort((b, j) => j.intent_match_score - b.intent_match_score).forEach((b, j) => {
      const M = j + 1, C = b.title;
      g.set(M, b);
      const T = new RegExp(`\\b${C.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}\\b`, "gi");
      if (T.test(y))
        y = y.replace(T, (w) => `${w}{{CITATION_${M}}}`);
      else {
        const w = b.keywords || [];
        let S = !1;
        for (const I of w) {
          const z = new RegExp(`\\b${I.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}\\b`, "gi");
          if (z.test(y) && !S) {
            y = y.replace(z, (E) => (S = !0, `${E}{{CITATION_${M}}}`));
            break;
          }
        }
        S || (y += `{{CITATION_${M}}}`);
      }
    }), { text: y, citationMap: g };
  }, [r, t]), h = () => {
    const { text: y, citationMap: g } = p;
    return y.split(/(\{\{CITATION_\d+\}\})/).map((b, j) => {
      const M = b.match(/\{\{CITATION_(\d+)\}\}/);
      if (M) {
        const C = parseInt(M[1]), T = g.get(C);
        if (T)
          return /* @__PURE__ */ e.jsx(
            ye,
            {
              recommendation: T,
              citationNumber: C,
              citationStyle: n,
              theme: s,
              showTooltip: !0,
              onHover: (w) => {
                m(w), a == null || a(w);
              }
            },
            `citation-${C}-${j}`
          );
      }
      return /* @__PURE__ */ e.jsx("span", { children: b }, j);
    });
  }, o = _(
    "admesh-citation-unit",
    "space-y-4",
    d
  ), k = s != null && s.accentColor ? {
    "--admesh-primary": s.accentColor
  } : void 0;
  return /* @__PURE__ */ e.jsxs(
    "div",
    {
      className: o,
      style: {
        ...k,
        fontFamily: (s == null ? void 0 : s.fontFamily) || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        ...(f = s == null ? void 0 : s.components) == null ? void 0 : f.citationUnit,
        ...c
      },
      "data-admesh-theme": s == null ? void 0 : s.mode,
      children: [
        /* @__PURE__ */ e.jsx("div", { className: "admesh-citation-text text-gray-800 dark:text-gray-200 leading-relaxed", children: h() }),
        i && t.length > 0 && /* @__PURE__ */ e.jsx("div", { className: "admesh-citation-list", children: /* @__PURE__ */ e.jsxs("div", { className: "border-t border-gray-200 dark:border-slate-700 pt-4", children: [
          /* @__PURE__ */ e.jsxs("h4", { className: "text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2", children: [
            /* @__PURE__ */ e.jsx("svg", { className: "w-4 h-4", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" }) }),
            "References"
          ] }),
          /* @__PURE__ */ e.jsx("div", { className: "space-y-2", children: t.sort((y, g) => g.intent_match_score - y.intent_match_score).map((y, g) => /* @__PURE__ */ e.jsxs(
            "div",
            {
              className: _(
                "flex items-start gap-3 p-2 rounded-lg transition-colors duration-200",
                {
                  "bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800": (u == null ? void 0 : u.ad_id) === y.ad_id,
                  "hover:bg-gray-50 dark:hover:bg-slate-800/50": (u == null ? void 0 : u.ad_id) !== y.ad_id
                }
              ),
              children: [
                /* @__PURE__ */ e.jsx("div", { className: "flex-shrink-0 mt-1", children: /* @__PURE__ */ e.jsx(
                  ye,
                  {
                    recommendation: y,
                    citationNumber: g + 1,
                    citationStyle: n,
                    theme: s,
                    showTooltip: !1
                  }
                ) }),
                /* @__PURE__ */ e.jsx("div", { className: "flex-1 min-w-0", children: /* @__PURE__ */ e.jsx(
                  q,
                  {
                    recommendation: y,
                    theme: s,
                    compact: !0,
                    showReason: !1
                  }
                ) })
              ]
            },
            y.ad_id || g
          )) })
        ] }) })
      ]
    }
  );
}, ke = ({
  recommendations: t,
  config: r,
  theme: s,
  conversationSummary: i,
  sessionId: n,
  onRecommendationClick: a,
  onDismiss: d,
  className: c
}) => {
  const [u, m] = R(r.autoShow !== !1), [p, h] = R(!1);
  if (W(() => {
    if (r.delayMs && r.delayMs > 0) {
      const j = setTimeout(() => {
        m(!0), h(!0);
      }, r.delayMs);
      return () => clearTimeout(j);
    } else
      h(!0);
  }, [r.delayMs]), !u || t.length === 0)
    return null;
  const o = r.maxRecommendations || 3, k = t.slice(0, o), f = (j, M) => {
    a == null || a(j, M);
  }, y = () => {
    m(!1), d == null || d();
  }, g = () => {
    switch (r.displayMode) {
      case "summary":
        return i ? /* @__PURE__ */ e.jsx(
          Be,
          {
            recommendations: k,
            conversationSummary: i,
            theme: s,
            showTopRecommendations: o,
            onRecommendationClick: f,
            onStartNewConversation: d
          }
        ) : null;
      case "inline":
        return /* @__PURE__ */ e.jsx("div", { className: "space-y-2", children: k.map((j, M) => /* @__PURE__ */ e.jsx(
          q,
          {
            recommendation: j,
            theme: s,
            compact: !0,
            showReason: !0,
            onClick: f
          },
          j.ad_id || M
        )) });
      case "minimal":
        return k.length > 0 ? /* @__PURE__ */ e.jsxs("div", { className: "admesh-minimal-unit", children: [
          /* @__PURE__ */ e.jsxs("div", { className: "flex items-center gap-2 mb-2", children: [
            /* @__PURE__ */ e.jsx("div", { className: "w-2 h-2 bg-blue-500 rounded-full" }),
            /* @__PURE__ */ e.jsxs("span", { className: "text-sm font-medium text-gray-700 dark:text-gray-300", children: [
              k.length,
              " intelligent match",
              k.length > 1 ? "es" : "",
              " found"
            ] })
          ] }),
          /* @__PURE__ */ e.jsx(
            q,
            {
              recommendation: k[0],
              theme: s,
              compact: !0,
              showReason: !1,
              onClick: f
            }
          ),
          k.length > 1 && /* @__PURE__ */ e.jsxs("div", { className: "text-xs text-gray-500 dark:text-gray-400 mt-1", children: [
            "+",
            k.length - 1,
            " more recommendation",
            k.length > 2 ? "s" : ""
          ] })
        ] }) : null;
      case "citation":
        return i ? /* @__PURE__ */ e.jsx(
          We,
          {
            recommendations: k,
            conversationText: i,
            theme: s,
            showCitationList: !0,
            citationStyle: "numbered",
            onRecommendationClick: f
          }
        ) : null;
      case "floating":
        return /* @__PURE__ */ e.jsxs("div", { className: "admesh-floating-unit bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700 p-4", children: [
          /* @__PURE__ */ e.jsxs("div", { className: "flex justify-between items-start mb-3", children: [
            /* @__PURE__ */ e.jsxs("div", { className: "flex items-center gap-2", children: [
              /* @__PURE__ */ e.jsx("div", { className: "w-2 h-2 bg-blue-500 rounded-full" }),
              /* @__PURE__ */ e.jsx("span", { className: "text-sm font-semibold text-gray-800 dark:text-gray-200", children: "Recommended for you" })
            ] }),
            d && /* @__PURE__ */ e.jsx(
              "button",
              {
                onClick: y,
                className: "text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",
                "aria-label": "Dismiss recommendations",
                children: /* @__PURE__ */ e.jsx("svg", { className: "w-4 h-4", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M6 18L18 6M6 6l12 12" }) })
              }
            )
          ] }),
          /* @__PURE__ */ e.jsx("div", { className: "space-y-2", children: k.map((j, M) => /* @__PURE__ */ e.jsx(
            q,
            {
              recommendation: j,
              theme: s,
              compact: !0,
              showReason: !1,
              onClick: f
            },
            j.ad_id || M
          )) })
        ] });
      default:
        return /* @__PURE__ */ e.jsx("div", { className: "space-y-3", children: k.map((j, M) => /* @__PURE__ */ e.jsx(
          pe,
          {
            recommendation: j,
            theme: s,
            showMatchScore: !1,
            showBadges: !0,
            onClick: f
          },
          j.ad_id || M
        )) });
    }
  }, v = _(
    "admesh-conversational-unit",
    "transition-all duration-300 ease-in-out",
    {
      "opacity-0 translate-y-2": !p,
      "opacity-100 translate-y-0": p,
      "fixed bottom-4 right-4 max-w-sm z-50": r.displayMode === "floating",
      "my-3": r.displayMode === "inline",
      "mt-4 pt-4 border-t border-gray-200 dark:border-slate-700": r.displayMode === "summary"
    },
    c
  ), b = s != null && s.accentColor ? {
    "--admesh-primary": s.accentColor
  } : void 0;
  return /* @__PURE__ */ e.jsxs(
    "div",
    {
      className: v,
      style: b,
      "data-admesh-theme": s == null ? void 0 : s.mode,
      "data-admesh-context": r.context,
      "data-session-id": n,
      children: [
        g(),
        r.showPoweredBy !== !1 && /* @__PURE__ */ e.jsx("div", { className: "flex justify-end mt-2", children: /* @__PURE__ */ e.jsx("span", { className: "text-xs text-gray-400 dark:text-gray-500", children: "Powered by AdMesh" }) })
      ]
    }
  );
}, Fe = ({
  message: t,
  theme: r,
  onRecommendationClick: s,
  className: i
}) => {
  const n = t.role === "user", a = t.role === "assistant", d = _(
    "admesh-chat-message",
    "flex items-start gap-3",
    {
      "flex-row-reverse": n
    },
    i
  ), c = _(
    "max-w-xs lg:max-w-sm px-4 py-3 rounded-lg text-sm",
    {
      "bg-gradient-to-r from-blue-600 to-indigo-600 text-white": n,
      "bg-gray-100 dark:bg-slate-800 text-gray-900 dark:text-gray-100": a,
      "bg-yellow-100 dark:bg-yellow-900 text-yellow-900 dark:text-yellow-100": t.role === "system"
    }
  ), u = r != null && r.accentColor ? {
    "--admesh-primary": r.accentColor
  } : void 0, m = (p) => p.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  return /* @__PURE__ */ e.jsxs(
    "div",
    {
      className: d,
      style: u,
      "data-admesh-theme": r == null ? void 0 : r.mode,
      children: [
        !n && /* @__PURE__ */ e.jsx("div", { className: "w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center flex-shrink-0", children: /* @__PURE__ */ e.jsx("svg", { className: "w-4 h-4 text-white", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M13 10V3L4 14h7v7l9-11h-7z" }) }) }),
        n && /* @__PURE__ */ e.jsx("div", { className: "w-8 h-8 bg-gray-300 dark:bg-slate-600 rounded-full flex items-center justify-center flex-shrink-0", children: /* @__PURE__ */ e.jsx("svg", { className: "w-4 h-4 text-gray-600 dark:text-gray-300", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" }) }) }),
        /* @__PURE__ */ e.jsxs("div", { className: `flex flex-col ${n ? "items-end" : "items-start"} flex-1`, children: [
          /* @__PURE__ */ e.jsx("div", { className: c, children: /* @__PURE__ */ e.jsx("div", { className: "whitespace-pre-wrap break-words", children: t.content }) }),
          /* @__PURE__ */ e.jsx("div", { className: _(
            "text-xs text-gray-500 dark:text-gray-400 mt-1",
            { "text-right": n }
          ), children: m(t.timestamp) }),
          t.recommendations && t.recommendations.length > 0 && /* @__PURE__ */ e.jsxs("div", { className: "mt-3 w-full max-w-lg", children: [
            /* @__PURE__ */ e.jsxs("div", { className: "flex items-center gap-2 mb-3", children: [
              /* @__PURE__ */ e.jsx("svg", { className: "w-4 h-4 text-blue-600 dark:text-blue-400", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M13 10V3L4 14h7v7l9-11h-7z" }) }),
              /* @__PURE__ */ e.jsxs("span", { className: "text-sm font-medium text-gray-700 dark:text-gray-300", children: [
                t.recommendations.length,
                " recommendation",
                t.recommendations.length > 1 ? "s" : "",
                " found"
              ] })
            ] }),
            /* @__PURE__ */ e.jsx(
              ke,
              {
                recommendations: t.recommendations,
                config: {
                  displayMode: "inline",
                  context: "chat",
                  maxRecommendations: 3,
                  showPoweredBy: !1,
                  autoShow: !0,
                  delayMs: 300
                },
                theme: r,
                onRecommendationClick: s,
                className: "bg-gray-50 dark:bg-slate-800/50 rounded-lg p-3 border border-gray-200 dark:border-slate-700"
              }
            )
          ] })
        ] })
      ]
    }
  );
}, Oe = ({
  placeholder: t = "Type your message...",
  disabled: r = !1,
  suggestions: s = [],
  theme: i,
  onSendMessage: n,
  className: a
}) => {
  const [d, c] = R(""), [u, m] = R(!1), [p, h] = R([]), o = re(null), k = (C) => {
    const T = C.target.value;
    if (c(T), T.trim() && s.length > 0) {
      const w = s.filter(
        (S) => S.toLowerCase().includes(T.toLowerCase())
      );
      h(w), m(w.length > 0);
    } else
      m(!1);
    o.current && (o.current.style.height = "auto", o.current.style.height = `${Math.min(o.current.scrollHeight, 120)}px`);
  }, f = (C) => {
    C.key === "Enter" && !C.shiftKey && (C.preventDefault(), y());
  }, y = () => {
    const C = d.trim();
    C && !r && n && (n(C), c(""), m(!1), o.current && (o.current.style.height = "auto"));
  }, g = (C) => {
    c(C), m(!1), o.current && o.current.focus();
  }, v = _(
    "admesh-chat-input",
    "relative",
    a
  ), b = _(
    "w-full resize-none rounded-lg border border-gray-300 dark:border-slate-600",
    "bg-white dark:bg-slate-800 text-gray-900 dark:text-gray-100",
    "placeholder-gray-500 dark:placeholder-gray-400",
    "focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent",
    "transition-all duration-200 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600",
    "pr-12 pl-4 py-3 text-sm leading-5",
    {
      "opacity-50 cursor-not-allowed": r
    }
  ), j = _(
    "absolute right-2 bottom-2 p-2 rounded-lg transition-all duration-200",
    "flex items-center justify-center",
    {
      "bg-blue-600 hover:bg-blue-700 text-white": d.trim() && !r,
      "bg-gray-200 dark:bg-slate-700 text-gray-400 dark:text-gray-500 cursor-not-allowed": !d.trim() || r
    }
  ), M = i != null && i.accentColor ? {
    "--admesh-primary": i.accentColor
  } : void 0;
  return /* @__PURE__ */ e.jsxs(
    "div",
    {
      className: v,
      style: M,
      "data-admesh-theme": i == null ? void 0 : i.mode,
      children: [
        u && p.length > 0 && /* @__PURE__ */ e.jsx("div", { className: "absolute bottom-full left-0 right-0 mb-2 bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-lg shadow-lg max-h-40 overflow-y-auto z-10", children: p.slice(0, 5).map((C, T) => /* @__PURE__ */ e.jsx(
          "button",
          {
            onClick: () => g(C),
            className: "w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors first:rounded-t-lg last:rounded-b-lg",
            children: C
          },
          T
        )) }),
        /* @__PURE__ */ e.jsxs("div", { className: "relative", children: [
          /* @__PURE__ */ e.jsx(
            "textarea",
            {
              ref: o,
              value: d,
              onChange: k,
              onKeyDown: f,
              placeholder: t,
              disabled: r,
              rows: 1,
              className: b,
              style: { minHeight: "44px", maxHeight: "120px" }
            }
          ),
          /* @__PURE__ */ e.jsx(
            "button",
            {
              onClick: y,
              disabled: !d.trim() || r,
              className: j,
              "aria-label": "Send message",
              children: /* @__PURE__ */ e.jsx("svg", { className: "w-4 h-4", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M12 19l9 2-9-18-9 18 9-2zm0 0v-8" }) })
            }
          )
        ] }),
        /* @__PURE__ */ e.jsxs("div", { className: "flex items-center justify-between mt-2 text-xs text-gray-500 dark:text-gray-400", children: [
          /* @__PURE__ */ e.jsx("span", { children: "Press Enter to send, Shift+Enter for new line" }),
          /* @__PURE__ */ e.jsxs("span", { className: _(
            "transition-opacity duration-200",
            { "opacity-0": d.length < 100 }
          ), children: [
            d.length,
            "/500"
          ] })
        ] })
      ]
    }
  );
}, $e = ({
  messages: t,
  config: r,
  theme: s,
  isLoading: i = !1,
  onSendMessage: n,
  onRecommendationClick: a,
  className: d
}) => {
  const c = re(null), u = re(null);
  W(() => {
    c.current && c.current.scrollIntoView({ behavior: "smooth" });
  }, [t]);
  const m = _(
    "admesh-chat-interface",
    "flex flex-col h-full bg-white dark:bg-slate-900",
    d
  ), p = s != null && s.accentColor ? {
    "--admesh-primary": s.accentColor
  } : void 0, h = r.maxMessages ? t.slice(-r.maxMessages) : t;
  return /* @__PURE__ */ e.jsxs(
    "div",
    {
      className: m,
      style: p,
      "data-admesh-theme": s == null ? void 0 : s.mode,
      children: [
        /* @__PURE__ */ e.jsx(
          "div",
          {
            ref: u,
            className: "flex-1 overflow-y-auto p-4 space-y-4 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600",
            children: h.length === 0 ? /* @__PURE__ */ e.jsxs("div", { className: "flex flex-col items-center justify-center h-full text-center", children: [
              /* @__PURE__ */ e.jsx("div", { className: "w-16 h-16 bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900 dark:to-indigo-900 rounded-full flex items-center justify-center mb-4", children: /* @__PURE__ */ e.jsx("svg", { className: "w-8 h-8 text-blue-600 dark:text-blue-400", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M13 10V3L4 14h7v7l9-11h-7z" }) }) }),
              /* @__PURE__ */ e.jsx("h3", { className: "text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2", children: "Welcome to AdMesh AI" }),
              /* @__PURE__ */ e.jsx("p", { className: "text-sm text-gray-600 dark:text-gray-400 max-w-xs", children: "Ask me anything about products, tools, or services. I'll provide personalized recommendations just for you!" })
            ] }) : /* @__PURE__ */ e.jsxs(e.Fragment, { children: [
              h.map((o) => /* @__PURE__ */ e.jsx(
                Fe,
                {
                  message: o,
                  theme: s,
                  onRecommendationClick: a
                },
                o.id
              )),
              i && r.enableTypingIndicator !== !1 && /* @__PURE__ */ e.jsxs("div", { className: "flex items-start gap-3", children: [
                /* @__PURE__ */ e.jsx("div", { className: "w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center flex-shrink-0", children: /* @__PURE__ */ e.jsx("svg", { className: "w-4 h-4 text-white", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M13 10V3L4 14h7v7l9-11h-7z" }) }) }),
                /* @__PURE__ */ e.jsx("div", { className: "bg-gray-100 dark:bg-slate-800 rounded-lg px-4 py-3 max-w-xs", children: /* @__PURE__ */ e.jsxs("div", { className: "flex space-x-1", children: [
                  /* @__PURE__ */ e.jsx("div", { className: "w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce" }),
                  /* @__PURE__ */ e.jsx("div", { className: "w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce", style: { animationDelay: "0.1s" } }),
                  /* @__PURE__ */ e.jsx("div", { className: "w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce", style: { animationDelay: "0.2s" } })
                ] }) })
              ] }),
              /* @__PURE__ */ e.jsx("div", { ref: c })
            ] })
          }
        ),
        r.enableSuggestions && r.suggestions && r.suggestions.length > 0 && t.length === 0 && /* @__PURE__ */ e.jsxs("div", { className: "px-4 pb-2", children: [
          /* @__PURE__ */ e.jsx("div", { className: "text-xs text-gray-500 dark:text-gray-400 mb-2", children: "Quick suggestions:" }),
          /* @__PURE__ */ e.jsx("div", { className: "flex flex-wrap gap-2", children: r.suggestions.slice(0, 3).map((o, k) => /* @__PURE__ */ e.jsx(
            "button",
            {
              onClick: () => n == null ? void 0 : n(o),
              className: "px-3 py-1.5 text-xs bg-gray-100 dark:bg-slate-800 hover:bg-gray-200 dark:hover:bg-slate-700 text-gray-700 dark:text-gray-300 rounded-full transition-colors",
              children: o
            },
            k
          )) })
        ] }),
        r.showInputField !== !1 && n && /* @__PURE__ */ e.jsx("div", { className: "border-t border-gray-200 dark:border-slate-700 p-4", children: /* @__PURE__ */ e.jsx(
          Oe,
          {
            placeholder: r.placeholder || "Ask me about products, tools, or services...",
            disabled: i,
            suggestions: r.suggestions,
            theme: s,
            onSendMessage: n
          }
        ) })
      ]
    }
  );
}, Ze = ({
  config: t,
  theme: r,
  title: s = "AI Assistant",
  subtitle: i = "Get personalized recommendations",
  isOpen: n,
  onToggle: a,
  onSendMessage: d,
  onRecommendationClick: c,
  autoRecommendations: u,
  autoRecommendationTrigger: m,
  showInputField: p = !0,
  autoShowRecommendations: h = !1,
  onAutoRecommendationDismiss: o,
  className: k
}) => {
  const [f, y] = R(t.autoOpen || !1), [g, v] = R([]), [b, j] = R(!1), [M, C] = R(!1), T = n !== void 0 ? n : f;
  W(() => {
    if (t.showWelcomeMessage && t.welcomeMessage && g.length === 0) {
      const x = {
        id: "welcome",
        role: "assistant",
        content: t.welcomeMessage,
        timestamp: /* @__PURE__ */ new Date()
      };
      v([x]);
    }
  }, [t.showWelcomeMessage, t.welcomeMessage, g.length]), W(() => {
    if (u && u.length > 0 && h) {
      const x = {
        id: `auto-${Date.now()}`,
        role: "assistant",
        content: m ? `Based on "${m}", here are some relevant recommendations:` : "I found some relevant recommendations for you:",
        timestamp: /* @__PURE__ */ new Date(),
        recommendations: u
      };
      n === void 0 && y(!0), v((L) => L.some((O) => O.id.startsWith("auto-")) ? L.map(
        (O) => O.id.startsWith("auto-") ? x : O
      ) : [...L, x]);
    }
  }, [u, h, m, n]);
  const w = () => {
    a ? a() : y(!f), C(!0);
  }, S = async (x) => {
    if (!d) return;
    const L = {
      id: `user-${Date.now()}`,
      role: "user",
      content: x,
      timestamp: /* @__PURE__ */ new Date()
    };
    v((U) => [...U, L]), j(!0);
    try {
      await d(x);
    } catch (U) {
      console.error("Error sending message:", U);
      const O = {
        id: `error-${Date.now()}`,
        role: "assistant",
        content: "Sorry, I encountered an error. Please try again.",
        timestamp: /* @__PURE__ */ new Date()
      };
      v((G) => [...G, O]);
    } finally {
      j(!1);
    }
  }, I = () => {
    switch (t.size) {
      case "sm":
        return "w-80 h-96";
      case "md":
        return "w-96 h-[32rem]";
      case "lg":
        return "w-[28rem] h-[36rem]";
      case "xl":
        return "w-[32rem] h-[40rem]";
      default:
        return "w-96 h-[32rem]";
    }
  }, E = _(
    "admesh-floating-chat",
    "fixed z-50 transition-all duration-300 ease-in-out",
    (() => {
      switch (t.position) {
        case "bottom-right":
          return "bottom-4 right-4";
        case "bottom-left":
          return "bottom-4 left-4";
        case "top-right":
          return "top-4 right-4";
        case "top-left":
          return "top-4 left-4";
        default:
          return "bottom-4 right-4";
      }
    })(),
    k
  ), F = _(
    "bg-white dark:bg-slate-900 rounded-lg shadow-2xl border border-gray-200 dark:border-slate-700 overflow-hidden",
    I(),
    {
      "opacity-0 scale-95 pointer-events-none": !T,
      "opacity-100 scale-100": T
    }
  ), V = r != null && r.accentColor ? {
    "--admesh-primary": r.accentColor
  } : void 0;
  return /* @__PURE__ */ e.jsxs(
    "div",
    {
      className: E,
      style: V,
      "data-admesh-theme": r == null ? void 0 : r.mode,
      children: [
        /* @__PURE__ */ e.jsx("div", { className: F, children: T && /* @__PURE__ */ e.jsxs(e.Fragment, { children: [
          /* @__PURE__ */ e.jsxs("div", { className: "flex items-center justify-between p-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white", children: [
            /* @__PURE__ */ e.jsxs("div", { className: "flex items-center gap-3", children: [
              /* @__PURE__ */ e.jsx("div", { className: "w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center", children: /* @__PURE__ */ e.jsx("svg", { className: "w-4 h-4", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M13 10V3L4 14h7v7l9-11h-7z" }) }) }),
              /* @__PURE__ */ e.jsxs("div", { children: [
                /* @__PURE__ */ e.jsx("h3", { className: "font-semibold text-sm", children: s }),
                /* @__PURE__ */ e.jsx("p", { className: "text-xs text-blue-100", children: i })
              ] })
            ] }),
            /* @__PURE__ */ e.jsxs("div", { className: "flex items-center gap-2", children: [
              u && u.length > 0 && o && /* @__PURE__ */ e.jsx(
                "button",
                {
                  onClick: () => {
                    o(), v((x) => x.filter((L) => !L.id.startsWith("auto-")));
                  },
                  className: "p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors",
                  "aria-label": "Dismiss recommendations",
                  title: "Dismiss recommendations",
                  children: /* @__PURE__ */ e.jsx("svg", { className: "w-4 h-4", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" }) })
                }
              ),
              /* @__PURE__ */ e.jsx(
                "button",
                {
                  onClick: w,
                  className: "p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors",
                  "aria-label": "Close chat",
                  children: /* @__PURE__ */ e.jsx("svg", { className: "w-4 h-4", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M6 18L18 6M6 6l12 12" }) })
                }
              )
            ] })
          ] }),
          /* @__PURE__ */ e.jsx(
            $e,
            {
              messages: g,
              config: {
                ...t,
                showInputField: p
              },
              theme: r,
              isLoading: b,
              onSendMessage: p ? S : () => {
              },
              onRecommendationClick: c,
              className: "h-full"
            }
          )
        ] }) }),
        !T && /* @__PURE__ */ e.jsxs(
          "button",
          {
            onClick: w,
            className: _(
              "w-14 h-14 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700",
              "text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200",
              "flex items-center justify-center relative"
            ),
            "aria-label": "Open chat",
            children: [
              /* @__PURE__ */ e.jsx("svg", { className: "w-6 h-6", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" }) }),
              !M && /* @__PURE__ */ e.jsx("div", { className: "absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse" })
            ]
          }
        ),
        T && /* @__PURE__ */ e.jsx("div", { className: "absolute bottom-2 right-2 text-xs text-gray-400 dark:text-gray-500 bg-white dark:bg-slate-900 px-2 py-1 rounded shadow-sm", children: "Powered by AdMesh" })
      ]
    }
  );
}, De = ({
  title: t,
  theme: r,
  collapsible: s = !1,
  isCollapsed: i = !1,
  onToggle: n,
  onSearch: a,
  showSearch: d = !1,
  className: c
}) => {
  const [u, m] = R(""), [p, h] = R(!1), [o, k] = R(!1);
  W(() => {
    const b = () => {
      k(window.innerWidth < 640);
    };
    return b(), window.addEventListener("resize", b), () => window.removeEventListener("resize", b);
  }, []);
  const f = (b) => {
    const j = b.target.value;
    m(j), a == null || a(j);
  }, y = () => {
    m(""), a == null || a("");
  }, g = _(
    "admesh-sidebar-header",
    "flex flex-col p-4 border-b border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-800",
    c
  ), v = r != null && r.accentColor ? {
    "--admesh-primary": r.accentColor
  } : void 0;
  return /* @__PURE__ */ e.jsxs(
    "div",
    {
      className: g,
      style: v,
      "data-admesh-theme": r == null ? void 0 : r.mode,
      children: [
        /* @__PURE__ */ e.jsxs("div", { className: "flex items-center justify-between mb-3", children: [
          /* @__PURE__ */ e.jsx("h3", { className: "text-lg font-semibold text-gray-900 dark:text-gray-100 truncate", children: t }),
          /* @__PURE__ */ e.jsxs("div", { className: "flex items-center gap-2", children: [
            o && n && /* @__PURE__ */ e.jsx(
              "button",
              {
                onClick: n,
                className: "p-1.5 rounded-lg hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors flex-shrink-0 sm:hidden",
                title: "Close sidebar",
                children: /* @__PURE__ */ e.jsx(
                  "svg",
                  {
                    className: "w-4 h-4 text-gray-600 dark:text-gray-400",
                    fill: "none",
                    stroke: "currentColor",
                    viewBox: "0 0 24 24",
                    children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M6 18L18 6M6 6l12 12" })
                  }
                )
              }
            ),
            s && /* @__PURE__ */ e.jsx(
              "button",
              {
                onClick: n,
                className: "p-1.5 rounded-lg hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors flex-shrink-0 hidden sm:block",
                title: i ? "Expand sidebar" : "Collapse sidebar",
                children: /* @__PURE__ */ e.jsx(
                  "svg",
                  {
                    className: _(
                      "w-4 h-4 text-gray-600 dark:text-gray-400 transition-transform duration-200",
                      { "rotate-180": i }
                    ),
                    fill: "none",
                    stroke: "currentColor",
                    viewBox: "0 0 24 24",
                    children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M15 19l-7-7 7-7" })
                  }
                )
              }
            )
          ] })
        ] }),
        d && !i && /* @__PURE__ */ e.jsxs("div", { className: "relative", children: [
          /* @__PURE__ */ e.jsxs("div", { className: _(
            "relative flex items-center transition-all duration-200",
            {
              "ring-2 ring-blue-500 dark:ring-blue-400": p
            }
          ), children: [
            /* @__PURE__ */ e.jsx("div", { className: "absolute left-3 pointer-events-none", children: /* @__PURE__ */ e.jsx("svg", { className: "w-4 h-4 text-gray-400 dark:text-gray-500", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" }) }) }),
            /* @__PURE__ */ e.jsx(
              "input",
              {
                type: "text",
                value: u,
                onChange: f,
                onFocus: () => h(!0),
                onBlur: () => h(!1),
                placeholder: "Search recommendations...",
                className: _(
                  "w-full pl-10 pr-10 py-2 text-sm bg-white dark:bg-slate-900 border border-gray-300 dark:border-slate-600 rounded-lg",
                  "placeholder-gray-400 dark:placeholder-gray-500 text-gray-900 dark:text-gray-100",
                  "focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent",
                  "transition-all duration-200"
                )
              }
            ),
            u && /* @__PURE__ */ e.jsx(
              "button",
              {
                onClick: y,
                className: "absolute right-3 p-0.5 rounded-full hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors",
                title: "Clear search",
                children: /* @__PURE__ */ e.jsx("svg", { className: "w-3 h-3 text-gray-400 dark:text-gray-500", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M6 18L18 6M6 6l12 12" }) })
              }
            )
          ] }),
          u && /* @__PURE__ */ e.jsx("div", { className: "mt-2 text-xs text-gray-500 dark:text-gray-400", children: "Search results will be filtered in real-time" })
        ] }),
        !i && /* @__PURE__ */ e.jsxs("div", { className: "mt-3 flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400", children: [
          /* @__PURE__ */ e.jsxs("div", { className: "flex items-center gap-1", children: [
            /* @__PURE__ */ e.jsx("div", { className: "w-2 h-2 bg-green-500 rounded-full" }),
            /* @__PURE__ */ e.jsx("span", { children: "Live recommendations" })
          ] }),
          /* @__PURE__ */ e.jsxs("div", { className: "flex items-center gap-1", children: [
            /* @__PURE__ */ e.jsx("svg", { className: "w-3 h-3", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M13 10V3L4 14h7v7l9-11h-7z" }) }),
            /* @__PURE__ */ e.jsx("span", { children: "AI-powered" })
          ] })
        ] })
      ]
    }
  );
}, Ve = ({
  recommendations: t,
  displayMode: r,
  theme: s,
  maxRecommendations: i,
  onRecommendationClick: n,
  className: a
}) => {
  const [d, c] = R(!1), [u, m] = R("all"), p = i ? t.slice(0, i) : t, o = (() => {
    switch (u) {
      case "top":
        return p.filter((g) => g.intent_match_score >= 0.8).slice(0, 5);
      case "recent":
        return p.slice(0, 3);
      default:
        return p;
    }
  })(), k = _(
    "admesh-sidebar-content",
    "flex flex-col h-full",
    a
  ), f = s != null && s.accentColor ? {
    "--admesh-primary": s.accentColor
  } : void 0, y = () => {
    if (o.length === 0)
      return /* @__PURE__ */ e.jsxs("div", { className: "flex-1 flex flex-col items-center justify-center p-6 text-center", children: [
        /* @__PURE__ */ e.jsx("div", { className: "w-16 h-16 bg-gray-100 dark:bg-slate-800 rounded-full flex items-center justify-center mb-4", children: /* @__PURE__ */ e.jsx("svg", { className: "w-8 h-8 text-gray-400 dark:text-gray-500", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" }) }) }),
        /* @__PURE__ */ e.jsx("h4", { className: "text-sm font-medium text-gray-900 dark:text-gray-100 mb-2", children: "No recommendations found" }),
        /* @__PURE__ */ e.jsx("p", { className: "text-xs text-gray-500 dark:text-gray-400", children: "Try adjusting your search or filters" })
      ] });
    switch (r) {
      case "recommendations":
        return /* @__PURE__ */ e.jsx("div", { className: "space-y-3", children: o.map((g, v) => /* @__PURE__ */ e.jsx(
          q,
          {
            recommendation: g,
            theme: s,
            compact: !0,
            showReason: !0,
            onClick: n
          },
          g.ad_id || v
        )) });
      case "history":
        return /* @__PURE__ */ e.jsx("div", { className: "space-y-2", children: o.map((g, v) => /* @__PURE__ */ e.jsxs("div", { className: "flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-800 transition-colors", children: [
          /* @__PURE__ */ e.jsx("div", { className: "w-2 h-2 bg-gray-400 rounded-full flex-shrink-0" }),
          /* @__PURE__ */ e.jsxs("div", { className: "flex-1 min-w-0", children: [
            /* @__PURE__ */ e.jsx("div", { className: "text-sm font-medium text-gray-900 dark:text-gray-100 truncate", children: g.title }),
            /* @__PURE__ */ e.jsxs("div", { className: "text-xs text-gray-500 dark:text-gray-400", children: [
              Math.round(g.intent_match_score * 100),
              "% match"
            ] })
          ] })
        ] }, g.ad_id || v)) });
      case "favorites":
        return /* @__PURE__ */ e.jsx("div", { className: "space-y-3", children: o.slice(0, 3).map((g, v) => /* @__PURE__ */ e.jsxs("div", { className: "relative", children: [
          /* @__PURE__ */ e.jsx(
            q,
            {
              recommendation: g,
              theme: s,
              compact: !0,
              showReason: !1,
              onClick: n
            }
          ),
          /* @__PURE__ */ e.jsx("button", { className: "absolute top-2 right-2 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors", children: /* @__PURE__ */ e.jsx("svg", { className: "w-3 h-3 text-yellow-500", fill: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { d: "M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" }) }) })
        ] }, g.ad_id || v)) });
      case "mixed":
        return /* @__PURE__ */ e.jsxs("div", { className: "space-y-4", children: [
          o[0] && /* @__PURE__ */ e.jsxs("div", { children: [
            /* @__PURE__ */ e.jsx("h4", { className: "text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2", children: "Top Pick" }),
            /* @__PURE__ */ e.jsx(
              pe,
              {
                recommendation: o[0],
                theme: s,
                showMatchScore: !0,
                showBadges: !0,
                onClick: n,
                className: "text-xs"
              }
            )
          ] }),
          o.slice(1).length > 0 && /* @__PURE__ */ e.jsxs("div", { children: [
            /* @__PURE__ */ e.jsx("h4", { className: "text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2", children: "More Options" }),
            /* @__PURE__ */ e.jsx("div", { className: "space-y-2", children: o.slice(1, 4).map((g, v) => /* @__PURE__ */ e.jsx(
              q,
              {
                recommendation: g,
                theme: s,
                compact: !0,
                showReason: !1,
                onClick: n
              },
              g.ad_id || v
            )) })
          ] })
        ] });
      default:
        return /* @__PURE__ */ e.jsx("div", { className: "space-y-3", children: o.map((g, v) => /* @__PURE__ */ e.jsx(
          q,
          {
            recommendation: g,
            theme: s,
            compact: !0,
            showReason: !0,
            onClick: n
          },
          g.ad_id || v
        )) });
    }
  };
  return /* @__PURE__ */ e.jsxs(
    "div",
    {
      className: k,
      style: f,
      "data-admesh-theme": s == null ? void 0 : s.mode,
      children: [
        /* @__PURE__ */ e.jsxs("div", { className: "flex border-b border-gray-200 dark:border-slate-700 bg-white dark:bg-slate-900", children: [
          /* @__PURE__ */ e.jsxs(
            "button",
            {
              onClick: () => m("all"),
              className: _(
                "flex-1 px-3 py-2 text-xs font-medium transition-colors",
                u === "all" ? "text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400" : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
              ),
              children: [
                "All (",
                t.length,
                ")"
              ]
            }
          ),
          /* @__PURE__ */ e.jsx(
            "button",
            {
              onClick: () => m("top"),
              className: _(
                "flex-1 px-3 py-2 text-xs font-medium transition-colors",
                u === "top" ? "text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400" : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
              ),
              children: "Top"
            }
          ),
          /* @__PURE__ */ e.jsx(
            "button",
            {
              onClick: () => m("recent"),
              className: _(
                "flex-1 px-3 py-2 text-xs font-medium transition-colors",
                u === "recent" ? "text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400" : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
              ),
              children: "Recent"
            }
          )
        ] }),
        /* @__PURE__ */ e.jsx("div", { className: "flex-1 overflow-y-auto p-4 min-h-0", style: {
          WebkitOverflowScrolling: "touch",
          // Smooth scrolling on iOS
          overscrollBehavior: "contain"
          // Prevent scroll chaining on mobile
        }, children: y() }),
        /* @__PURE__ */ e.jsx("div", { className: "p-3 border-t border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-800", children: /* @__PURE__ */ e.jsxs("div", { className: "flex items-center justify-between text-xs", children: [
          /* @__PURE__ */ e.jsxs("span", { className: "text-gray-500 dark:text-gray-400", children: [
            o.length,
            " recommendation",
            o.length !== 1 ? "s" : ""
          ] }),
          /* @__PURE__ */ e.jsx(
            "button",
            {
              onClick: () => c(!d),
              className: "text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors",
              children: "Filters"
            }
          )
        ] }) })
      ]
    }
  );
}, er = ({
  recommendations: t,
  config: r,
  theme: s,
  title: i = "Recommendations",
  isOpen: n = !0,
  onToggle: a,
  onRecommendationClick: d,
  onSearch: c,
  // onFilter,
  className: u,
  containerMode: m = !1
  // New prop for demo/container integration
}) => {
  const [p, h] = R(r.defaultCollapsed || !1), [o, k] = R(""), [f] = R({}), [y, g] = R(!1);
  W(() => {
    const w = () => {
      g(window.innerWidth < 640);
    };
    return w(), window.addEventListener("resize", w), () => window.removeEventListener("resize", w);
  }, []), W(() => {
    if (y && n && !p && !m) {
      const w = window.getComputedStyle(document.body).overflow;
      return document.body.style.overflow = "hidden", document.body.style.position = "fixed", document.body.style.width = "100%", () => {
        document.body.style.overflow = w, document.body.style.position = "", document.body.style.width = "";
      };
    }
  }, [y, n, p, m]), W(() => {
    if (r.autoRefresh && r.refreshInterval) {
      const w = setInterval(() => {
        console.log("Auto-refreshing recommendations...");
      }, r.refreshInterval);
      return () => clearInterval(w);
    }
  }, [r.autoRefresh, r.refreshInterval]);
  const v = te(() => {
    let w = [...t];
    if (o.trim()) {
      const S = o.toLowerCase();
      w = w.filter(
        (I) => {
          var z;
          return I.title.toLowerCase().includes(S) || I.reason.toLowerCase().includes(S) || ((z = I.keywords) == null ? void 0 : z.some((E) => E.toLowerCase().includes(S)));
        }
      );
    }
    return f.categories && f.categories.length > 0 && (w = w.filter(
      (S) => {
        var I;
        return (I = S.categories) == null ? void 0 : I.some((z) => {
          var E;
          return (E = f.categories) == null ? void 0 : E.includes(z);
        });
      }
    )), f.hasFreeTier, f.hasTrial && (w = w.filter((S) => S.trial_days && S.trial_days > 0)), f.minMatchScore !== void 0 && (w = w.filter((S) => S.intent_match_score >= f.minMatchScore)), w.sort((S, I) => I.intent_match_score - S.intent_match_score), r.maxRecommendations && (w = w.slice(0, r.maxRecommendations)), w;
  }, [t, o, f, r.maxRecommendations]), b = () => {
    r.collapsible && (h(!p), a == null || a());
  }, j = (w) => {
    k(w), c == null || c(w);
  }, C = _(
    "admesh-sidebar",
    "flex flex-col bg-white dark:bg-slate-900 border-gray-200 dark:border-slate-700 shadow-lg transition-all duration-300 ease-in-out",
    (() => {
      if (p) return "w-12";
      switch (r.size) {
        case "sm":
          return "w-full sm:w-64 max-w-[90vw] sm:max-w-sm";
        case "md":
          return "w-full sm:w-80 max-w-[90vw] sm:max-w-md";
        case "lg":
          return "w-full sm:w-96 max-w-[90vw] sm:max-w-lg";
        case "xl":
          return "w-full sm:w-[28rem] max-w-[90vw] sm:max-w-xl";
        default:
          return "w-full sm:w-80 max-w-[90vw] sm:max-w-md";
      }
    })(),
    {
      "border-r": r.position === "left",
      "border-l": r.position === "right",
      // Use fixed positioning for full-screen mode, relative for container mode
      // Improved mobile positioning with proper viewport handling
      "fixed top-0 bottom-0 z-[9999]": !m,
      "relative h-full": m,
      "left-0": r.position === "left" && !m,
      "right-0": r.position === "right" && !m,
      // Better mobile transform handling
      "transform -translate-x-full": r.position === "left" && !n && !m,
      "transform translate-x-full": r.position === "right" && !n && !m,
      // Mobile-specific improvements
      "min-h-0": !0,
      // Prevent height issues on mobile
      "overflow-hidden": !m
      // Prevent scroll issues
    },
    u
  ), T = s != null && s.accentColor ? {
    "--admesh-primary": s.accentColor
  } : void 0;
  return !n && !r.collapsible ? null : /* @__PURE__ */ e.jsxs(e.Fragment, { children: [
    n && !p && /* @__PURE__ */ e.jsx(
      "div",
      {
        className: _(
          "bg-black bg-opacity-50 z-[9998] sm:hidden transition-opacity duration-300",
          m ? "absolute inset-0" : "fixed inset-0"
        ),
        onClick: () => a == null ? void 0 : a(),
        style: {
          // Ensure overlay covers the entire viewport on mobile
          position: m ? "absolute" : "fixed",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          touchAction: "none"
          // Prevent scrolling behind overlay
        }
      }
    ),
    /* @__PURE__ */ e.jsxs(
      "div",
      {
        className: C,
        style: T,
        "data-admesh-theme": s == null ? void 0 : s.mode,
        "data-sidebar-position": r.position,
        "data-sidebar-size": r.size,
        "data-mobile-open": y && n && !p ? "true" : "false",
        "data-container-mode": m ? "true" : "false",
        children: [
          r.showHeader !== !1 && /* @__PURE__ */ e.jsx(
            De,
            {
              title: i,
              theme: s,
              collapsible: r.collapsible,
              isCollapsed: p,
              onToggle: b,
              onSearch: r.showSearch ? j : void 0,
              showSearch: r.showSearch && !p
            }
          ),
          !p && /* @__PURE__ */ e.jsx(
            Ve,
            {
              recommendations: v,
              displayMode: r.displayMode,
              theme: s,
              maxRecommendations: r.maxRecommendations,
              onRecommendationClick: d,
              className: "flex-1 overflow-hidden min-h-0"
            }
          ),
          p && r.collapsible && /* @__PURE__ */ e.jsxs("div", { className: "flex-1 flex flex-col items-center justify-center p-2", children: [
            /* @__PURE__ */ e.jsx(
              "button",
              {
                onClick: b,
                className: "p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-800 transition-colors",
                title: "Expand sidebar",
                children: /* @__PURE__ */ e.jsx("svg", { className: "w-5 h-5 text-gray-600 dark:text-gray-400", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M9 5l7 7-7 7" }) })
              }
            ),
            /* @__PURE__ */ e.jsx("div", { className: "mt-4 text-xs text-gray-500 dark:text-gray-400 transform -rotate-90 whitespace-nowrap", children: v.length })
          ] }),
          !p && /* @__PURE__ */ e.jsx("div", { className: "p-3 border-t border-gray-200 dark:border-slate-700", children: /* @__PURE__ */ e.jsx("div", { className: "text-xs text-gray-400 dark:text-gray-500 text-center", children: "Powered by AdMesh" }) })
        ]
      }
    )
  ] });
}, rr = ({
  recommendations: t,
  trigger: r,
  theme: s,
  title: i = "AI Recommendations",
  position: n = "bottom-right",
  size: a = "md",
  autoShow: d = !0,
  showDelay: c = 1e3,
  onRecommendationClick: u,
  onDismiss: m,
  className: p
}) => {
  const [h, o] = R(!1), [k, f] = R(!1);
  W(() => {
    if (d && t.length > 0) {
      const M = setTimeout(() => {
        o(!0), f(!0);
      }, c);
      return () => clearTimeout(M);
    }
  }, [d, t.length, c]);
  const y = () => {
    o(!1), m == null || m();
  }, g = () => {
    switch (a) {
      case "sm":
        return "w-72 max-h-80";
      case "md":
        return "w-80 max-h-96";
      case "lg":
        return "w-96 max-h-[28rem]";
      default:
        return "w-80 max-h-96";
    }
  }, v = () => {
    switch (n) {
      case "bottom-right":
        return "bottom-4 right-4";
      case "bottom-left":
        return "bottom-4 left-4";
      case "top-right":
        return "top-4 right-4";
      case "top-left":
        return "top-4 left-4";
      default:
        return "bottom-4 right-4";
    }
  };
  if (!h || t.length === 0)
    return null;
  const b = _(
    "admesh-auto-recommendation-widget",
    "fixed z-50 transition-all duration-500 ease-out",
    v(),
    g(),
    {
      "opacity-0 scale-95 translate-y-2": !k,
      "opacity-100 scale-100 translate-y-0": k
    },
    p
  ), j = s != null && s.accentColor ? {
    "--admesh-primary": s.accentColor
  } : void 0;
  return /* @__PURE__ */ e.jsx(
    "div",
    {
      className: b,
      style: j,
      "data-admesh-theme": s == null ? void 0 : s.mode,
      children: /* @__PURE__ */ e.jsxs("div", { className: "bg-white dark:bg-slate-900 rounded-lg shadow-2xl border border-gray-200 dark:border-slate-700 overflow-hidden", children: [
        /* @__PURE__ */ e.jsxs("div", { className: "flex items-center justify-between p-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white", children: [
          /* @__PURE__ */ e.jsxs("div", { className: "flex items-center gap-3", children: [
            /* @__PURE__ */ e.jsx("div", { className: "w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center", children: /* @__PURE__ */ e.jsx("svg", { className: "w-4 h-4", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M13 10V3L4 14h7v7l9-11h-7z" }) }) }),
            /* @__PURE__ */ e.jsxs("div", { children: [
              /* @__PURE__ */ e.jsx("h3", { className: "font-semibold text-sm", children: i }),
              r && /* @__PURE__ */ e.jsxs("p", { className: "text-xs text-blue-100 truncate max-w-48", children: [
                'Based on: "',
                r,
                '"'
              ] })
            ] })
          ] }),
          /* @__PURE__ */ e.jsx(
            "button",
            {
              onClick: y,
              className: "p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors",
              "aria-label": "Dismiss recommendations",
              children: /* @__PURE__ */ e.jsx("svg", { className: "w-4 h-4", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ e.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M6 18L18 6M6 6l12 12" }) })
            }
          )
        ] }),
        /* @__PURE__ */ e.jsxs("div", { className: "p-4 max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600", children: [
          /* @__PURE__ */ e.jsxs("div", { className: "flex items-center gap-2 mb-3", children: [
            /* @__PURE__ */ e.jsx("div", { className: "w-2 h-2 bg-green-500 rounded-full animate-pulse" }),
            /* @__PURE__ */ e.jsxs("span", { className: "text-sm font-medium text-gray-700 dark:text-gray-300", children: [
              t.length,
              " intelligent match",
              t.length > 1 ? "es" : "",
              " found"
            ] })
          ] }),
          /* @__PURE__ */ e.jsx(
            ke,
            {
              recommendations: t,
              config: {
                displayMode: "inline",
                context: "assistant",
                maxRecommendations: 3,
                showPoweredBy: !1,
                autoShow: !0,
                delayMs: 200
              },
              theme: s,
              onRecommendationClick: u
            }
          )
        ] }),
        /* @__PURE__ */ e.jsx("div", { className: "px-4 py-3 bg-gray-50 dark:bg-slate-800 border-t border-gray-200 dark:border-slate-700", children: /* @__PURE__ */ e.jsxs("div", { className: "flex items-center justify-between", children: [
          /* @__PURE__ */ e.jsx("span", { className: "text-xs text-gray-500 dark:text-gray-400", children: "Powered by AdMesh" }),
          /* @__PURE__ */ e.jsx(
            "button",
            {
              onClick: y,
              className: "text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors",
              children: "Dismiss"
            }
          )
        ] }) })
      ] })
    }
  );
}, J = (t = {}) => {
  const r = {
    mode: "light",
    primaryColor: "#3b82f6",
    secondaryColor: "#10b981",
    accentColor: "#3b82f6",
    backgroundColor: "#ffffff",
    surfaceColor: "#f9fafb",
    borderColor: "#e5e7eb",
    textColor: "#111827",
    textSecondaryColor: "#6b7280",
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    fontSize: {
      small: "12px",
      base: "14px",
      large: "16px",
      title: "18px"
    },
    borderRadius: "8px",
    spacing: {
      small: "4px",
      medium: "8px",
      large: "16px"
    },
    shadows: {
      small: "0 1px 3px rgba(0, 0, 0, 0.1)",
      medium: "0 4px 6px rgba(0, 0, 0, 0.1)",
      large: "0 10px 15px rgba(0, 0, 0, 0.1)"
    },
    icons: {
      expandIcon: "▼",
      collapseIcon: "▲",
      starIcon: "★",
      checkIcon: "✓",
      arrowIcon: "→"
    }
  };
  return {
    ...r,
    ...t,
    fontSize: {
      ...r.fontSize,
      ...t.fontSize
    },
    spacing: {
      ...r.spacing,
      ...t.spacing
    },
    shadows: {
      ...r.shadows,
      ...t.shadows
    },
    icons: {
      ...r.icons,
      ...t.icons
    },
    components: {
      ...r.components,
      ...t.components
    }
  };
}, tr = (t = {}) => J({
  ...{
    mode: "dark",
    backgroundColor: "#1f2937",
    surfaceColor: "#374151",
    borderColor: "#4b5563",
    textColor: "#f9fafb",
    textSecondaryColor: "#9ca3af",
    shadows: {
      small: "0 1px 3px rgba(0, 0, 0, 0.3)",
      medium: "0 4px 6px rgba(0, 0, 0, 0.3)",
      large: "0 10px 15px rgba(0, 0, 0, 0.3)"
    }
  },
  ...t
}), sr = {
  // Clean, minimal theme
  minimal: J({
    primaryColor: "#000000",
    secondaryColor: "#666666",
    borderRadius: "4px",
    shadows: {
      small: "none",
      medium: "0 1px 3px rgba(0, 0, 0, 0.1)",
      large: "0 2px 6px rgba(0, 0, 0, 0.1)"
    }
  }),
  // Modern, colorful theme
  vibrant: J({
    primaryColor: "#8b5cf6",
    secondaryColor: "#06b6d4",
    accentColor: "#f59e0b",
    borderRadius: "12px",
    gradients: {
      primary: "linear-gradient(135deg, #8b5cf6 0%, #06b6d4 100%)",
      secondary: "linear-gradient(135deg, #06b6d4 0%, #10b981 100%)",
      accent: "linear-gradient(135deg, #f59e0b 0%, #ef4444 100%)"
    }
  }),
  // Professional, corporate theme
  corporate: J({
    primaryColor: "#1e40af",
    secondaryColor: "#059669",
    backgroundColor: "#f8fafc",
    surfaceColor: "#ffffff",
    borderColor: "#cbd5e1",
    borderRadius: "6px",
    fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, sans-serif'
  }),
  // High contrast theme for accessibility
  highContrast: J({
    primaryColor: "#000000",
    secondaryColor: "#ffffff",
    backgroundColor: "#ffffff",
    surfaceColor: "#f5f5f5",
    borderColor: "#000000",
    textColor: "#000000",
    textSecondaryColor: "#333333",
    borderRadius: "0px",
    shadows: {
      small: "none",
      medium: "0 0 0 2px #000000",
      large: "0 0 0 3px #000000"
    }
  })
}, ar = (...t) => {
  const r = J();
  return t.reduce((s, i) => i ? J({
    ...s,
    ...i
  }) : s, r);
}, or = (t) => {
  var s, i, n, a, d, c;
  const r = getComputedStyle(t);
  return {
    primaryColor: ((s = r.getPropertyValue("--admesh-primary-color")) == null ? void 0 : s.trim()) || void 0,
    secondaryColor: ((i = r.getPropertyValue("--admesh-secondary-color")) == null ? void 0 : i.trim()) || void 0,
    backgroundColor: ((n = r.getPropertyValue("--admesh-background-color")) == null ? void 0 : n.trim()) || void 0,
    textColor: ((a = r.getPropertyValue("--admesh-text-color")) == null ? void 0 : a.trim()) || void 0,
    borderRadius: ((d = r.getPropertyValue("--admesh-border-radius")) == null ? void 0 : d.trim()) || void 0,
    fontFamily: ((c = r.getPropertyValue("--admesh-font-family")) == null ? void 0 : c.trim()) || void 0
  };
}, nr = "0.2.1", lr = {
  trackingEnabled: !0,
  debug: !1,
  theme: {
    mode: "light",
    accentColor: "#2563eb"
  }
};
export {
  rr as AdMeshAutoRecommendationWidget,
  Pe as AdMeshBadge,
  Oe as AdMeshChatInput,
  $e as AdMeshChatInterface,
  Fe as AdMeshChatMessage,
  ye as AdMeshCitationReference,
  We as AdMeshCitationUnit,
  Ie as AdMeshCompareTable,
  Be as AdMeshConversationSummary,
  ke as AdMeshConversationalUnit,
  Xe as AdMeshExpandableUnit,
  Ze as AdMeshFloatingChat,
  q as AdMeshInlineRecommendation,
  D as AdMeshLinkTracker,
  pe as AdMeshProductCard,
  er as AdMeshSidebar,
  Ve as AdMeshSidebarContent,
  De as AdMeshSidebarHeader,
  lr as DEFAULT_CONFIG,
  nr as VERSION,
  Ye as buildAdMeshLink,
  J as createAdMeshTheme,
  tr as createDarkTheme,
  He as extractTrackingData,
  Ae as getBadgeText,
  Ke as getCtaText,
  xe as getInlineDisclosure,
  he as getInlineTooltip,
  ae as getLabelTooltip,
  Qe as getPoweredByText,
  K as getRecommendationLabel,
  Ge as getSectionDisclosure,
  Je as hasHighQualityMatches,
  ar as mergeThemes,
  qe as setAdMeshTrackerConfig,
  or as themeFromCSSProperties,
  sr as themePresets,
  me as useAdMeshStyles,
  Te as useAdMeshTracker
};
//# sourceMappingURL=index.mjs.map
