'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Search, Loader2, ExternalLink } from 'lucide-react';
import CitationRenderer from '@/components/CitationRenderer';
import ThemeToggle from '@/components/ThemeToggle';
import SearchHistory from '@/components/SearchHistory';
import { AdMeshCitationUnit, AdMeshInlineRecommendation, AdMeshProductCard } from 'admesh-ui-sdk';

interface Source {
  id: number;
  title: string;
  url: string;
  snippet: string;
  favicon?: string;
}



interface SearchResponse {
  query: string;
  aiResponse: string;
  sources: Source[];
}

export default function Home() {
  const [query, setQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [response, setResponse] = useState<SearchResponse | null>(null);
  const [streamedContent, setStreamedContent] = useState('');
  const [sources, setSources] = useState<Source[]>([]);
  const [recommendations, setRecommendations] = useState<any>(null);
  const [status, setStatus] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!query.trim() || isSearching) return;

    setIsSearching(true);
    setResponse(null);
    setStreamedContent('');
    setSources([]);
    setRecommendations(null);
    setStatus('');

    try {
      const response = await fetch('/api/search/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query }),
      });

      if (!response.body) throw new Error('No response body');

      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));

              switch (data.type) {
                case 'status':
                  setStatus(data.message);
                  break;
                case 'sources':
                  setSources(data.data);
                  break;
                case 'recommendations':
                  setRecommendations(data.data);
                  console.log('🔍 Full AdMesh API Response:', data.data);
                  console.log('🔍 Recommendations Array:', data.data?.response?.recommendations);
                  if (data.data?.response?.recommendations?.[0]) {
                    console.log('🔍 First Recommendation Data:', data.data.response.recommendations[0]);
                    console.log('🔍 Available Fields:', Object.keys(data.data.response.recommendations[0]));
                  }
                  break;
                case 'content':
                  setStreamedContent(prev => prev + data.data);
                  break;
                case 'complete':
                  setStatus('');
                  break;
                case 'error':
                  console.error('Stream error:', data.message);
                  break;
              }
            } catch (e) {
              // Ignore parsing errors for incomplete chunks
            }
          }
        }
      }
    } catch (error) {
      console.error('Search error:', error);
      setStatus('An error occurred while searching');
    } finally {
      setIsSearching(false);
    }
  };

  return (
    <div className="min-h-screen bg-white dark:bg-slate-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex justify-between items-start mb-8">
          <div className="flex-1 text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-4 text-gray-900 dark:text-white">
              AI Search Engine
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Get comprehensive answers powered by AI and real-time web search
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <ThemeToggle />
          </div>
        </div>

        {/* Search Form */}
        <div className="max-w-4xl mx-auto mb-8">
          <form onSubmit={handleSearch}>
            <div className="relative">
              <input
                ref={inputRef}
                type="text"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="Ask anything..."
                className="w-full px-6 py-4 pr-16 text-lg border border-gray-300 rounded-2xl shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-slate-800 dark:border-slate-600 dark:text-white"
                disabled={isSearching}
              />
              <button
                type="submit"
                disabled={isSearching || !query.trim()}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 p-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isSearching ? (
                  <Loader2 className="w-5 h-5 animate-spin" />
                ) : (
                  <Search className="w-5 h-5" />
                )}
              </button>
            </div>
          </form>

          {/* Search History */}
          <div className="mt-4 flex justify-center">
            <SearchHistory
              onSelectQuery={setQuery}
              currentQuery={query}
            />
          </div>
        </div>

        {/* Status */}
        {status && (
          <div className="max-w-4xl mx-auto mb-6">
            <div className="flex items-center justify-center space-x-2 text-blue-600 dark:text-blue-400">
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>{status}</span>
            </div>
          </div>
        )}

        {/* Results */}
        {(streamedContent || sources.length > 0 || recommendations) && (
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* AI Response */}
              <div className="lg:col-span-1">
                <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-lg p-6">
                  <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                    AI Response
                  </h2>
                  <div className="prose dark:prose-invert max-w-none">
                    {streamedContent ? (
                      <CitationRenderer content={streamedContent} sources={sources} />
                    ) : (
                      <div className="text-gray-500 dark:text-gray-400">
                        Waiting for response...
                      </div>
                    )}
                    {isSearching && (
                      <span className="inline-block w-2 h-5 bg-blue-600 animate-pulse ml-1"></span>
                    )}
                  </div>
                </div>

                {/* AdMesh Recommendations */}
                {recommendations && recommendations.response?.recommendations?.[0] && (
                  <div className="my-6">
                    <h3 className="text-lg font-semibold mb-4 text-gray-800 dark:text-white">
                      Recommended Solutions
                    </h3>
                    <div style={{
                      isolation: 'isolate',
                      position: 'relative',
                      zIndex: 1
                    }}>
                      <AdMeshProductCard
                        recommendation={recommendations.response.recommendations[0]}
                        variation="default"
                        showMatchScore={false}
                        showBadges={true}
                        theme={{
                          mode: 'light',
                          borderRadius: '16px',
                          accentColor: '#8b5cf6',
                          primaryColor: '#8b5cf6',
                          backgroundColor: '#ffffff',
                          surfaceColor: '#f8fafc',
                          borderColor: '#e2e8f0',
                          textColor: '#1e293b',
                          textSecondaryColor: '#64748b',
                          shadows: {
                            small: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
                            medium: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
                            large: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)'
                          },
                          spacing: {
                            small: '8px',
                            medium: '16px',
                            large: '24px'
                          }
                        }}
                        style={{
                          maxWidth: '100%',
                          margin: '0 auto'
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Sources */}
              {sources.length > 0 && (
                <div className="lg:col-span-1">
                  <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-lg p-6">
                    <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                      Sources
                    </h2>
                    <div className="space-y-4">
                      {sources.map((source) => (
                        <div
                          key={source.id}
                          className="border border-gray-200 dark:border-slate-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors"
                        >
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex items-center space-x-2">
                              <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                                [{source.id}]
                              </span>
                              {source.favicon && (
                                <img
                                  src={source.favicon}
                                  alt=""
                                  className="w-4 h-4"
                                  onError={(e) => {
                                    e.currentTarget.style.display = 'none';
                                  }}
                                />
                              )}
                            </div>
                            <a
                              href={source.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                            >
                              <ExternalLink className="w-4 h-4" />
                            </a>
                          </div>
                          <h3 className="font-medium text-gray-800 dark:text-white mb-2 line-clamp-2">
                            {source.title}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-3 mb-2">
                            {source.snippet}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-500 truncate">
                            {new URL(source.url).hostname}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* AdMesh Product Card Variations */}
              {recommendations && recommendations.response?.recommendations && (
                <div className="lg:col-span-1">
                  <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-lg p-6">
                    <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
                      Product Recommendations
                    </h2>
                    <div className="space-y-6">
                      {/* Default Variation */}
                      {recommendations.response.recommendations[0] && (
                        <div>
                          <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">Default Card</h3>
                          <AdMeshProductCard
                            recommendation={recommendations.response.recommendations[0]}
                            variation="default"
                            showMatchScore={false}
                            showBadges={true}
                            theme={{
                              mode: 'light',
                              borderRadius: '16px',
                              accentColor: '#8b5cf6',
                              primaryColor: '#8b5cf6',
                              backgroundColor: '#ffffff',
                              surfaceColor: '#f8fafc',
                              borderColor: '#e2e8f0',
                              textColor: '#1e293b',
                              textSecondaryColor: '#64748b',
                              shadows: {
                                small: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
                                medium: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
                                large: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)'
                              },
                              spacing: {
                                small: '8px',
                                medium: '16px',
                                large: '24px'
                              }
                            }}
                          />
                        </div>
                        
                      )}

                      {/* Statement Variation */}
                      {recommendations.response.recommendations[0] && (
                        <div>
                          <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">Statement Style</h3>
                          <AdMeshProductCard
                            recommendation={recommendations.response.recommendations[0]}
                            variation="statement"
                            showMatchScore={true}
                            showBadges={true}
                            theme={{
                              mode: 'light',
                              borderRadius: '16px',
                              accentColor: '#8b5cf6',
                              primaryColor: '#8b5cf6',
                              backgroundColor: '#ffffff',
                              surfaceColor: '#f8fafc',
                              borderColor: '#e2e8f0',
                              textColor: '#1e293b',
                              textSecondaryColor: '#64748b',
                              shadows: {
                                small: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
                                medium: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
                                large: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)'
                              },
                              spacing: {
                                small: '8px',
                                medium: '16px',
                                large: '24px'
                              }
                            }}
                          />
                        </div>
                      )}

                      {/* Question Variation */}
                      {recommendations.response.recommendations[0] && (
                        <div>
                          <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">Question Style</h3>
                          <AdMeshProductCard
                            recommendation={recommendations.response.recommendations[0]}
                            variation="question"
                            showMatchScore={true}
                            showBadges={true}
                            theme={{
                              mode: 'light',
                              borderRadius: '16px',
                              accentColor: '#8b5cf6',
                              primaryColor: '#8b5cf6',
                              backgroundColor: '#ffffff',
                              surfaceColor: '#f8fafc',
                              borderColor: '#e2e8f0',
                              textColor: '#1e293b',
                              textSecondaryColor: '#64748b',
                              shadows: {
                                small: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
                                medium: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
                                large: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)'
                              },
                              spacing: {
                                small: '8px',
                                medium: '16px',
                                large: '24px'
                              }
                            }}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Example Queries */}
        {!streamedContent && !isSearching && (
          <div className="max-w-4xl mx-auto mt-12">
            <h3 className="text-lg font-semibold text-center mb-6 text-gray-700 dark:text-gray-300">
              Try asking about:
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[
                "What's the latest news in AI?",
                "How does quantum computing work?",
                "Best practices for React development",
                "Climate change recent developments",
                "Space exploration missions 2024",
                "Cryptocurrency market trends"
              ].map((example, index) => (
                <button
                  key={index}
                  onClick={() => setQuery(example)}
                  className="p-4 text-left bg-white dark:bg-slate-800 rounded-lg shadow hover:shadow-md transition-shadow border border-gray-200 dark:border-slate-600"
                >
                  <span className="text-gray-700 dark:text-gray-300">{example}</span>
                </button>
              ))}
            </div>
          </div>
        )}

       
       
      </div>
    </div>
  );
}
